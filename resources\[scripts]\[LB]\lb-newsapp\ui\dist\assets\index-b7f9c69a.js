var Ng=Object.defineProperty;var Mg=(e,t,n)=>t in e?Ng(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n;var Tn=(e,t,n)=>(Mg(e,typeof t!="symbol"?t+"":t,n),n);(function(){const t=document.createElement("link").relList;if(t&&t.supports&&t.supports("modulepreload"))return;for(const i of document.querySelectorAll('link[rel="modulepreload"]'))r(i);new MutationObserver(i=>{for(const o of i)if(o.type==="childList")for(const s of o.addedNodes)s.tagName==="LINK"&&s.rel==="modulepreload"&&r(s)}).observe(document,{childList:!0,subtree:!0});function n(i){const o={};return i.integrity&&(o.integrity=i.integrity),i.referrerPolicy&&(o.referrerPolicy=i.referrerPolicy),i.crossOrigin==="use-credentials"?o.credentials="include":i.crossOrigin==="anonymous"?o.credentials="omit":o.credentials="same-origin",o}function r(i){if(i.ep)return;i.ep=!0;const o=n(i);fetch(i.href,o)}})();function Dg(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}var Rd={exports:{}},Jo={},_d={exports:{}},F={};/**
 * @license React
 * react.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var yi=Symbol.for("react.element"),Lg=Symbol.for("react.portal"),Rg=Symbol.for("react.fragment"),_g=Symbol.for("react.strict_mode"),Vg=Symbol.for("react.profiler"),Og=Symbol.for("react.provider"),Ig=Symbol.for("react.context"),Fg=Symbol.for("react.forward_ref"),jg=Symbol.for("react.suspense"),zg=Symbol.for("react.memo"),Bg=Symbol.for("react.lazy"),bu=Symbol.iterator;function Ug(e){return e===null||typeof e!="object"?null:(e=bu&&e[bu]||e["@@iterator"],typeof e=="function"?e:null)}var Vd={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},Od=Object.assign,Id={};function fr(e,t,n){this.props=e,this.context=t,this.refs=Id,this.updater=n||Vd}fr.prototype.isReactComponent={};fr.prototype.setState=function(e,t){if(typeof e!="object"&&typeof e!="function"&&e!=null)throw Error("setState(...): takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,e,t,"setState")};fr.prototype.forceUpdate=function(e){this.updater.enqueueForceUpdate(this,e,"forceUpdate")};function Fd(){}Fd.prototype=fr.prototype;function ya(e,t,n){this.props=e,this.context=t,this.refs=Id,this.updater=n||Vd}var wa=ya.prototype=new Fd;wa.constructor=ya;Od(wa,fr.prototype);wa.isPureReactComponent=!0;var Zu=Array.isArray,jd=Object.prototype.hasOwnProperty,Sa={current:null},zd={key:!0,ref:!0,__self:!0,__source:!0};function Bd(e,t,n){var r,i={},o=null,s=null;if(t!=null)for(r in t.ref!==void 0&&(s=t.ref),t.key!==void 0&&(o=""+t.key),t)jd.call(t,r)&&!zd.hasOwnProperty(r)&&(i[r]=t[r]);var l=arguments.length-2;if(l===1)i.children=n;else if(1<l){for(var a=Array(l),u=0;u<l;u++)a[u]=arguments[u+2];i.children=a}if(e&&e.defaultProps)for(r in l=e.defaultProps,l)i[r]===void 0&&(i[r]=l[r]);return{$$typeof:yi,type:e,key:o,ref:s,props:i,_owner:Sa.current}}function $g(e,t){return{$$typeof:yi,type:e.type,key:t,ref:e.ref,props:e.props,_owner:e._owner}}function xa(e){return typeof e=="object"&&e!==null&&e.$$typeof===yi}function Hg(e){var t={"=":"=0",":":"=2"};return"$"+e.replace(/[=:]/g,function(n){return t[n]})}var qu=/\/+/g;function Cs(e,t){return typeof e=="object"&&e!==null&&e.key!=null?Hg(""+e.key):t.toString(36)}function eo(e,t,n,r,i){var o=typeof e;(o==="undefined"||o==="boolean")&&(e=null);var s=!1;if(e===null)s=!0;else switch(o){case"string":case"number":s=!0;break;case"object":switch(e.$$typeof){case yi:case Lg:s=!0}}if(s)return s=e,i=i(s),e=r===""?"."+Cs(s,0):r,Zu(i)?(n="",e!=null&&(n=e.replace(qu,"$&/")+"/"),eo(i,t,n,"",function(u){return u})):i!=null&&(xa(i)&&(i=$g(i,n+(!i.key||s&&s.key===i.key?"":(""+i.key).replace(qu,"$&/")+"/")+e)),t.push(i)),1;if(s=0,r=r===""?".":r+":",Zu(e))for(var l=0;l<e.length;l++){o=e[l];var a=r+Cs(o,l);s+=eo(o,t,n,a,i)}else if(a=Ug(e),typeof a=="function")for(e=a.call(e),l=0;!(o=e.next()).done;)o=o.value,a=r+Cs(o,l++),s+=eo(o,t,n,a,i);else if(o==="object")throw t=String(e),Error("Objects are not valid as a React child (found: "+(t==="[object Object]"?"object with keys {"+Object.keys(e).join(", ")+"}":t)+"). If you meant to render a collection of children, use an array instead.");return s}function Ri(e,t,n){if(e==null)return e;var r=[],i=0;return eo(e,r,"","",function(o){return t.call(n,o,i++)}),r}function Wg(e){if(e._status===-1){var t=e._result;t=t(),t.then(function(n){(e._status===0||e._status===-1)&&(e._status=1,e._result=n)},function(n){(e._status===0||e._status===-1)&&(e._status=2,e._result=n)}),e._status===-1&&(e._status=0,e._result=t)}if(e._status===1)return e._result.default;throw e._result}var Te={current:null},to={transition:null},Kg={ReactCurrentDispatcher:Te,ReactCurrentBatchConfig:to,ReactCurrentOwner:Sa};function Ud(){throw Error("act(...) is not supported in production builds of React.")}F.Children={map:Ri,forEach:function(e,t,n){Ri(e,function(){t.apply(this,arguments)},n)},count:function(e){var t=0;return Ri(e,function(){t++}),t},toArray:function(e){return Ri(e,function(t){return t})||[]},only:function(e){if(!xa(e))throw Error("React.Children.only expected to receive a single React element child.");return e}};F.Component=fr;F.Fragment=Rg;F.Profiler=Vg;F.PureComponent=ya;F.StrictMode=_g;F.Suspense=jg;F.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=Kg;F.act=Ud;F.cloneElement=function(e,t,n){if(e==null)throw Error("React.cloneElement(...): The argument must be a React element, but you passed "+e+".");var r=Od({},e.props),i=e.key,o=e.ref,s=e._owner;if(t!=null){if(t.ref!==void 0&&(o=t.ref,s=Sa.current),t.key!==void 0&&(i=""+t.key),e.type&&e.type.defaultProps)var l=e.type.defaultProps;for(a in t)jd.call(t,a)&&!zd.hasOwnProperty(a)&&(r[a]=t[a]===void 0&&l!==void 0?l[a]:t[a])}var a=arguments.length-2;if(a===1)r.children=n;else if(1<a){l=Array(a);for(var u=0;u<a;u++)l[u]=arguments[u+2];r.children=l}return{$$typeof:yi,type:e.type,key:i,ref:o,props:r,_owner:s}};F.createContext=function(e){return e={$$typeof:Ig,_currentValue:e,_currentValue2:e,_threadCount:0,Provider:null,Consumer:null,_defaultValue:null,_globalName:null},e.Provider={$$typeof:Og,_context:e},e.Consumer=e};F.createElement=Bd;F.createFactory=function(e){var t=Bd.bind(null,e);return t.type=e,t};F.createRef=function(){return{current:null}};F.forwardRef=function(e){return{$$typeof:Fg,render:e}};F.isValidElement=xa;F.lazy=function(e){return{$$typeof:Bg,_payload:{_status:-1,_result:e},_init:Wg}};F.memo=function(e,t){return{$$typeof:zg,type:e,compare:t===void 0?null:t}};F.startTransition=function(e){var t=to.transition;to.transition={};try{e()}finally{to.transition=t}};F.unstable_act=Ud;F.useCallback=function(e,t){return Te.current.useCallback(e,t)};F.useContext=function(e){return Te.current.useContext(e)};F.useDebugValue=function(){};F.useDeferredValue=function(e){return Te.current.useDeferredValue(e)};F.useEffect=function(e,t){return Te.current.useEffect(e,t)};F.useId=function(){return Te.current.useId()};F.useImperativeHandle=function(e,t,n){return Te.current.useImperativeHandle(e,t,n)};F.useInsertionEffect=function(e,t){return Te.current.useInsertionEffect(e,t)};F.useLayoutEffect=function(e,t){return Te.current.useLayoutEffect(e,t)};F.useMemo=function(e,t){return Te.current.useMemo(e,t)};F.useReducer=function(e,t,n){return Te.current.useReducer(e,t,n)};F.useRef=function(e){return Te.current.useRef(e)};F.useState=function(e){return Te.current.useState(e)};F.useSyncExternalStore=function(e,t,n){return Te.current.useSyncExternalStore(e,t,n)};F.useTransition=function(){return Te.current.useTransition()};F.version="18.3.1";_d.exports=F;var E=_d.exports;const fn=Dg(E);/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Gg=E,Qg=Symbol.for("react.element"),Xg=Symbol.for("react.fragment"),Yg=Object.prototype.hasOwnProperty,bg=Gg.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,Zg={key:!0,ref:!0,__self:!0,__source:!0};function $d(e,t,n){var r,i={},o=null,s=null;n!==void 0&&(o=""+n),t.key!==void 0&&(o=""+t.key),t.ref!==void 0&&(s=t.ref);for(r in t)Yg.call(t,r)&&!Zg.hasOwnProperty(r)&&(i[r]=t[r]);if(e&&e.defaultProps)for(r in t=e.defaultProps,t)i[r]===void 0&&(i[r]=t[r]);return{$$typeof:Qg,type:e,key:o,ref:s,props:i,_owner:bg.current}}Jo.Fragment=Xg;Jo.jsx=$d;Jo.jsxs=$d;Rd.exports=Jo;var Ea=Rd.exports;const Qe=Ea.Fragment,y=Ea.jsx,L=Ea.jsxs;var al={},Hd={exports:{}},ze={},Wd={exports:{}},Kd={};/**
 * @license React
 * scheduler.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */(function(e){function t(N,V){var O=N.length;N.push(V);e:for(;0<O;){var ee=O-1>>>1,ue=N[ee];if(0<i(ue,V))N[ee]=V,N[O]=ue,O=ee;else break e}}function n(N){return N.length===0?null:N[0]}function r(N){if(N.length===0)return null;var V=N[0],O=N.pop();if(O!==V){N[0]=O;e:for(var ee=0,ue=N.length,Di=ue>>>1;ee<Di;){var Jt=2*(ee+1)-1,Es=N[Jt],en=Jt+1,Li=N[en];if(0>i(Es,O))en<ue&&0>i(Li,Es)?(N[ee]=Li,N[en]=O,ee=en):(N[ee]=Es,N[Jt]=O,ee=Jt);else if(en<ue&&0>i(Li,O))N[ee]=Li,N[en]=O,ee=en;else break e}}return V}function i(N,V){var O=N.sortIndex-V.sortIndex;return O!==0?O:N.id-V.id}if(typeof performance=="object"&&typeof performance.now=="function"){var o=performance;e.unstable_now=function(){return o.now()}}else{var s=Date,l=s.now();e.unstable_now=function(){return s.now()-l}}var a=[],u=[],c=1,d=null,f=3,m=!1,v=!1,w=!1,C=typeof setTimeout=="function"?setTimeout:null,p=typeof clearTimeout=="function"?clearTimeout:null,h=typeof setImmediate<"u"?setImmediate:null;typeof navigator<"u"&&navigator.scheduling!==void 0&&navigator.scheduling.isInputPending!==void 0&&navigator.scheduling.isInputPending.bind(navigator.scheduling);function g(N){for(var V=n(u);V!==null;){if(V.callback===null)r(u);else if(V.startTime<=N)r(u),V.sortIndex=V.expirationTime,t(a,V);else break;V=n(u)}}function S(N){if(w=!1,g(N),!v)if(n(a)!==null)v=!0,Mi(x);else{var V=n(u);V!==null&&oe(S,V.startTime-N)}}function x(N,V){v=!1,w&&(w=!1,p(P),P=-1),m=!0;var O=f;try{for(g(V),d=n(a);d!==null&&(!(d.expirationTime>V)||N&&!ae());){var ee=d.callback;if(typeof ee=="function"){d.callback=null,f=d.priorityLevel;var ue=ee(d.expirationTime<=V);V=e.unstable_now(),typeof ue=="function"?d.callback=ue:d===n(a)&&r(a),g(V)}else r(a);d=n(a)}if(d!==null)var Di=!0;else{var Jt=n(u);Jt!==null&&oe(S,Jt.startTime-V),Di=!1}return Di}finally{d=null,f=O,m=!1}}var k=!1,A=null,P=-1,I=5,_=-1;function ae(){return!(e.unstable_now()-_<I)}function At(){if(A!==null){var N=e.unstable_now();_=N;var V=!0;try{V=A(!0,N)}finally{V?qt():(k=!1,A=null)}}else k=!1}var qt;if(typeof h=="function")qt=function(){h(At)};else if(typeof MessageChannel<"u"){var vr=new MessageChannel,Yu=vr.port2;vr.port1.onmessage=At,qt=function(){Yu.postMessage(null)}}else qt=function(){C(At,0)};function Mi(N){A=N,k||(k=!0,qt())}function oe(N,V){P=C(function(){N(e.unstable_now())},V)}e.unstable_IdlePriority=5,e.unstable_ImmediatePriority=1,e.unstable_LowPriority=4,e.unstable_NormalPriority=3,e.unstable_Profiling=null,e.unstable_UserBlockingPriority=2,e.unstable_cancelCallback=function(N){N.callback=null},e.unstable_continueExecution=function(){v||m||(v=!0,Mi(x))},e.unstable_forceFrameRate=function(N){0>N||125<N?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):I=0<N?Math.floor(1e3/N):5},e.unstable_getCurrentPriorityLevel=function(){return f},e.unstable_getFirstCallbackNode=function(){return n(a)},e.unstable_next=function(N){switch(f){case 1:case 2:case 3:var V=3;break;default:V=f}var O=f;f=V;try{return N()}finally{f=O}},e.unstable_pauseExecution=function(){},e.unstable_requestPaint=function(){},e.unstable_runWithPriority=function(N,V){switch(N){case 1:case 2:case 3:case 4:case 5:break;default:N=3}var O=f;f=N;try{return V()}finally{f=O}},e.unstable_scheduleCallback=function(N,V,O){var ee=e.unstable_now();switch(typeof O=="object"&&O!==null?(O=O.delay,O=typeof O=="number"&&0<O?ee+O:ee):O=ee,N){case 1:var ue=-1;break;case 2:ue=250;break;case 5:ue=**********;break;case 4:ue=1e4;break;default:ue=5e3}return ue=O+ue,N={id:c++,callback:V,priorityLevel:N,startTime:O,expirationTime:ue,sortIndex:-1},O>ee?(N.sortIndex=O,t(u,N),n(a)===null&&N===n(u)&&(w?(p(P),P=-1):w=!0,oe(S,O-ee))):(N.sortIndex=ue,t(a,N),v||m||(v=!0,Mi(x))),N},e.unstable_shouldYield=ae,e.unstable_wrapCallback=function(N){var V=f;return function(){var O=f;f=V;try{return N.apply(this,arguments)}finally{f=O}}}})(Kd);Wd.exports=Kd;var qg=Wd.exports;/**
 * @license React
 * react-dom.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Jg=E,Fe=qg;function T(e){for(var t="https://reactjs.org/docs/error-decoder.html?invariant="+e,n=1;n<arguments.length;n++)t+="&args[]="+encodeURIComponent(arguments[n]);return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}var Gd=new Set,Yr={};function En(e,t){Jn(e,t),Jn(e+"Capture",t)}function Jn(e,t){for(Yr[e]=t,e=0;e<t.length;e++)Gd.add(t[e])}var xt=!(typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"),ul=Object.prototype.hasOwnProperty,ev=/^[:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD][:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD\-.0-9\u00B7\u0300-\u036F\u203F-\u2040]*$/,Ju={},ec={};function tv(e){return ul.call(ec,e)?!0:ul.call(Ju,e)?!1:ev.test(e)?ec[e]=!0:(Ju[e]=!0,!1)}function nv(e,t,n,r){if(n!==null&&n.type===0)return!1;switch(typeof t){case"function":case"symbol":return!0;case"boolean":return r?!1:n!==null?!n.acceptsBooleans:(e=e.toLowerCase().slice(0,5),e!=="data-"&&e!=="aria-");default:return!1}}function rv(e,t,n,r){if(t===null||typeof t>"u"||nv(e,t,n,r))return!0;if(r)return!1;if(n!==null)switch(n.type){case 3:return!t;case 4:return t===!1;case 5:return isNaN(t);case 6:return isNaN(t)||1>t}return!1}function ke(e,t,n,r,i,o,s){this.acceptsBooleans=t===2||t===3||t===4,this.attributeName=r,this.attributeNamespace=i,this.mustUseProperty=n,this.propertyName=e,this.type=t,this.sanitizeURL=o,this.removeEmptyString=s}var me={};"children dangerouslySetInnerHTML defaultValue defaultChecked innerHTML suppressContentEditableWarning suppressHydrationWarning style".split(" ").forEach(function(e){me[e]=new ke(e,0,!1,e,null,!1,!1)});[["acceptCharset","accept-charset"],["className","class"],["htmlFor","for"],["httpEquiv","http-equiv"]].forEach(function(e){var t=e[0];me[t]=new ke(t,1,!1,e[1],null,!1,!1)});["contentEditable","draggable","spellCheck","value"].forEach(function(e){me[e]=new ke(e,2,!1,e.toLowerCase(),null,!1,!1)});["autoReverse","externalResourcesRequired","focusable","preserveAlpha"].forEach(function(e){me[e]=new ke(e,2,!1,e,null,!1,!1)});"allowFullScreen async autoFocus autoPlay controls default defer disabled disablePictureInPicture disableRemotePlayback formNoValidate hidden loop noModule noValidate open playsInline readOnly required reversed scoped seamless itemScope".split(" ").forEach(function(e){me[e]=new ke(e,3,!1,e.toLowerCase(),null,!1,!1)});["checked","multiple","muted","selected"].forEach(function(e){me[e]=new ke(e,3,!0,e,null,!1,!1)});["capture","download"].forEach(function(e){me[e]=new ke(e,4,!1,e,null,!1,!1)});["cols","rows","size","span"].forEach(function(e){me[e]=new ke(e,6,!1,e,null,!1,!1)});["rowSpan","start"].forEach(function(e){me[e]=new ke(e,5,!1,e.toLowerCase(),null,!1,!1)});var Ca=/[\-:]([a-z])/g;function Pa(e){return e[1].toUpperCase()}"accent-height alignment-baseline arabic-form baseline-shift cap-height clip-path clip-rule color-interpolation color-interpolation-filters color-profile color-rendering dominant-baseline enable-background fill-opacity fill-rule flood-color flood-opacity font-family font-size font-size-adjust font-stretch font-style font-variant font-weight glyph-name glyph-orientation-horizontal glyph-orientation-vertical horiz-adv-x horiz-origin-x image-rendering letter-spacing lighting-color marker-end marker-mid marker-start overline-position overline-thickness paint-order panose-1 pointer-events rendering-intent shape-rendering stop-color stop-opacity strikethrough-position strikethrough-thickness stroke-dasharray stroke-dashoffset stroke-linecap stroke-linejoin stroke-miterlimit stroke-opacity stroke-width text-anchor text-decoration text-rendering underline-position underline-thickness unicode-bidi unicode-range units-per-em v-alphabetic v-hanging v-ideographic v-mathematical vector-effect vert-adv-y vert-origin-x vert-origin-y word-spacing writing-mode xmlns:xlink x-height".split(" ").forEach(function(e){var t=e.replace(Ca,Pa);me[t]=new ke(t,1,!1,e,null,!1,!1)});"xlink:actuate xlink:arcrole xlink:role xlink:show xlink:title xlink:type".split(" ").forEach(function(e){var t=e.replace(Ca,Pa);me[t]=new ke(t,1,!1,e,"http://www.w3.org/1999/xlink",!1,!1)});["xml:base","xml:lang","xml:space"].forEach(function(e){var t=e.replace(Ca,Pa);me[t]=new ke(t,1,!1,e,"http://www.w3.org/XML/1998/namespace",!1,!1)});["tabIndex","crossOrigin"].forEach(function(e){me[e]=new ke(e,1,!1,e.toLowerCase(),null,!1,!1)});me.xlinkHref=new ke("xlinkHref",1,!1,"xlink:href","http://www.w3.org/1999/xlink",!0,!1);["src","href","action","formAction"].forEach(function(e){me[e]=new ke(e,1,!1,e.toLowerCase(),null,!0,!0)});function Ta(e,t,n,r){var i=me.hasOwnProperty(t)?me[t]:null;(i!==null?i.type!==0:r||!(2<t.length)||t[0]!=="o"&&t[0]!=="O"||t[1]!=="n"&&t[1]!=="N")&&(rv(t,n,i,r)&&(n=null),r||i===null?tv(t)&&(n===null?e.removeAttribute(t):e.setAttribute(t,""+n)):i.mustUseProperty?e[i.propertyName]=n===null?i.type===3?!1:"":n:(t=i.attributeName,r=i.attributeNamespace,n===null?e.removeAttribute(t):(i=i.type,n=i===3||i===4&&n===!0?"":""+n,r?e.setAttributeNS(r,t,n):e.setAttribute(t,n))))}var kt=Jg.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED,_i=Symbol.for("react.element"),Dn=Symbol.for("react.portal"),Ln=Symbol.for("react.fragment"),ka=Symbol.for("react.strict_mode"),cl=Symbol.for("react.profiler"),Qd=Symbol.for("react.provider"),Xd=Symbol.for("react.context"),Aa=Symbol.for("react.forward_ref"),fl=Symbol.for("react.suspense"),dl=Symbol.for("react.suspense_list"),Na=Symbol.for("react.memo"),Dt=Symbol.for("react.lazy"),Yd=Symbol.for("react.offscreen"),tc=Symbol.iterator;function yr(e){return e===null||typeof e!="object"?null:(e=tc&&e[tc]||e["@@iterator"],typeof e=="function"?e:null)}var Y=Object.assign,Ps;function Ar(e){if(Ps===void 0)try{throw Error()}catch(n){var t=n.stack.trim().match(/\n( *(at )?)/);Ps=t&&t[1]||""}return`
`+Ps+e}var Ts=!1;function ks(e,t){if(!e||Ts)return"";Ts=!0;var n=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{if(t)if(t=function(){throw Error()},Object.defineProperty(t.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(t,[])}catch(u){var r=u}Reflect.construct(e,[],t)}else{try{t.call()}catch(u){r=u}e.call(t.prototype)}else{try{throw Error()}catch(u){r=u}e()}}catch(u){if(u&&r&&typeof u.stack=="string"){for(var i=u.stack.split(`
`),o=r.stack.split(`
`),s=i.length-1,l=o.length-1;1<=s&&0<=l&&i[s]!==o[l];)l--;for(;1<=s&&0<=l;s--,l--)if(i[s]!==o[l]){if(s!==1||l!==1)do if(s--,l--,0>l||i[s]!==o[l]){var a=`
`+i[s].replace(" at new "," at ");return e.displayName&&a.includes("<anonymous>")&&(a=a.replace("<anonymous>",e.displayName)),a}while(1<=s&&0<=l);break}}}finally{Ts=!1,Error.prepareStackTrace=n}return(e=e?e.displayName||e.name:"")?Ar(e):""}function iv(e){switch(e.tag){case 5:return Ar(e.type);case 16:return Ar("Lazy");case 13:return Ar("Suspense");case 19:return Ar("SuspenseList");case 0:case 2:case 15:return e=ks(e.type,!1),e;case 11:return e=ks(e.type.render,!1),e;case 1:return e=ks(e.type,!0),e;default:return""}}function hl(e){if(e==null)return null;if(typeof e=="function")return e.displayName||e.name||null;if(typeof e=="string")return e;switch(e){case Ln:return"Fragment";case Dn:return"Portal";case cl:return"Profiler";case ka:return"StrictMode";case fl:return"Suspense";case dl:return"SuspenseList"}if(typeof e=="object")switch(e.$$typeof){case Xd:return(e.displayName||"Context")+".Consumer";case Qd:return(e._context.displayName||"Context")+".Provider";case Aa:var t=e.render;return e=e.displayName,e||(e=t.displayName||t.name||"",e=e!==""?"ForwardRef("+e+")":"ForwardRef"),e;case Na:return t=e.displayName||null,t!==null?t:hl(e.type)||"Memo";case Dt:t=e._payload,e=e._init;try{return hl(e(t))}catch{}}return null}function ov(e){var t=e.type;switch(e.tag){case 24:return"Cache";case 9:return(t.displayName||"Context")+".Consumer";case 10:return(t._context.displayName||"Context")+".Provider";case 18:return"DehydratedFragment";case 11:return e=t.render,e=e.displayName||e.name||"",t.displayName||(e!==""?"ForwardRef("+e+")":"ForwardRef");case 7:return"Fragment";case 5:return t;case 4:return"Portal";case 3:return"Root";case 6:return"Text";case 16:return hl(t);case 8:return t===ka?"StrictMode":"Mode";case 22:return"Offscreen";case 12:return"Profiler";case 21:return"Scope";case 13:return"Suspense";case 19:return"SuspenseList";case 25:return"TracingMarker";case 1:case 0:case 17:case 2:case 14:case 15:if(typeof t=="function")return t.displayName||t.name||null;if(typeof t=="string")return t}return null}function Wt(e){switch(typeof e){case"boolean":case"number":case"string":case"undefined":return e;case"object":return e;default:return""}}function bd(e){var t=e.type;return(e=e.nodeName)&&e.toLowerCase()==="input"&&(t==="checkbox"||t==="radio")}function sv(e){var t=bd(e)?"checked":"value",n=Object.getOwnPropertyDescriptor(e.constructor.prototype,t),r=""+e[t];if(!e.hasOwnProperty(t)&&typeof n<"u"&&typeof n.get=="function"&&typeof n.set=="function"){var i=n.get,o=n.set;return Object.defineProperty(e,t,{configurable:!0,get:function(){return i.call(this)},set:function(s){r=""+s,o.call(this,s)}}),Object.defineProperty(e,t,{enumerable:n.enumerable}),{getValue:function(){return r},setValue:function(s){r=""+s},stopTracking:function(){e._valueTracker=null,delete e[t]}}}}function Vi(e){e._valueTracker||(e._valueTracker=sv(e))}function Zd(e){if(!e)return!1;var t=e._valueTracker;if(!t)return!0;var n=t.getValue(),r="";return e&&(r=bd(e)?e.checked?"true":"false":e.value),e=r,e!==n?(t.setValue(e),!0):!1}function yo(e){if(e=e||(typeof document<"u"?document:void 0),typeof e>"u")return null;try{return e.activeElement||e.body}catch{return e.body}}function pl(e,t){var n=t.checked;return Y({},t,{defaultChecked:void 0,defaultValue:void 0,value:void 0,checked:n??e._wrapperState.initialChecked})}function nc(e,t){var n=t.defaultValue==null?"":t.defaultValue,r=t.checked!=null?t.checked:t.defaultChecked;n=Wt(t.value!=null?t.value:n),e._wrapperState={initialChecked:r,initialValue:n,controlled:t.type==="checkbox"||t.type==="radio"?t.checked!=null:t.value!=null}}function qd(e,t){t=t.checked,t!=null&&Ta(e,"checked",t,!1)}function ml(e,t){qd(e,t);var n=Wt(t.value),r=t.type;if(n!=null)r==="number"?(n===0&&e.value===""||e.value!=n)&&(e.value=""+n):e.value!==""+n&&(e.value=""+n);else if(r==="submit"||r==="reset"){e.removeAttribute("value");return}t.hasOwnProperty("value")?gl(e,t.type,n):t.hasOwnProperty("defaultValue")&&gl(e,t.type,Wt(t.defaultValue)),t.checked==null&&t.defaultChecked!=null&&(e.defaultChecked=!!t.defaultChecked)}function rc(e,t,n){if(t.hasOwnProperty("value")||t.hasOwnProperty("defaultValue")){var r=t.type;if(!(r!=="submit"&&r!=="reset"||t.value!==void 0&&t.value!==null))return;t=""+e._wrapperState.initialValue,n||t===e.value||(e.value=t),e.defaultValue=t}n=e.name,n!==""&&(e.name=""),e.defaultChecked=!!e._wrapperState.initialChecked,n!==""&&(e.name=n)}function gl(e,t,n){(t!=="number"||yo(e.ownerDocument)!==e)&&(n==null?e.defaultValue=""+e._wrapperState.initialValue:e.defaultValue!==""+n&&(e.defaultValue=""+n))}var Nr=Array.isArray;function Qn(e,t,n,r){if(e=e.options,t){t={};for(var i=0;i<n.length;i++)t["$"+n[i]]=!0;for(n=0;n<e.length;n++)i=t.hasOwnProperty("$"+e[n].value),e[n].selected!==i&&(e[n].selected=i),i&&r&&(e[n].defaultSelected=!0)}else{for(n=""+Wt(n),t=null,i=0;i<e.length;i++){if(e[i].value===n){e[i].selected=!0,r&&(e[i].defaultSelected=!0);return}t!==null||e[i].disabled||(t=e[i])}t!==null&&(t.selected=!0)}}function vl(e,t){if(t.dangerouslySetInnerHTML!=null)throw Error(T(91));return Y({},t,{value:void 0,defaultValue:void 0,children:""+e._wrapperState.initialValue})}function ic(e,t){var n=t.value;if(n==null){if(n=t.children,t=t.defaultValue,n!=null){if(t!=null)throw Error(T(92));if(Nr(n)){if(1<n.length)throw Error(T(93));n=n[0]}t=n}t==null&&(t=""),n=t}e._wrapperState={initialValue:Wt(n)}}function Jd(e,t){var n=Wt(t.value),r=Wt(t.defaultValue);n!=null&&(n=""+n,n!==e.value&&(e.value=n),t.defaultValue==null&&e.defaultValue!==n&&(e.defaultValue=n)),r!=null&&(e.defaultValue=""+r)}function oc(e){var t=e.textContent;t===e._wrapperState.initialValue&&t!==""&&t!==null&&(e.value=t)}function eh(e){switch(e){case"svg":return"http://www.w3.org/2000/svg";case"math":return"http://www.w3.org/1998/Math/MathML";default:return"http://www.w3.org/1999/xhtml"}}function yl(e,t){return e==null||e==="http://www.w3.org/1999/xhtml"?eh(t):e==="http://www.w3.org/2000/svg"&&t==="foreignObject"?"http://www.w3.org/1999/xhtml":e}var Oi,th=function(e){return typeof MSApp<"u"&&MSApp.execUnsafeLocalFunction?function(t,n,r,i){MSApp.execUnsafeLocalFunction(function(){return e(t,n,r,i)})}:e}(function(e,t){if(e.namespaceURI!=="http://www.w3.org/2000/svg"||"innerHTML"in e)e.innerHTML=t;else{for(Oi=Oi||document.createElement("div"),Oi.innerHTML="<svg>"+t.valueOf().toString()+"</svg>",t=Oi.firstChild;e.firstChild;)e.removeChild(e.firstChild);for(;t.firstChild;)e.appendChild(t.firstChild)}});function br(e,t){if(t){var n=e.firstChild;if(n&&n===e.lastChild&&n.nodeType===3){n.nodeValue=t;return}}e.textContent=t}var Vr={animationIterationCount:!0,aspectRatio:!0,borderImageOutset:!0,borderImageSlice:!0,borderImageWidth:!0,boxFlex:!0,boxFlexGroup:!0,boxOrdinalGroup:!0,columnCount:!0,columns:!0,flex:!0,flexGrow:!0,flexPositive:!0,flexShrink:!0,flexNegative:!0,flexOrder:!0,gridArea:!0,gridRow:!0,gridRowEnd:!0,gridRowSpan:!0,gridRowStart:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnSpan:!0,gridColumnStart:!0,fontWeight:!0,lineClamp:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,tabSize:!0,widows:!0,zIndex:!0,zoom:!0,fillOpacity:!0,floodOpacity:!0,stopOpacity:!0,strokeDasharray:!0,strokeDashoffset:!0,strokeMiterlimit:!0,strokeOpacity:!0,strokeWidth:!0},lv=["Webkit","ms","Moz","O"];Object.keys(Vr).forEach(function(e){lv.forEach(function(t){t=t+e.charAt(0).toUpperCase()+e.substring(1),Vr[t]=Vr[e]})});function nh(e,t,n){return t==null||typeof t=="boolean"||t===""?"":n||typeof t!="number"||t===0||Vr.hasOwnProperty(e)&&Vr[e]?(""+t).trim():t+"px"}function rh(e,t){e=e.style;for(var n in t)if(t.hasOwnProperty(n)){var r=n.indexOf("--")===0,i=nh(n,t[n],r);n==="float"&&(n="cssFloat"),r?e.setProperty(n,i):e[n]=i}}var av=Y({menuitem:!0},{area:!0,base:!0,br:!0,col:!0,embed:!0,hr:!0,img:!0,input:!0,keygen:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0});function wl(e,t){if(t){if(av[e]&&(t.children!=null||t.dangerouslySetInnerHTML!=null))throw Error(T(137,e));if(t.dangerouslySetInnerHTML!=null){if(t.children!=null)throw Error(T(60));if(typeof t.dangerouslySetInnerHTML!="object"||!("__html"in t.dangerouslySetInnerHTML))throw Error(T(61))}if(t.style!=null&&typeof t.style!="object")throw Error(T(62))}}function Sl(e,t){if(e.indexOf("-")===-1)return typeof t.is=="string";switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var xl=null;function Ma(e){return e=e.target||e.srcElement||window,e.correspondingUseElement&&(e=e.correspondingUseElement),e.nodeType===3?e.parentNode:e}var El=null,Xn=null,Yn=null;function sc(e){if(e=xi(e)){if(typeof El!="function")throw Error(T(280));var t=e.stateNode;t&&(t=is(t),El(e.stateNode,e.type,t))}}function ih(e){Xn?Yn?Yn.push(e):Yn=[e]:Xn=e}function oh(){if(Xn){var e=Xn,t=Yn;if(Yn=Xn=null,sc(e),t)for(e=0;e<t.length;e++)sc(t[e])}}function sh(e,t){return e(t)}function lh(){}var As=!1;function ah(e,t,n){if(As)return e(t,n);As=!0;try{return sh(e,t,n)}finally{As=!1,(Xn!==null||Yn!==null)&&(lh(),oh())}}function Zr(e,t){var n=e.stateNode;if(n===null)return null;var r=is(n);if(r===null)return null;n=r[t];e:switch(t){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(r=!r.disabled)||(e=e.type,r=!(e==="button"||e==="input"||e==="select"||e==="textarea")),e=!r;break e;default:e=!1}if(e)return null;if(n&&typeof n!="function")throw Error(T(231,t,typeof n));return n}var Cl=!1;if(xt)try{var wr={};Object.defineProperty(wr,"passive",{get:function(){Cl=!0}}),window.addEventListener("test",wr,wr),window.removeEventListener("test",wr,wr)}catch{Cl=!1}function uv(e,t,n,r,i,o,s,l,a){var u=Array.prototype.slice.call(arguments,3);try{t.apply(n,u)}catch(c){this.onError(c)}}var Or=!1,wo=null,So=!1,Pl=null,cv={onError:function(e){Or=!0,wo=e}};function fv(e,t,n,r,i,o,s,l,a){Or=!1,wo=null,uv.apply(cv,arguments)}function dv(e,t,n,r,i,o,s,l,a){if(fv.apply(this,arguments),Or){if(Or){var u=wo;Or=!1,wo=null}else throw Error(T(198));So||(So=!0,Pl=u)}}function Cn(e){var t=e,n=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do t=e,t.flags&4098&&(n=t.return),e=t.return;while(e)}return t.tag===3?n:null}function uh(e){if(e.tag===13){var t=e.memoizedState;if(t===null&&(e=e.alternate,e!==null&&(t=e.memoizedState)),t!==null)return t.dehydrated}return null}function lc(e){if(Cn(e)!==e)throw Error(T(188))}function hv(e){var t=e.alternate;if(!t){if(t=Cn(e),t===null)throw Error(T(188));return t!==e?null:e}for(var n=e,r=t;;){var i=n.return;if(i===null)break;var o=i.alternate;if(o===null){if(r=i.return,r!==null){n=r;continue}break}if(i.child===o.child){for(o=i.child;o;){if(o===n)return lc(i),e;if(o===r)return lc(i),t;o=o.sibling}throw Error(T(188))}if(n.return!==r.return)n=i,r=o;else{for(var s=!1,l=i.child;l;){if(l===n){s=!0,n=i,r=o;break}if(l===r){s=!0,r=i,n=o;break}l=l.sibling}if(!s){for(l=o.child;l;){if(l===n){s=!0,n=o,r=i;break}if(l===r){s=!0,r=o,n=i;break}l=l.sibling}if(!s)throw Error(T(189))}}if(n.alternate!==r)throw Error(T(190))}if(n.tag!==3)throw Error(T(188));return n.stateNode.current===n?e:t}function ch(e){return e=hv(e),e!==null?fh(e):null}function fh(e){if(e.tag===5||e.tag===6)return e;for(e=e.child;e!==null;){var t=fh(e);if(t!==null)return t;e=e.sibling}return null}var dh=Fe.unstable_scheduleCallback,ac=Fe.unstable_cancelCallback,pv=Fe.unstable_shouldYield,mv=Fe.unstable_requestPaint,re=Fe.unstable_now,gv=Fe.unstable_getCurrentPriorityLevel,Da=Fe.unstable_ImmediatePriority,hh=Fe.unstable_UserBlockingPriority,xo=Fe.unstable_NormalPriority,vv=Fe.unstable_LowPriority,ph=Fe.unstable_IdlePriority,es=null,ut=null;function yv(e){if(ut&&typeof ut.onCommitFiberRoot=="function")try{ut.onCommitFiberRoot(es,e,void 0,(e.current.flags&128)===128)}catch{}}var rt=Math.clz32?Math.clz32:xv,wv=Math.log,Sv=Math.LN2;function xv(e){return e>>>=0,e===0?32:31-(wv(e)/Sv|0)|0}var Ii=64,Fi=4194304;function Mr(e){switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e&4194240;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return e&130023424;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 1073741824;default:return e}}function Eo(e,t){var n=e.pendingLanes;if(n===0)return 0;var r=0,i=e.suspendedLanes,o=e.pingedLanes,s=n&268435455;if(s!==0){var l=s&~i;l!==0?r=Mr(l):(o&=s,o!==0&&(r=Mr(o)))}else s=n&~i,s!==0?r=Mr(s):o!==0&&(r=Mr(o));if(r===0)return 0;if(t!==0&&t!==r&&!(t&i)&&(i=r&-r,o=t&-t,i>=o||i===16&&(o&4194240)!==0))return t;if(r&4&&(r|=n&16),t=e.entangledLanes,t!==0)for(e=e.entanglements,t&=r;0<t;)n=31-rt(t),i=1<<n,r|=e[n],t&=~i;return r}function Ev(e,t){switch(e){case 1:case 2:case 4:return t+250;case 8:case 16:case 32:case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t+5e3;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return-1;case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function Cv(e,t){for(var n=e.suspendedLanes,r=e.pingedLanes,i=e.expirationTimes,o=e.pendingLanes;0<o;){var s=31-rt(o),l=1<<s,a=i[s];a===-1?(!(l&n)||l&r)&&(i[s]=Ev(l,t)):a<=t&&(e.expiredLanes|=l),o&=~l}}function Tl(e){return e=e.pendingLanes&-1073741825,e!==0?e:e&1073741824?1073741824:0}function mh(){var e=Ii;return Ii<<=1,!(Ii&4194240)&&(Ii=64),e}function Ns(e){for(var t=[],n=0;31>n;n++)t.push(e);return t}function wi(e,t,n){e.pendingLanes|=t,t!==536870912&&(e.suspendedLanes=0,e.pingedLanes=0),e=e.eventTimes,t=31-rt(t),e[t]=n}function Pv(e,t){var n=e.pendingLanes&~t;e.pendingLanes=t,e.suspendedLanes=0,e.pingedLanes=0,e.expiredLanes&=t,e.mutableReadLanes&=t,e.entangledLanes&=t,t=e.entanglements;var r=e.eventTimes;for(e=e.expirationTimes;0<n;){var i=31-rt(n),o=1<<i;t[i]=0,r[i]=-1,e[i]=-1,n&=~o}}function La(e,t){var n=e.entangledLanes|=t;for(e=e.entanglements;n;){var r=31-rt(n),i=1<<r;i&t|e[r]&t&&(e[r]|=t),n&=~i}}var z=0;function gh(e){return e&=-e,1<e?4<e?e&268435455?16:536870912:4:1}var vh,Ra,yh,wh,Sh,kl=!1,ji=[],It=null,Ft=null,jt=null,qr=new Map,Jr=new Map,Rt=[],Tv="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset submit".split(" ");function uc(e,t){switch(e){case"focusin":case"focusout":It=null;break;case"dragenter":case"dragleave":Ft=null;break;case"mouseover":case"mouseout":jt=null;break;case"pointerover":case"pointerout":qr.delete(t.pointerId);break;case"gotpointercapture":case"lostpointercapture":Jr.delete(t.pointerId)}}function Sr(e,t,n,r,i,o){return e===null||e.nativeEvent!==o?(e={blockedOn:t,domEventName:n,eventSystemFlags:r,nativeEvent:o,targetContainers:[i]},t!==null&&(t=xi(t),t!==null&&Ra(t)),e):(e.eventSystemFlags|=r,t=e.targetContainers,i!==null&&t.indexOf(i)===-1&&t.push(i),e)}function kv(e,t,n,r,i){switch(t){case"focusin":return It=Sr(It,e,t,n,r,i),!0;case"dragenter":return Ft=Sr(Ft,e,t,n,r,i),!0;case"mouseover":return jt=Sr(jt,e,t,n,r,i),!0;case"pointerover":var o=i.pointerId;return qr.set(o,Sr(qr.get(o)||null,e,t,n,r,i)),!0;case"gotpointercapture":return o=i.pointerId,Jr.set(o,Sr(Jr.get(o)||null,e,t,n,r,i)),!0}return!1}function xh(e){var t=ln(e.target);if(t!==null){var n=Cn(t);if(n!==null){if(t=n.tag,t===13){if(t=uh(n),t!==null){e.blockedOn=t,Sh(e.priority,function(){yh(n)});return}}else if(t===3&&n.stateNode.current.memoizedState.isDehydrated){e.blockedOn=n.tag===3?n.stateNode.containerInfo:null;return}}}e.blockedOn=null}function no(e){if(e.blockedOn!==null)return!1;for(var t=e.targetContainers;0<t.length;){var n=Al(e.domEventName,e.eventSystemFlags,t[0],e.nativeEvent);if(n===null){n=e.nativeEvent;var r=new n.constructor(n.type,n);xl=r,n.target.dispatchEvent(r),xl=null}else return t=xi(n),t!==null&&Ra(t),e.blockedOn=n,!1;t.shift()}return!0}function cc(e,t,n){no(e)&&n.delete(t)}function Av(){kl=!1,It!==null&&no(It)&&(It=null),Ft!==null&&no(Ft)&&(Ft=null),jt!==null&&no(jt)&&(jt=null),qr.forEach(cc),Jr.forEach(cc)}function xr(e,t){e.blockedOn===t&&(e.blockedOn=null,kl||(kl=!0,Fe.unstable_scheduleCallback(Fe.unstable_NormalPriority,Av)))}function ei(e){function t(i){return xr(i,e)}if(0<ji.length){xr(ji[0],e);for(var n=1;n<ji.length;n++){var r=ji[n];r.blockedOn===e&&(r.blockedOn=null)}}for(It!==null&&xr(It,e),Ft!==null&&xr(Ft,e),jt!==null&&xr(jt,e),qr.forEach(t),Jr.forEach(t),n=0;n<Rt.length;n++)r=Rt[n],r.blockedOn===e&&(r.blockedOn=null);for(;0<Rt.length&&(n=Rt[0],n.blockedOn===null);)xh(n),n.blockedOn===null&&Rt.shift()}var bn=kt.ReactCurrentBatchConfig,Co=!0;function Nv(e,t,n,r){var i=z,o=bn.transition;bn.transition=null;try{z=1,_a(e,t,n,r)}finally{z=i,bn.transition=o}}function Mv(e,t,n,r){var i=z,o=bn.transition;bn.transition=null;try{z=4,_a(e,t,n,r)}finally{z=i,bn.transition=o}}function _a(e,t,n,r){if(Co){var i=Al(e,t,n,r);if(i===null)js(e,t,r,Po,n),uc(e,r);else if(kv(i,e,t,n,r))r.stopPropagation();else if(uc(e,r),t&4&&-1<Tv.indexOf(e)){for(;i!==null;){var o=xi(i);if(o!==null&&vh(o),o=Al(e,t,n,r),o===null&&js(e,t,r,Po,n),o===i)break;i=o}i!==null&&r.stopPropagation()}else js(e,t,r,null,n)}}var Po=null;function Al(e,t,n,r){if(Po=null,e=Ma(r),e=ln(e),e!==null)if(t=Cn(e),t===null)e=null;else if(n=t.tag,n===13){if(e=uh(t),e!==null)return e;e=null}else if(n===3){if(t.stateNode.current.memoizedState.isDehydrated)return t.tag===3?t.stateNode.containerInfo:null;e=null}else t!==e&&(e=null);return Po=e,null}function Eh(e){switch(e){case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 1;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"toggle":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 4;case"message":switch(gv()){case Da:return 1;case hh:return 4;case xo:case vv:return 16;case ph:return 536870912;default:return 16}default:return 16}}var Vt=null,Va=null,ro=null;function Ch(){if(ro)return ro;var e,t=Va,n=t.length,r,i="value"in Vt?Vt.value:Vt.textContent,o=i.length;for(e=0;e<n&&t[e]===i[e];e++);var s=n-e;for(r=1;r<=s&&t[n-r]===i[o-r];r++);return ro=i.slice(e,1<r?1-r:void 0)}function io(e){var t=e.keyCode;return"charCode"in e?(e=e.charCode,e===0&&t===13&&(e=13)):e=t,e===10&&(e=13),32<=e||e===13?e:0}function zi(){return!0}function fc(){return!1}function Be(e){function t(n,r,i,o,s){this._reactName=n,this._targetInst=i,this.type=r,this.nativeEvent=o,this.target=s,this.currentTarget=null;for(var l in e)e.hasOwnProperty(l)&&(n=e[l],this[l]=n?n(o):o[l]);return this.isDefaultPrevented=(o.defaultPrevented!=null?o.defaultPrevented:o.returnValue===!1)?zi:fc,this.isPropagationStopped=fc,this}return Y(t.prototype,{preventDefault:function(){this.defaultPrevented=!0;var n=this.nativeEvent;n&&(n.preventDefault?n.preventDefault():typeof n.returnValue!="unknown"&&(n.returnValue=!1),this.isDefaultPrevented=zi)},stopPropagation:function(){var n=this.nativeEvent;n&&(n.stopPropagation?n.stopPropagation():typeof n.cancelBubble!="unknown"&&(n.cancelBubble=!0),this.isPropagationStopped=zi)},persist:function(){},isPersistent:zi}),t}var dr={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},Oa=Be(dr),Si=Y({},dr,{view:0,detail:0}),Dv=Be(Si),Ms,Ds,Er,ts=Y({},Si,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:Ia,button:0,buttons:0,relatedTarget:function(e){return e.relatedTarget===void 0?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==Er&&(Er&&e.type==="mousemove"?(Ms=e.screenX-Er.screenX,Ds=e.screenY-Er.screenY):Ds=Ms=0,Er=e),Ms)},movementY:function(e){return"movementY"in e?e.movementY:Ds}}),dc=Be(ts),Lv=Y({},ts,{dataTransfer:0}),Rv=Be(Lv),_v=Y({},Si,{relatedTarget:0}),Ls=Be(_v),Vv=Y({},dr,{animationName:0,elapsedTime:0,pseudoElement:0}),Ov=Be(Vv),Iv=Y({},dr,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}}),Fv=Be(Iv),jv=Y({},dr,{data:0}),hc=Be(jv),zv={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},Bv={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},Uv={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function $v(e){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(e):(e=Uv[e])?!!t[e]:!1}function Ia(){return $v}var Hv=Y({},Si,{key:function(e){if(e.key){var t=zv[e.key]||e.key;if(t!=="Unidentified")return t}return e.type==="keypress"?(e=io(e),e===13?"Enter":String.fromCharCode(e)):e.type==="keydown"||e.type==="keyup"?Bv[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:Ia,charCode:function(e){return e.type==="keypress"?io(e):0},keyCode:function(e){return e.type==="keydown"||e.type==="keyup"?e.keyCode:0},which:function(e){return e.type==="keypress"?io(e):e.type==="keydown"||e.type==="keyup"?e.keyCode:0}}),Wv=Be(Hv),Kv=Y({},ts,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),pc=Be(Kv),Gv=Y({},Si,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:Ia}),Qv=Be(Gv),Xv=Y({},dr,{propertyName:0,elapsedTime:0,pseudoElement:0}),Yv=Be(Xv),bv=Y({},ts,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0}),Zv=Be(bv),qv=[9,13,27,32],Fa=xt&&"CompositionEvent"in window,Ir=null;xt&&"documentMode"in document&&(Ir=document.documentMode);var Jv=xt&&"TextEvent"in window&&!Ir,Ph=xt&&(!Fa||Ir&&8<Ir&&11>=Ir),mc=String.fromCharCode(32),gc=!1;function Th(e,t){switch(e){case"keyup":return qv.indexOf(t.keyCode)!==-1;case"keydown":return t.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function kh(e){return e=e.detail,typeof e=="object"&&"data"in e?e.data:null}var Rn=!1;function e0(e,t){switch(e){case"compositionend":return kh(t);case"keypress":return t.which!==32?null:(gc=!0,mc);case"textInput":return e=t.data,e===mc&&gc?null:e;default:return null}}function t0(e,t){if(Rn)return e==="compositionend"||!Fa&&Th(e,t)?(e=Ch(),ro=Va=Vt=null,Rn=!1,e):null;switch(e){case"paste":return null;case"keypress":if(!(t.ctrlKey||t.altKey||t.metaKey)||t.ctrlKey&&t.altKey){if(t.char&&1<t.char.length)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return Ph&&t.locale!=="ko"?null:t.data;default:return null}}var n0={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function vc(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t==="input"?!!n0[e.type]:t==="textarea"}function Ah(e,t,n,r){ih(r),t=To(t,"onChange"),0<t.length&&(n=new Oa("onChange","change",null,n,r),e.push({event:n,listeners:t}))}var Fr=null,ti=null;function r0(e){jh(e,0)}function ns(e){var t=On(e);if(Zd(t))return e}function i0(e,t){if(e==="change")return t}var Nh=!1;if(xt){var Rs;if(xt){var _s="oninput"in document;if(!_s){var yc=document.createElement("div");yc.setAttribute("oninput","return;"),_s=typeof yc.oninput=="function"}Rs=_s}else Rs=!1;Nh=Rs&&(!document.documentMode||9<document.documentMode)}function wc(){Fr&&(Fr.detachEvent("onpropertychange",Mh),ti=Fr=null)}function Mh(e){if(e.propertyName==="value"&&ns(ti)){var t=[];Ah(t,ti,e,Ma(e)),ah(r0,t)}}function o0(e,t,n){e==="focusin"?(wc(),Fr=t,ti=n,Fr.attachEvent("onpropertychange",Mh)):e==="focusout"&&wc()}function s0(e){if(e==="selectionchange"||e==="keyup"||e==="keydown")return ns(ti)}function l0(e,t){if(e==="click")return ns(t)}function a0(e,t){if(e==="input"||e==="change")return ns(t)}function u0(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var ot=typeof Object.is=="function"?Object.is:u0;function ni(e,t){if(ot(e,t))return!0;if(typeof e!="object"||e===null||typeof t!="object"||t===null)return!1;var n=Object.keys(e),r=Object.keys(t);if(n.length!==r.length)return!1;for(r=0;r<n.length;r++){var i=n[r];if(!ul.call(t,i)||!ot(e[i],t[i]))return!1}return!0}function Sc(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function xc(e,t){var n=Sc(e);e=0;for(var r;n;){if(n.nodeType===3){if(r=e+n.textContent.length,e<=t&&r>=t)return{node:n,offset:t-e};e=r}e:{for(;n;){if(n.nextSibling){n=n.nextSibling;break e}n=n.parentNode}n=void 0}n=Sc(n)}}function Dh(e,t){return e&&t?e===t?!0:e&&e.nodeType===3?!1:t&&t.nodeType===3?Dh(e,t.parentNode):"contains"in e?e.contains(t):e.compareDocumentPosition?!!(e.compareDocumentPosition(t)&16):!1:!1}function Lh(){for(var e=window,t=yo();t instanceof e.HTMLIFrameElement;){try{var n=typeof t.contentWindow.location.href=="string"}catch{n=!1}if(n)e=t.contentWindow;else break;t=yo(e.document)}return t}function ja(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&(t==="input"&&(e.type==="text"||e.type==="search"||e.type==="tel"||e.type==="url"||e.type==="password")||t==="textarea"||e.contentEditable==="true")}function c0(e){var t=Lh(),n=e.focusedElem,r=e.selectionRange;if(t!==n&&n&&n.ownerDocument&&Dh(n.ownerDocument.documentElement,n)){if(r!==null&&ja(n)){if(t=r.start,e=r.end,e===void 0&&(e=t),"selectionStart"in n)n.selectionStart=t,n.selectionEnd=Math.min(e,n.value.length);else if(e=(t=n.ownerDocument||document)&&t.defaultView||window,e.getSelection){e=e.getSelection();var i=n.textContent.length,o=Math.min(r.start,i);r=r.end===void 0?o:Math.min(r.end,i),!e.extend&&o>r&&(i=r,r=o,o=i),i=xc(n,o);var s=xc(n,r);i&&s&&(e.rangeCount!==1||e.anchorNode!==i.node||e.anchorOffset!==i.offset||e.focusNode!==s.node||e.focusOffset!==s.offset)&&(t=t.createRange(),t.setStart(i.node,i.offset),e.removeAllRanges(),o>r?(e.addRange(t),e.extend(s.node,s.offset)):(t.setEnd(s.node,s.offset),e.addRange(t)))}}for(t=[],e=n;e=e.parentNode;)e.nodeType===1&&t.push({element:e,left:e.scrollLeft,top:e.scrollTop});for(typeof n.focus=="function"&&n.focus(),n=0;n<t.length;n++)e=t[n],e.element.scrollLeft=e.left,e.element.scrollTop=e.top}}var f0=xt&&"documentMode"in document&&11>=document.documentMode,_n=null,Nl=null,jr=null,Ml=!1;function Ec(e,t,n){var r=n.window===n?n.document:n.nodeType===9?n:n.ownerDocument;Ml||_n==null||_n!==yo(r)||(r=_n,"selectionStart"in r&&ja(r)?r={start:r.selectionStart,end:r.selectionEnd}:(r=(r.ownerDocument&&r.ownerDocument.defaultView||window).getSelection(),r={anchorNode:r.anchorNode,anchorOffset:r.anchorOffset,focusNode:r.focusNode,focusOffset:r.focusOffset}),jr&&ni(jr,r)||(jr=r,r=To(Nl,"onSelect"),0<r.length&&(t=new Oa("onSelect","select",null,t,n),e.push({event:t,listeners:r}),t.target=_n)))}function Bi(e,t){var n={};return n[e.toLowerCase()]=t.toLowerCase(),n["Webkit"+e]="webkit"+t,n["Moz"+e]="moz"+t,n}var Vn={animationend:Bi("Animation","AnimationEnd"),animationiteration:Bi("Animation","AnimationIteration"),animationstart:Bi("Animation","AnimationStart"),transitionend:Bi("Transition","TransitionEnd")},Vs={},Rh={};xt&&(Rh=document.createElement("div").style,"AnimationEvent"in window||(delete Vn.animationend.animation,delete Vn.animationiteration.animation,delete Vn.animationstart.animation),"TransitionEvent"in window||delete Vn.transitionend.transition);function rs(e){if(Vs[e])return Vs[e];if(!Vn[e])return e;var t=Vn[e],n;for(n in t)if(t.hasOwnProperty(n)&&n in Rh)return Vs[e]=t[n];return e}var _h=rs("animationend"),Vh=rs("animationiteration"),Oh=rs("animationstart"),Ih=rs("transitionend"),Fh=new Map,Cc="abort auxClick cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");function Xt(e,t){Fh.set(e,t),En(t,[e])}for(var Os=0;Os<Cc.length;Os++){var Is=Cc[Os],d0=Is.toLowerCase(),h0=Is[0].toUpperCase()+Is.slice(1);Xt(d0,"on"+h0)}Xt(_h,"onAnimationEnd");Xt(Vh,"onAnimationIteration");Xt(Oh,"onAnimationStart");Xt("dblclick","onDoubleClick");Xt("focusin","onFocus");Xt("focusout","onBlur");Xt(Ih,"onTransitionEnd");Jn("onMouseEnter",["mouseout","mouseover"]);Jn("onMouseLeave",["mouseout","mouseover"]);Jn("onPointerEnter",["pointerout","pointerover"]);Jn("onPointerLeave",["pointerout","pointerover"]);En("onChange","change click focusin focusout input keydown keyup selectionchange".split(" "));En("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" "));En("onBeforeInput",["compositionend","keypress","textInput","paste"]);En("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" "));En("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" "));En("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var Dr="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),p0=new Set("cancel close invalid load scroll toggle".split(" ").concat(Dr));function Pc(e,t,n){var r=e.type||"unknown-event";e.currentTarget=n,dv(r,t,void 0,e),e.currentTarget=null}function jh(e,t){t=(t&4)!==0;for(var n=0;n<e.length;n++){var r=e[n],i=r.event;r=r.listeners;e:{var o=void 0;if(t)for(var s=r.length-1;0<=s;s--){var l=r[s],a=l.instance,u=l.currentTarget;if(l=l.listener,a!==o&&i.isPropagationStopped())break e;Pc(i,l,u),o=a}else for(s=0;s<r.length;s++){if(l=r[s],a=l.instance,u=l.currentTarget,l=l.listener,a!==o&&i.isPropagationStopped())break e;Pc(i,l,u),o=a}}}if(So)throw e=Pl,So=!1,Pl=null,e}function U(e,t){var n=t[Vl];n===void 0&&(n=t[Vl]=new Set);var r=e+"__bubble";n.has(r)||(zh(t,e,2,!1),n.add(r))}function Fs(e,t,n){var r=0;t&&(r|=4),zh(n,e,r,t)}var Ui="_reactListening"+Math.random().toString(36).slice(2);function ri(e){if(!e[Ui]){e[Ui]=!0,Gd.forEach(function(n){n!=="selectionchange"&&(p0.has(n)||Fs(n,!1,e),Fs(n,!0,e))});var t=e.nodeType===9?e:e.ownerDocument;t===null||t[Ui]||(t[Ui]=!0,Fs("selectionchange",!1,t))}}function zh(e,t,n,r){switch(Eh(t)){case 1:var i=Nv;break;case 4:i=Mv;break;default:i=_a}n=i.bind(null,t,n,e),i=void 0,!Cl||t!=="touchstart"&&t!=="touchmove"&&t!=="wheel"||(i=!0),r?i!==void 0?e.addEventListener(t,n,{capture:!0,passive:i}):e.addEventListener(t,n,!0):i!==void 0?e.addEventListener(t,n,{passive:i}):e.addEventListener(t,n,!1)}function js(e,t,n,r,i){var o=r;if(!(t&1)&&!(t&2)&&r!==null)e:for(;;){if(r===null)return;var s=r.tag;if(s===3||s===4){var l=r.stateNode.containerInfo;if(l===i||l.nodeType===8&&l.parentNode===i)break;if(s===4)for(s=r.return;s!==null;){var a=s.tag;if((a===3||a===4)&&(a=s.stateNode.containerInfo,a===i||a.nodeType===8&&a.parentNode===i))return;s=s.return}for(;l!==null;){if(s=ln(l),s===null)return;if(a=s.tag,a===5||a===6){r=o=s;continue e}l=l.parentNode}}r=r.return}ah(function(){var u=o,c=Ma(n),d=[];e:{var f=Fh.get(e);if(f!==void 0){var m=Oa,v=e;switch(e){case"keypress":if(io(n)===0)break e;case"keydown":case"keyup":m=Wv;break;case"focusin":v="focus",m=Ls;break;case"focusout":v="blur",m=Ls;break;case"beforeblur":case"afterblur":m=Ls;break;case"click":if(n.button===2)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":m=dc;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":m=Rv;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":m=Qv;break;case _h:case Vh:case Oh:m=Ov;break;case Ih:m=Yv;break;case"scroll":m=Dv;break;case"wheel":m=Zv;break;case"copy":case"cut":case"paste":m=Fv;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":m=pc}var w=(t&4)!==0,C=!w&&e==="scroll",p=w?f!==null?f+"Capture":null:f;w=[];for(var h=u,g;h!==null;){g=h;var S=g.stateNode;if(g.tag===5&&S!==null&&(g=S,p!==null&&(S=Zr(h,p),S!=null&&w.push(ii(h,S,g)))),C)break;h=h.return}0<w.length&&(f=new m(f,v,null,n,c),d.push({event:f,listeners:w}))}}if(!(t&7)){e:{if(f=e==="mouseover"||e==="pointerover",m=e==="mouseout"||e==="pointerout",f&&n!==xl&&(v=n.relatedTarget||n.fromElement)&&(ln(v)||v[Et]))break e;if((m||f)&&(f=c.window===c?c:(f=c.ownerDocument)?f.defaultView||f.parentWindow:window,m?(v=n.relatedTarget||n.toElement,m=u,v=v?ln(v):null,v!==null&&(C=Cn(v),v!==C||v.tag!==5&&v.tag!==6)&&(v=null)):(m=null,v=u),m!==v)){if(w=dc,S="onMouseLeave",p="onMouseEnter",h="mouse",(e==="pointerout"||e==="pointerover")&&(w=pc,S="onPointerLeave",p="onPointerEnter",h="pointer"),C=m==null?f:On(m),g=v==null?f:On(v),f=new w(S,h+"leave",m,n,c),f.target=C,f.relatedTarget=g,S=null,ln(c)===u&&(w=new w(p,h+"enter",v,n,c),w.target=g,w.relatedTarget=C,S=w),C=S,m&&v)t:{for(w=m,p=v,h=0,g=w;g;g=kn(g))h++;for(g=0,S=p;S;S=kn(S))g++;for(;0<h-g;)w=kn(w),h--;for(;0<g-h;)p=kn(p),g--;for(;h--;){if(w===p||p!==null&&w===p.alternate)break t;w=kn(w),p=kn(p)}w=null}else w=null;m!==null&&Tc(d,f,m,w,!1),v!==null&&C!==null&&Tc(d,C,v,w,!0)}}e:{if(f=u?On(u):window,m=f.nodeName&&f.nodeName.toLowerCase(),m==="select"||m==="input"&&f.type==="file")var x=i0;else if(vc(f))if(Nh)x=a0;else{x=s0;var k=o0}else(m=f.nodeName)&&m.toLowerCase()==="input"&&(f.type==="checkbox"||f.type==="radio")&&(x=l0);if(x&&(x=x(e,u))){Ah(d,x,n,c);break e}k&&k(e,f,u),e==="focusout"&&(k=f._wrapperState)&&k.controlled&&f.type==="number"&&gl(f,"number",f.value)}switch(k=u?On(u):window,e){case"focusin":(vc(k)||k.contentEditable==="true")&&(_n=k,Nl=u,jr=null);break;case"focusout":jr=Nl=_n=null;break;case"mousedown":Ml=!0;break;case"contextmenu":case"mouseup":case"dragend":Ml=!1,Ec(d,n,c);break;case"selectionchange":if(f0)break;case"keydown":case"keyup":Ec(d,n,c)}var A;if(Fa)e:{switch(e){case"compositionstart":var P="onCompositionStart";break e;case"compositionend":P="onCompositionEnd";break e;case"compositionupdate":P="onCompositionUpdate";break e}P=void 0}else Rn?Th(e,n)&&(P="onCompositionEnd"):e==="keydown"&&n.keyCode===229&&(P="onCompositionStart");P&&(Ph&&n.locale!=="ko"&&(Rn||P!=="onCompositionStart"?P==="onCompositionEnd"&&Rn&&(A=Ch()):(Vt=c,Va="value"in Vt?Vt.value:Vt.textContent,Rn=!0)),k=To(u,P),0<k.length&&(P=new hc(P,e,null,n,c),d.push({event:P,listeners:k}),A?P.data=A:(A=kh(n),A!==null&&(P.data=A)))),(A=Jv?e0(e,n):t0(e,n))&&(u=To(u,"onBeforeInput"),0<u.length&&(c=new hc("onBeforeInput","beforeinput",null,n,c),d.push({event:c,listeners:u}),c.data=A))}jh(d,t)})}function ii(e,t,n){return{instance:e,listener:t,currentTarget:n}}function To(e,t){for(var n=t+"Capture",r=[];e!==null;){var i=e,o=i.stateNode;i.tag===5&&o!==null&&(i=o,o=Zr(e,n),o!=null&&r.unshift(ii(e,o,i)),o=Zr(e,t),o!=null&&r.push(ii(e,o,i))),e=e.return}return r}function kn(e){if(e===null)return null;do e=e.return;while(e&&e.tag!==5);return e||null}function Tc(e,t,n,r,i){for(var o=t._reactName,s=[];n!==null&&n!==r;){var l=n,a=l.alternate,u=l.stateNode;if(a!==null&&a===r)break;l.tag===5&&u!==null&&(l=u,i?(a=Zr(n,o),a!=null&&s.unshift(ii(n,a,l))):i||(a=Zr(n,o),a!=null&&s.push(ii(n,a,l)))),n=n.return}s.length!==0&&e.push({event:t,listeners:s})}var m0=/\r\n?/g,g0=/\u0000|\uFFFD/g;function kc(e){return(typeof e=="string"?e:""+e).replace(m0,`
`).replace(g0,"")}function $i(e,t,n){if(t=kc(t),kc(e)!==t&&n)throw Error(T(425))}function ko(){}var Dl=null,Ll=null;function Rl(e,t){return e==="textarea"||e==="noscript"||typeof t.children=="string"||typeof t.children=="number"||typeof t.dangerouslySetInnerHTML=="object"&&t.dangerouslySetInnerHTML!==null&&t.dangerouslySetInnerHTML.__html!=null}var _l=typeof setTimeout=="function"?setTimeout:void 0,v0=typeof clearTimeout=="function"?clearTimeout:void 0,Ac=typeof Promise=="function"?Promise:void 0,y0=typeof queueMicrotask=="function"?queueMicrotask:typeof Ac<"u"?function(e){return Ac.resolve(null).then(e).catch(w0)}:_l;function w0(e){setTimeout(function(){throw e})}function zs(e,t){var n=t,r=0;do{var i=n.nextSibling;if(e.removeChild(n),i&&i.nodeType===8)if(n=i.data,n==="/$"){if(r===0){e.removeChild(i),ei(t);return}r--}else n!=="$"&&n!=="$?"&&n!=="$!"||r++;n=i}while(n);ei(t)}function zt(e){for(;e!=null;e=e.nextSibling){var t=e.nodeType;if(t===1||t===3)break;if(t===8){if(t=e.data,t==="$"||t==="$!"||t==="$?")break;if(t==="/$")return null}}return e}function Nc(e){e=e.previousSibling;for(var t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="$"||n==="$!"||n==="$?"){if(t===0)return e;t--}else n==="/$"&&t++}e=e.previousSibling}return null}var hr=Math.random().toString(36).slice(2),at="__reactFiber$"+hr,oi="__reactProps$"+hr,Et="__reactContainer$"+hr,Vl="__reactEvents$"+hr,S0="__reactListeners$"+hr,x0="__reactHandles$"+hr;function ln(e){var t=e[at];if(t)return t;for(var n=e.parentNode;n;){if(t=n[Et]||n[at]){if(n=t.alternate,t.child!==null||n!==null&&n.child!==null)for(e=Nc(e);e!==null;){if(n=e[at])return n;e=Nc(e)}return t}e=n,n=e.parentNode}return null}function xi(e){return e=e[at]||e[Et],!e||e.tag!==5&&e.tag!==6&&e.tag!==13&&e.tag!==3?null:e}function On(e){if(e.tag===5||e.tag===6)return e.stateNode;throw Error(T(33))}function is(e){return e[oi]||null}var Ol=[],In=-1;function Yt(e){return{current:e}}function $(e){0>In||(e.current=Ol[In],Ol[In]=null,In--)}function B(e,t){In++,Ol[In]=e.current,e.current=t}var Kt={},Ee=Yt(Kt),De=Yt(!1),gn=Kt;function er(e,t){var n=e.type.contextTypes;if(!n)return Kt;var r=e.stateNode;if(r&&r.__reactInternalMemoizedUnmaskedChildContext===t)return r.__reactInternalMemoizedMaskedChildContext;var i={},o;for(o in n)i[o]=t[o];return r&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=t,e.__reactInternalMemoizedMaskedChildContext=i),i}function Le(e){return e=e.childContextTypes,e!=null}function Ao(){$(De),$(Ee)}function Mc(e,t,n){if(Ee.current!==Kt)throw Error(T(168));B(Ee,t),B(De,n)}function Bh(e,t,n){var r=e.stateNode;if(t=t.childContextTypes,typeof r.getChildContext!="function")return n;r=r.getChildContext();for(var i in r)if(!(i in t))throw Error(T(108,ov(e)||"Unknown",i));return Y({},n,r)}function No(e){return e=(e=e.stateNode)&&e.__reactInternalMemoizedMergedChildContext||Kt,gn=Ee.current,B(Ee,e),B(De,De.current),!0}function Dc(e,t,n){var r=e.stateNode;if(!r)throw Error(T(169));n?(e=Bh(e,t,gn),r.__reactInternalMemoizedMergedChildContext=e,$(De),$(Ee),B(Ee,e)):$(De),B(De,n)}var pt=null,os=!1,Bs=!1;function Uh(e){pt===null?pt=[e]:pt.push(e)}function E0(e){os=!0,Uh(e)}function bt(){if(!Bs&&pt!==null){Bs=!0;var e=0,t=z;try{var n=pt;for(z=1;e<n.length;e++){var r=n[e];do r=r(!0);while(r!==null)}pt=null,os=!1}catch(i){throw pt!==null&&(pt=pt.slice(e+1)),dh(Da,bt),i}finally{z=t,Bs=!1}}return null}var Fn=[],jn=0,Mo=null,Do=0,He=[],We=0,vn=null,gt=1,vt="";function nn(e,t){Fn[jn++]=Do,Fn[jn++]=Mo,Mo=e,Do=t}function $h(e,t,n){He[We++]=gt,He[We++]=vt,He[We++]=vn,vn=e;var r=gt;e=vt;var i=32-rt(r)-1;r&=~(1<<i),n+=1;var o=32-rt(t)+i;if(30<o){var s=i-i%5;o=(r&(1<<s)-1).toString(32),r>>=s,i-=s,gt=1<<32-rt(t)+i|n<<i|r,vt=o+e}else gt=1<<o|n<<i|r,vt=e}function za(e){e.return!==null&&(nn(e,1),$h(e,1,0))}function Ba(e){for(;e===Mo;)Mo=Fn[--jn],Fn[jn]=null,Do=Fn[--jn],Fn[jn]=null;for(;e===vn;)vn=He[--We],He[We]=null,vt=He[--We],He[We]=null,gt=He[--We],He[We]=null}var Oe=null,Ve=null,W=!1,et=null;function Hh(e,t){var n=Ke(5,null,null,0);n.elementType="DELETED",n.stateNode=t,n.return=e,t=e.deletions,t===null?(e.deletions=[n],e.flags|=16):t.push(n)}function Lc(e,t){switch(e.tag){case 5:var n=e.type;return t=t.nodeType!==1||n.toLowerCase()!==t.nodeName.toLowerCase()?null:t,t!==null?(e.stateNode=t,Oe=e,Ve=zt(t.firstChild),!0):!1;case 6:return t=e.pendingProps===""||t.nodeType!==3?null:t,t!==null?(e.stateNode=t,Oe=e,Ve=null,!0):!1;case 13:return t=t.nodeType!==8?null:t,t!==null?(n=vn!==null?{id:gt,overflow:vt}:null,e.memoizedState={dehydrated:t,treeContext:n,retryLane:1073741824},n=Ke(18,null,null,0),n.stateNode=t,n.return=e,e.child=n,Oe=e,Ve=null,!0):!1;default:return!1}}function Il(e){return(e.mode&1)!==0&&(e.flags&128)===0}function Fl(e){if(W){var t=Ve;if(t){var n=t;if(!Lc(e,t)){if(Il(e))throw Error(T(418));t=zt(n.nextSibling);var r=Oe;t&&Lc(e,t)?Hh(r,n):(e.flags=e.flags&-4097|2,W=!1,Oe=e)}}else{if(Il(e))throw Error(T(418));e.flags=e.flags&-4097|2,W=!1,Oe=e}}}function Rc(e){for(e=e.return;e!==null&&e.tag!==5&&e.tag!==3&&e.tag!==13;)e=e.return;Oe=e}function Hi(e){if(e!==Oe)return!1;if(!W)return Rc(e),W=!0,!1;var t;if((t=e.tag!==3)&&!(t=e.tag!==5)&&(t=e.type,t=t!=="head"&&t!=="body"&&!Rl(e.type,e.memoizedProps)),t&&(t=Ve)){if(Il(e))throw Wh(),Error(T(418));for(;t;)Hh(e,t),t=zt(t.nextSibling)}if(Rc(e),e.tag===13){if(e=e.memoizedState,e=e!==null?e.dehydrated:null,!e)throw Error(T(317));e:{for(e=e.nextSibling,t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="/$"){if(t===0){Ve=zt(e.nextSibling);break e}t--}else n!=="$"&&n!=="$!"&&n!=="$?"||t++}e=e.nextSibling}Ve=null}}else Ve=Oe?zt(e.stateNode.nextSibling):null;return!0}function Wh(){for(var e=Ve;e;)e=zt(e.nextSibling)}function tr(){Ve=Oe=null,W=!1}function Ua(e){et===null?et=[e]:et.push(e)}var C0=kt.ReactCurrentBatchConfig;function Cr(e,t,n){if(e=n.ref,e!==null&&typeof e!="function"&&typeof e!="object"){if(n._owner){if(n=n._owner,n){if(n.tag!==1)throw Error(T(309));var r=n.stateNode}if(!r)throw Error(T(147,e));var i=r,o=""+e;return t!==null&&t.ref!==null&&typeof t.ref=="function"&&t.ref._stringRef===o?t.ref:(t=function(s){var l=i.refs;s===null?delete l[o]:l[o]=s},t._stringRef=o,t)}if(typeof e!="string")throw Error(T(284));if(!n._owner)throw Error(T(290,e))}return e}function Wi(e,t){throw e=Object.prototype.toString.call(t),Error(T(31,e==="[object Object]"?"object with keys {"+Object.keys(t).join(", ")+"}":e))}function _c(e){var t=e._init;return t(e._payload)}function Kh(e){function t(p,h){if(e){var g=p.deletions;g===null?(p.deletions=[h],p.flags|=16):g.push(h)}}function n(p,h){if(!e)return null;for(;h!==null;)t(p,h),h=h.sibling;return null}function r(p,h){for(p=new Map;h!==null;)h.key!==null?p.set(h.key,h):p.set(h.index,h),h=h.sibling;return p}function i(p,h){return p=Ht(p,h),p.index=0,p.sibling=null,p}function o(p,h,g){return p.index=g,e?(g=p.alternate,g!==null?(g=g.index,g<h?(p.flags|=2,h):g):(p.flags|=2,h)):(p.flags|=1048576,h)}function s(p){return e&&p.alternate===null&&(p.flags|=2),p}function l(p,h,g,S){return h===null||h.tag!==6?(h=Qs(g,p.mode,S),h.return=p,h):(h=i(h,g),h.return=p,h)}function a(p,h,g,S){var x=g.type;return x===Ln?c(p,h,g.props.children,S,g.key):h!==null&&(h.elementType===x||typeof x=="object"&&x!==null&&x.$$typeof===Dt&&_c(x)===h.type)?(S=i(h,g.props),S.ref=Cr(p,h,g),S.return=p,S):(S=fo(g.type,g.key,g.props,null,p.mode,S),S.ref=Cr(p,h,g),S.return=p,S)}function u(p,h,g,S){return h===null||h.tag!==4||h.stateNode.containerInfo!==g.containerInfo||h.stateNode.implementation!==g.implementation?(h=Xs(g,p.mode,S),h.return=p,h):(h=i(h,g.children||[]),h.return=p,h)}function c(p,h,g,S,x){return h===null||h.tag!==7?(h=hn(g,p.mode,S,x),h.return=p,h):(h=i(h,g),h.return=p,h)}function d(p,h,g){if(typeof h=="string"&&h!==""||typeof h=="number")return h=Qs(""+h,p.mode,g),h.return=p,h;if(typeof h=="object"&&h!==null){switch(h.$$typeof){case _i:return g=fo(h.type,h.key,h.props,null,p.mode,g),g.ref=Cr(p,null,h),g.return=p,g;case Dn:return h=Xs(h,p.mode,g),h.return=p,h;case Dt:var S=h._init;return d(p,S(h._payload),g)}if(Nr(h)||yr(h))return h=hn(h,p.mode,g,null),h.return=p,h;Wi(p,h)}return null}function f(p,h,g,S){var x=h!==null?h.key:null;if(typeof g=="string"&&g!==""||typeof g=="number")return x!==null?null:l(p,h,""+g,S);if(typeof g=="object"&&g!==null){switch(g.$$typeof){case _i:return g.key===x?a(p,h,g,S):null;case Dn:return g.key===x?u(p,h,g,S):null;case Dt:return x=g._init,f(p,h,x(g._payload),S)}if(Nr(g)||yr(g))return x!==null?null:c(p,h,g,S,null);Wi(p,g)}return null}function m(p,h,g,S,x){if(typeof S=="string"&&S!==""||typeof S=="number")return p=p.get(g)||null,l(h,p,""+S,x);if(typeof S=="object"&&S!==null){switch(S.$$typeof){case _i:return p=p.get(S.key===null?g:S.key)||null,a(h,p,S,x);case Dn:return p=p.get(S.key===null?g:S.key)||null,u(h,p,S,x);case Dt:var k=S._init;return m(p,h,g,k(S._payload),x)}if(Nr(S)||yr(S))return p=p.get(g)||null,c(h,p,S,x,null);Wi(h,S)}return null}function v(p,h,g,S){for(var x=null,k=null,A=h,P=h=0,I=null;A!==null&&P<g.length;P++){A.index>P?(I=A,A=null):I=A.sibling;var _=f(p,A,g[P],S);if(_===null){A===null&&(A=I);break}e&&A&&_.alternate===null&&t(p,A),h=o(_,h,P),k===null?x=_:k.sibling=_,k=_,A=I}if(P===g.length)return n(p,A),W&&nn(p,P),x;if(A===null){for(;P<g.length;P++)A=d(p,g[P],S),A!==null&&(h=o(A,h,P),k===null?x=A:k.sibling=A,k=A);return W&&nn(p,P),x}for(A=r(p,A);P<g.length;P++)I=m(A,p,P,g[P],S),I!==null&&(e&&I.alternate!==null&&A.delete(I.key===null?P:I.key),h=o(I,h,P),k===null?x=I:k.sibling=I,k=I);return e&&A.forEach(function(ae){return t(p,ae)}),W&&nn(p,P),x}function w(p,h,g,S){var x=yr(g);if(typeof x!="function")throw Error(T(150));if(g=x.call(g),g==null)throw Error(T(151));for(var k=x=null,A=h,P=h=0,I=null,_=g.next();A!==null&&!_.done;P++,_=g.next()){A.index>P?(I=A,A=null):I=A.sibling;var ae=f(p,A,_.value,S);if(ae===null){A===null&&(A=I);break}e&&A&&ae.alternate===null&&t(p,A),h=o(ae,h,P),k===null?x=ae:k.sibling=ae,k=ae,A=I}if(_.done)return n(p,A),W&&nn(p,P),x;if(A===null){for(;!_.done;P++,_=g.next())_=d(p,_.value,S),_!==null&&(h=o(_,h,P),k===null?x=_:k.sibling=_,k=_);return W&&nn(p,P),x}for(A=r(p,A);!_.done;P++,_=g.next())_=m(A,p,P,_.value,S),_!==null&&(e&&_.alternate!==null&&A.delete(_.key===null?P:_.key),h=o(_,h,P),k===null?x=_:k.sibling=_,k=_);return e&&A.forEach(function(At){return t(p,At)}),W&&nn(p,P),x}function C(p,h,g,S){if(typeof g=="object"&&g!==null&&g.type===Ln&&g.key===null&&(g=g.props.children),typeof g=="object"&&g!==null){switch(g.$$typeof){case _i:e:{for(var x=g.key,k=h;k!==null;){if(k.key===x){if(x=g.type,x===Ln){if(k.tag===7){n(p,k.sibling),h=i(k,g.props.children),h.return=p,p=h;break e}}else if(k.elementType===x||typeof x=="object"&&x!==null&&x.$$typeof===Dt&&_c(x)===k.type){n(p,k.sibling),h=i(k,g.props),h.ref=Cr(p,k,g),h.return=p,p=h;break e}n(p,k);break}else t(p,k);k=k.sibling}g.type===Ln?(h=hn(g.props.children,p.mode,S,g.key),h.return=p,p=h):(S=fo(g.type,g.key,g.props,null,p.mode,S),S.ref=Cr(p,h,g),S.return=p,p=S)}return s(p);case Dn:e:{for(k=g.key;h!==null;){if(h.key===k)if(h.tag===4&&h.stateNode.containerInfo===g.containerInfo&&h.stateNode.implementation===g.implementation){n(p,h.sibling),h=i(h,g.children||[]),h.return=p,p=h;break e}else{n(p,h);break}else t(p,h);h=h.sibling}h=Xs(g,p.mode,S),h.return=p,p=h}return s(p);case Dt:return k=g._init,C(p,h,k(g._payload),S)}if(Nr(g))return v(p,h,g,S);if(yr(g))return w(p,h,g,S);Wi(p,g)}return typeof g=="string"&&g!==""||typeof g=="number"?(g=""+g,h!==null&&h.tag===6?(n(p,h.sibling),h=i(h,g),h.return=p,p=h):(n(p,h),h=Qs(g,p.mode,S),h.return=p,p=h),s(p)):n(p,h)}return C}var nr=Kh(!0),Gh=Kh(!1),Lo=Yt(null),Ro=null,zn=null,$a=null;function Ha(){$a=zn=Ro=null}function Wa(e){var t=Lo.current;$(Lo),e._currentValue=t}function jl(e,t,n){for(;e!==null;){var r=e.alternate;if((e.childLanes&t)!==t?(e.childLanes|=t,r!==null&&(r.childLanes|=t)):r!==null&&(r.childLanes&t)!==t&&(r.childLanes|=t),e===n)break;e=e.return}}function Zn(e,t){Ro=e,$a=zn=null,e=e.dependencies,e!==null&&e.firstContext!==null&&(e.lanes&t&&(Me=!0),e.firstContext=null)}function Xe(e){var t=e._currentValue;if($a!==e)if(e={context:e,memoizedValue:t,next:null},zn===null){if(Ro===null)throw Error(T(308));zn=e,Ro.dependencies={lanes:0,firstContext:e}}else zn=zn.next=e;return t}var an=null;function Ka(e){an===null?an=[e]:an.push(e)}function Qh(e,t,n,r){var i=t.interleaved;return i===null?(n.next=n,Ka(t)):(n.next=i.next,i.next=n),t.interleaved=n,Ct(e,r)}function Ct(e,t){e.lanes|=t;var n=e.alternate;for(n!==null&&(n.lanes|=t),n=e,e=e.return;e!==null;)e.childLanes|=t,n=e.alternate,n!==null&&(n.childLanes|=t),n=e,e=e.return;return n.tag===3?n.stateNode:null}var Lt=!1;function Ga(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,interleaved:null,lanes:0},effects:null}}function Xh(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,effects:e.effects})}function yt(e,t){return{eventTime:e,lane:t,tag:0,payload:null,callback:null,next:null}}function Bt(e,t,n){var r=e.updateQueue;if(r===null)return null;if(r=r.shared,j&2){var i=r.pending;return i===null?t.next=t:(t.next=i.next,i.next=t),r.pending=t,Ct(e,n)}return i=r.interleaved,i===null?(t.next=t,Ka(r)):(t.next=i.next,i.next=t),r.interleaved=t,Ct(e,n)}function oo(e,t,n){if(t=t.updateQueue,t!==null&&(t=t.shared,(n&4194240)!==0)){var r=t.lanes;r&=e.pendingLanes,n|=r,t.lanes=n,La(e,n)}}function Vc(e,t){var n=e.updateQueue,r=e.alternate;if(r!==null&&(r=r.updateQueue,n===r)){var i=null,o=null;if(n=n.firstBaseUpdate,n!==null){do{var s={eventTime:n.eventTime,lane:n.lane,tag:n.tag,payload:n.payload,callback:n.callback,next:null};o===null?i=o=s:o=o.next=s,n=n.next}while(n!==null);o===null?i=o=t:o=o.next=t}else i=o=t;n={baseState:r.baseState,firstBaseUpdate:i,lastBaseUpdate:o,shared:r.shared,effects:r.effects},e.updateQueue=n;return}e=n.lastBaseUpdate,e===null?n.firstBaseUpdate=t:e.next=t,n.lastBaseUpdate=t}function _o(e,t,n,r){var i=e.updateQueue;Lt=!1;var o=i.firstBaseUpdate,s=i.lastBaseUpdate,l=i.shared.pending;if(l!==null){i.shared.pending=null;var a=l,u=a.next;a.next=null,s===null?o=u:s.next=u,s=a;var c=e.alternate;c!==null&&(c=c.updateQueue,l=c.lastBaseUpdate,l!==s&&(l===null?c.firstBaseUpdate=u:l.next=u,c.lastBaseUpdate=a))}if(o!==null){var d=i.baseState;s=0,c=u=a=null,l=o;do{var f=l.lane,m=l.eventTime;if((r&f)===f){c!==null&&(c=c.next={eventTime:m,lane:0,tag:l.tag,payload:l.payload,callback:l.callback,next:null});e:{var v=e,w=l;switch(f=t,m=n,w.tag){case 1:if(v=w.payload,typeof v=="function"){d=v.call(m,d,f);break e}d=v;break e;case 3:v.flags=v.flags&-65537|128;case 0:if(v=w.payload,f=typeof v=="function"?v.call(m,d,f):v,f==null)break e;d=Y({},d,f);break e;case 2:Lt=!0}}l.callback!==null&&l.lane!==0&&(e.flags|=64,f=i.effects,f===null?i.effects=[l]:f.push(l))}else m={eventTime:m,lane:f,tag:l.tag,payload:l.payload,callback:l.callback,next:null},c===null?(u=c=m,a=d):c=c.next=m,s|=f;if(l=l.next,l===null){if(l=i.shared.pending,l===null)break;f=l,l=f.next,f.next=null,i.lastBaseUpdate=f,i.shared.pending=null}}while(1);if(c===null&&(a=d),i.baseState=a,i.firstBaseUpdate=u,i.lastBaseUpdate=c,t=i.shared.interleaved,t!==null){i=t;do s|=i.lane,i=i.next;while(i!==t)}else o===null&&(i.shared.lanes=0);wn|=s,e.lanes=s,e.memoizedState=d}}function Oc(e,t,n){if(e=t.effects,t.effects=null,e!==null)for(t=0;t<e.length;t++){var r=e[t],i=r.callback;if(i!==null){if(r.callback=null,r=n,typeof i!="function")throw Error(T(191,i));i.call(r)}}}var Ei={},ct=Yt(Ei),si=Yt(Ei),li=Yt(Ei);function un(e){if(e===Ei)throw Error(T(174));return e}function Qa(e,t){switch(B(li,t),B(si,e),B(ct,Ei),e=t.nodeType,e){case 9:case 11:t=(t=t.documentElement)?t.namespaceURI:yl(null,"");break;default:e=e===8?t.parentNode:t,t=e.namespaceURI||null,e=e.tagName,t=yl(t,e)}$(ct),B(ct,t)}function rr(){$(ct),$(si),$(li)}function Yh(e){un(li.current);var t=un(ct.current),n=yl(t,e.type);t!==n&&(B(si,e),B(ct,n))}function Xa(e){si.current===e&&($(ct),$(si))}var G=Yt(0);function Vo(e){for(var t=e;t!==null;){if(t.tag===13){var n=t.memoizedState;if(n!==null&&(n=n.dehydrated,n===null||n.data==="$?"||n.data==="$!"))return t}else if(t.tag===19&&t.memoizedProps.revealOrder!==void 0){if(t.flags&128)return t}else if(t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}var Us=[];function Ya(){for(var e=0;e<Us.length;e++)Us[e]._workInProgressVersionPrimary=null;Us.length=0}var so=kt.ReactCurrentDispatcher,$s=kt.ReactCurrentBatchConfig,yn=0,X=null,se=null,ce=null,Oo=!1,zr=!1,ai=0,P0=0;function ve(){throw Error(T(321))}function ba(e,t){if(t===null)return!1;for(var n=0;n<t.length&&n<e.length;n++)if(!ot(e[n],t[n]))return!1;return!0}function Za(e,t,n,r,i,o){if(yn=o,X=t,t.memoizedState=null,t.updateQueue=null,t.lanes=0,so.current=e===null||e.memoizedState===null?N0:M0,e=n(r,i),zr){o=0;do{if(zr=!1,ai=0,25<=o)throw Error(T(301));o+=1,ce=se=null,t.updateQueue=null,so.current=D0,e=n(r,i)}while(zr)}if(so.current=Io,t=se!==null&&se.next!==null,yn=0,ce=se=X=null,Oo=!1,t)throw Error(T(300));return e}function qa(){var e=ai!==0;return ai=0,e}function lt(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return ce===null?X.memoizedState=ce=e:ce=ce.next=e,ce}function Ye(){if(se===null){var e=X.alternate;e=e!==null?e.memoizedState:null}else e=se.next;var t=ce===null?X.memoizedState:ce.next;if(t!==null)ce=t,se=e;else{if(e===null)throw Error(T(310));se=e,e={memoizedState:se.memoizedState,baseState:se.baseState,baseQueue:se.baseQueue,queue:se.queue,next:null},ce===null?X.memoizedState=ce=e:ce=ce.next=e}return ce}function ui(e,t){return typeof t=="function"?t(e):t}function Hs(e){var t=Ye(),n=t.queue;if(n===null)throw Error(T(311));n.lastRenderedReducer=e;var r=se,i=r.baseQueue,o=n.pending;if(o!==null){if(i!==null){var s=i.next;i.next=o.next,o.next=s}r.baseQueue=i=o,n.pending=null}if(i!==null){o=i.next,r=r.baseState;var l=s=null,a=null,u=o;do{var c=u.lane;if((yn&c)===c)a!==null&&(a=a.next={lane:0,action:u.action,hasEagerState:u.hasEagerState,eagerState:u.eagerState,next:null}),r=u.hasEagerState?u.eagerState:e(r,u.action);else{var d={lane:c,action:u.action,hasEagerState:u.hasEagerState,eagerState:u.eagerState,next:null};a===null?(l=a=d,s=r):a=a.next=d,X.lanes|=c,wn|=c}u=u.next}while(u!==null&&u!==o);a===null?s=r:a.next=l,ot(r,t.memoizedState)||(Me=!0),t.memoizedState=r,t.baseState=s,t.baseQueue=a,n.lastRenderedState=r}if(e=n.interleaved,e!==null){i=e;do o=i.lane,X.lanes|=o,wn|=o,i=i.next;while(i!==e)}else i===null&&(n.lanes=0);return[t.memoizedState,n.dispatch]}function Ws(e){var t=Ye(),n=t.queue;if(n===null)throw Error(T(311));n.lastRenderedReducer=e;var r=n.dispatch,i=n.pending,o=t.memoizedState;if(i!==null){n.pending=null;var s=i=i.next;do o=e(o,s.action),s=s.next;while(s!==i);ot(o,t.memoizedState)||(Me=!0),t.memoizedState=o,t.baseQueue===null&&(t.baseState=o),n.lastRenderedState=o}return[o,r]}function bh(){}function Zh(e,t){var n=X,r=Ye(),i=t(),o=!ot(r.memoizedState,i);if(o&&(r.memoizedState=i,Me=!0),r=r.queue,Ja(ep.bind(null,n,r,e),[e]),r.getSnapshot!==t||o||ce!==null&&ce.memoizedState.tag&1){if(n.flags|=2048,ci(9,Jh.bind(null,n,r,i,t),void 0,null),fe===null)throw Error(T(349));yn&30||qh(n,t,i)}return i}function qh(e,t,n){e.flags|=16384,e={getSnapshot:t,value:n},t=X.updateQueue,t===null?(t={lastEffect:null,stores:null},X.updateQueue=t,t.stores=[e]):(n=t.stores,n===null?t.stores=[e]:n.push(e))}function Jh(e,t,n,r){t.value=n,t.getSnapshot=r,tp(t)&&np(e)}function ep(e,t,n){return n(function(){tp(t)&&np(e)})}function tp(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!ot(e,n)}catch{return!0}}function np(e){var t=Ct(e,1);t!==null&&it(t,e,1,-1)}function Ic(e){var t=lt();return typeof e=="function"&&(e=e()),t.memoizedState=t.baseState=e,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:ui,lastRenderedState:e},t.queue=e,e=e.dispatch=A0.bind(null,X,e),[t.memoizedState,e]}function ci(e,t,n,r){return e={tag:e,create:t,destroy:n,deps:r,next:null},t=X.updateQueue,t===null?(t={lastEffect:null,stores:null},X.updateQueue=t,t.lastEffect=e.next=e):(n=t.lastEffect,n===null?t.lastEffect=e.next=e:(r=n.next,n.next=e,e.next=r,t.lastEffect=e)),e}function rp(){return Ye().memoizedState}function lo(e,t,n,r){var i=lt();X.flags|=e,i.memoizedState=ci(1|t,n,void 0,r===void 0?null:r)}function ss(e,t,n,r){var i=Ye();r=r===void 0?null:r;var o=void 0;if(se!==null){var s=se.memoizedState;if(o=s.destroy,r!==null&&ba(r,s.deps)){i.memoizedState=ci(t,n,o,r);return}}X.flags|=e,i.memoizedState=ci(1|t,n,o,r)}function Fc(e,t){return lo(8390656,8,e,t)}function Ja(e,t){return ss(2048,8,e,t)}function ip(e,t){return ss(4,2,e,t)}function op(e,t){return ss(4,4,e,t)}function sp(e,t){if(typeof t=="function")return e=e(),t(e),function(){t(null)};if(t!=null)return e=e(),t.current=e,function(){t.current=null}}function lp(e,t,n){return n=n!=null?n.concat([e]):null,ss(4,4,sp.bind(null,t,e),n)}function eu(){}function ap(e,t){var n=Ye();t=t===void 0?null:t;var r=n.memoizedState;return r!==null&&t!==null&&ba(t,r[1])?r[0]:(n.memoizedState=[e,t],e)}function up(e,t){var n=Ye();t=t===void 0?null:t;var r=n.memoizedState;return r!==null&&t!==null&&ba(t,r[1])?r[0]:(e=e(),n.memoizedState=[e,t],e)}function cp(e,t,n){return yn&21?(ot(n,t)||(n=mh(),X.lanes|=n,wn|=n,e.baseState=!0),t):(e.baseState&&(e.baseState=!1,Me=!0),e.memoizedState=n)}function T0(e,t){var n=z;z=n!==0&&4>n?n:4,e(!0);var r=$s.transition;$s.transition={};try{e(!1),t()}finally{z=n,$s.transition=r}}function fp(){return Ye().memoizedState}function k0(e,t,n){var r=$t(e);if(n={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null},dp(e))hp(t,n);else if(n=Qh(e,t,n,r),n!==null){var i=Pe();it(n,e,r,i),pp(n,t,r)}}function A0(e,t,n){var r=$t(e),i={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null};if(dp(e))hp(t,i);else{var o=e.alternate;if(e.lanes===0&&(o===null||o.lanes===0)&&(o=t.lastRenderedReducer,o!==null))try{var s=t.lastRenderedState,l=o(s,n);if(i.hasEagerState=!0,i.eagerState=l,ot(l,s)){var a=t.interleaved;a===null?(i.next=i,Ka(t)):(i.next=a.next,a.next=i),t.interleaved=i;return}}catch{}finally{}n=Qh(e,t,i,r),n!==null&&(i=Pe(),it(n,e,r,i),pp(n,t,r))}}function dp(e){var t=e.alternate;return e===X||t!==null&&t===X}function hp(e,t){zr=Oo=!0;var n=e.pending;n===null?t.next=t:(t.next=n.next,n.next=t),e.pending=t}function pp(e,t,n){if(n&4194240){var r=t.lanes;r&=e.pendingLanes,n|=r,t.lanes=n,La(e,n)}}var Io={readContext:Xe,useCallback:ve,useContext:ve,useEffect:ve,useImperativeHandle:ve,useInsertionEffect:ve,useLayoutEffect:ve,useMemo:ve,useReducer:ve,useRef:ve,useState:ve,useDebugValue:ve,useDeferredValue:ve,useTransition:ve,useMutableSource:ve,useSyncExternalStore:ve,useId:ve,unstable_isNewReconciler:!1},N0={readContext:Xe,useCallback:function(e,t){return lt().memoizedState=[e,t===void 0?null:t],e},useContext:Xe,useEffect:Fc,useImperativeHandle:function(e,t,n){return n=n!=null?n.concat([e]):null,lo(4194308,4,sp.bind(null,t,e),n)},useLayoutEffect:function(e,t){return lo(4194308,4,e,t)},useInsertionEffect:function(e,t){return lo(4,2,e,t)},useMemo:function(e,t){var n=lt();return t=t===void 0?null:t,e=e(),n.memoizedState=[e,t],e},useReducer:function(e,t,n){var r=lt();return t=n!==void 0?n(t):t,r.memoizedState=r.baseState=t,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:t},r.queue=e,e=e.dispatch=k0.bind(null,X,e),[r.memoizedState,e]},useRef:function(e){var t=lt();return e={current:e},t.memoizedState=e},useState:Ic,useDebugValue:eu,useDeferredValue:function(e){return lt().memoizedState=e},useTransition:function(){var e=Ic(!1),t=e[0];return e=T0.bind(null,e[1]),lt().memoizedState=e,[t,e]},useMutableSource:function(){},useSyncExternalStore:function(e,t,n){var r=X,i=lt();if(W){if(n===void 0)throw Error(T(407));n=n()}else{if(n=t(),fe===null)throw Error(T(349));yn&30||qh(r,t,n)}i.memoizedState=n;var o={value:n,getSnapshot:t};return i.queue=o,Fc(ep.bind(null,r,o,e),[e]),r.flags|=2048,ci(9,Jh.bind(null,r,o,n,t),void 0,null),n},useId:function(){var e=lt(),t=fe.identifierPrefix;if(W){var n=vt,r=gt;n=(r&~(1<<32-rt(r)-1)).toString(32)+n,t=":"+t+"R"+n,n=ai++,0<n&&(t+="H"+n.toString(32)),t+=":"}else n=P0++,t=":"+t+"r"+n.toString(32)+":";return e.memoizedState=t},unstable_isNewReconciler:!1},M0={readContext:Xe,useCallback:ap,useContext:Xe,useEffect:Ja,useImperativeHandle:lp,useInsertionEffect:ip,useLayoutEffect:op,useMemo:up,useReducer:Hs,useRef:rp,useState:function(){return Hs(ui)},useDebugValue:eu,useDeferredValue:function(e){var t=Ye();return cp(t,se.memoizedState,e)},useTransition:function(){var e=Hs(ui)[0],t=Ye().memoizedState;return[e,t]},useMutableSource:bh,useSyncExternalStore:Zh,useId:fp,unstable_isNewReconciler:!1},D0={readContext:Xe,useCallback:ap,useContext:Xe,useEffect:Ja,useImperativeHandle:lp,useInsertionEffect:ip,useLayoutEffect:op,useMemo:up,useReducer:Ws,useRef:rp,useState:function(){return Ws(ui)},useDebugValue:eu,useDeferredValue:function(e){var t=Ye();return se===null?t.memoizedState=e:cp(t,se.memoizedState,e)},useTransition:function(){var e=Ws(ui)[0],t=Ye().memoizedState;return[e,t]},useMutableSource:bh,useSyncExternalStore:Zh,useId:fp,unstable_isNewReconciler:!1};function qe(e,t){if(e&&e.defaultProps){t=Y({},t),e=e.defaultProps;for(var n in e)t[n]===void 0&&(t[n]=e[n]);return t}return t}function zl(e,t,n,r){t=e.memoizedState,n=n(r,t),n=n==null?t:Y({},t,n),e.memoizedState=n,e.lanes===0&&(e.updateQueue.baseState=n)}var ls={isMounted:function(e){return(e=e._reactInternals)?Cn(e)===e:!1},enqueueSetState:function(e,t,n){e=e._reactInternals;var r=Pe(),i=$t(e),o=yt(r,i);o.payload=t,n!=null&&(o.callback=n),t=Bt(e,o,i),t!==null&&(it(t,e,i,r),oo(t,e,i))},enqueueReplaceState:function(e,t,n){e=e._reactInternals;var r=Pe(),i=$t(e),o=yt(r,i);o.tag=1,o.payload=t,n!=null&&(o.callback=n),t=Bt(e,o,i),t!==null&&(it(t,e,i,r),oo(t,e,i))},enqueueForceUpdate:function(e,t){e=e._reactInternals;var n=Pe(),r=$t(e),i=yt(n,r);i.tag=2,t!=null&&(i.callback=t),t=Bt(e,i,r),t!==null&&(it(t,e,r,n),oo(t,e,r))}};function jc(e,t,n,r,i,o,s){return e=e.stateNode,typeof e.shouldComponentUpdate=="function"?e.shouldComponentUpdate(r,o,s):t.prototype&&t.prototype.isPureReactComponent?!ni(n,r)||!ni(i,o):!0}function mp(e,t,n){var r=!1,i=Kt,o=t.contextType;return typeof o=="object"&&o!==null?o=Xe(o):(i=Le(t)?gn:Ee.current,r=t.contextTypes,o=(r=r!=null)?er(e,i):Kt),t=new t(n,o),e.memoizedState=t.state!==null&&t.state!==void 0?t.state:null,t.updater=ls,e.stateNode=t,t._reactInternals=e,r&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=i,e.__reactInternalMemoizedMaskedChildContext=o),t}function zc(e,t,n,r){e=t.state,typeof t.componentWillReceiveProps=="function"&&t.componentWillReceiveProps(n,r),typeof t.UNSAFE_componentWillReceiveProps=="function"&&t.UNSAFE_componentWillReceiveProps(n,r),t.state!==e&&ls.enqueueReplaceState(t,t.state,null)}function Bl(e,t,n,r){var i=e.stateNode;i.props=n,i.state=e.memoizedState,i.refs={},Ga(e);var o=t.contextType;typeof o=="object"&&o!==null?i.context=Xe(o):(o=Le(t)?gn:Ee.current,i.context=er(e,o)),i.state=e.memoizedState,o=t.getDerivedStateFromProps,typeof o=="function"&&(zl(e,t,o,n),i.state=e.memoizedState),typeof t.getDerivedStateFromProps=="function"||typeof i.getSnapshotBeforeUpdate=="function"||typeof i.UNSAFE_componentWillMount!="function"&&typeof i.componentWillMount!="function"||(t=i.state,typeof i.componentWillMount=="function"&&i.componentWillMount(),typeof i.UNSAFE_componentWillMount=="function"&&i.UNSAFE_componentWillMount(),t!==i.state&&ls.enqueueReplaceState(i,i.state,null),_o(e,n,i,r),i.state=e.memoizedState),typeof i.componentDidMount=="function"&&(e.flags|=4194308)}function ir(e,t){try{var n="",r=t;do n+=iv(r),r=r.return;while(r);var i=n}catch(o){i=`
Error generating stack: `+o.message+`
`+o.stack}return{value:e,source:t,stack:i,digest:null}}function Ks(e,t,n){return{value:e,source:null,stack:n??null,digest:t??null}}function Ul(e,t){try{console.error(t.value)}catch(n){setTimeout(function(){throw n})}}var L0=typeof WeakMap=="function"?WeakMap:Map;function gp(e,t,n){n=yt(-1,n),n.tag=3,n.payload={element:null};var r=t.value;return n.callback=function(){jo||(jo=!0,Zl=r),Ul(e,t)},n}function vp(e,t,n){n=yt(-1,n),n.tag=3;var r=e.type.getDerivedStateFromError;if(typeof r=="function"){var i=t.value;n.payload=function(){return r(i)},n.callback=function(){Ul(e,t)}}var o=e.stateNode;return o!==null&&typeof o.componentDidCatch=="function"&&(n.callback=function(){Ul(e,t),typeof r!="function"&&(Ut===null?Ut=new Set([this]):Ut.add(this));var s=t.stack;this.componentDidCatch(t.value,{componentStack:s!==null?s:""})}),n}function Bc(e,t,n){var r=e.pingCache;if(r===null){r=e.pingCache=new L0;var i=new Set;r.set(t,i)}else i=r.get(t),i===void 0&&(i=new Set,r.set(t,i));i.has(n)||(i.add(n),e=K0.bind(null,e,t,n),t.then(e,e))}function Uc(e){do{var t;if((t=e.tag===13)&&(t=e.memoizedState,t=t!==null?t.dehydrated!==null:!0),t)return e;e=e.return}while(e!==null);return null}function $c(e,t,n,r,i){return e.mode&1?(e.flags|=65536,e.lanes=i,e):(e===t?e.flags|=65536:(e.flags|=128,n.flags|=131072,n.flags&=-52805,n.tag===1&&(n.alternate===null?n.tag=17:(t=yt(-1,1),t.tag=2,Bt(n,t,1))),n.lanes|=1),e)}var R0=kt.ReactCurrentOwner,Me=!1;function Ce(e,t,n,r){t.child=e===null?Gh(t,null,n,r):nr(t,e.child,n,r)}function Hc(e,t,n,r,i){n=n.render;var o=t.ref;return Zn(t,i),r=Za(e,t,n,r,o,i),n=qa(),e!==null&&!Me?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~i,Pt(e,t,i)):(W&&n&&za(t),t.flags|=1,Ce(e,t,r,i),t.child)}function Wc(e,t,n,r,i){if(e===null){var o=n.type;return typeof o=="function"&&!au(o)&&o.defaultProps===void 0&&n.compare===null&&n.defaultProps===void 0?(t.tag=15,t.type=o,yp(e,t,o,r,i)):(e=fo(n.type,null,r,t,t.mode,i),e.ref=t.ref,e.return=t,t.child=e)}if(o=e.child,!(e.lanes&i)){var s=o.memoizedProps;if(n=n.compare,n=n!==null?n:ni,n(s,r)&&e.ref===t.ref)return Pt(e,t,i)}return t.flags|=1,e=Ht(o,r),e.ref=t.ref,e.return=t,t.child=e}function yp(e,t,n,r,i){if(e!==null){var o=e.memoizedProps;if(ni(o,r)&&e.ref===t.ref)if(Me=!1,t.pendingProps=r=o,(e.lanes&i)!==0)e.flags&131072&&(Me=!0);else return t.lanes=e.lanes,Pt(e,t,i)}return $l(e,t,n,r,i)}function wp(e,t,n){var r=t.pendingProps,i=r.children,o=e!==null?e.memoizedState:null;if(r.mode==="hidden")if(!(t.mode&1))t.memoizedState={baseLanes:0,cachePool:null,transitions:null},B(Un,_e),_e|=n;else{if(!(n&1073741824))return e=o!==null?o.baseLanes|n:n,t.lanes=t.childLanes=1073741824,t.memoizedState={baseLanes:e,cachePool:null,transitions:null},t.updateQueue=null,B(Un,_e),_e|=e,null;t.memoizedState={baseLanes:0,cachePool:null,transitions:null},r=o!==null?o.baseLanes:n,B(Un,_e),_e|=r}else o!==null?(r=o.baseLanes|n,t.memoizedState=null):r=n,B(Un,_e),_e|=r;return Ce(e,t,i,n),t.child}function Sp(e,t){var n=t.ref;(e===null&&n!==null||e!==null&&e.ref!==n)&&(t.flags|=512,t.flags|=2097152)}function $l(e,t,n,r,i){var o=Le(n)?gn:Ee.current;return o=er(t,o),Zn(t,i),n=Za(e,t,n,r,o,i),r=qa(),e!==null&&!Me?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~i,Pt(e,t,i)):(W&&r&&za(t),t.flags|=1,Ce(e,t,n,i),t.child)}function Kc(e,t,n,r,i){if(Le(n)){var o=!0;No(t)}else o=!1;if(Zn(t,i),t.stateNode===null)ao(e,t),mp(t,n,r),Bl(t,n,r,i),r=!0;else if(e===null){var s=t.stateNode,l=t.memoizedProps;s.props=l;var a=s.context,u=n.contextType;typeof u=="object"&&u!==null?u=Xe(u):(u=Le(n)?gn:Ee.current,u=er(t,u));var c=n.getDerivedStateFromProps,d=typeof c=="function"||typeof s.getSnapshotBeforeUpdate=="function";d||typeof s.UNSAFE_componentWillReceiveProps!="function"&&typeof s.componentWillReceiveProps!="function"||(l!==r||a!==u)&&zc(t,s,r,u),Lt=!1;var f=t.memoizedState;s.state=f,_o(t,r,s,i),a=t.memoizedState,l!==r||f!==a||De.current||Lt?(typeof c=="function"&&(zl(t,n,c,r),a=t.memoizedState),(l=Lt||jc(t,n,l,r,f,a,u))?(d||typeof s.UNSAFE_componentWillMount!="function"&&typeof s.componentWillMount!="function"||(typeof s.componentWillMount=="function"&&s.componentWillMount(),typeof s.UNSAFE_componentWillMount=="function"&&s.UNSAFE_componentWillMount()),typeof s.componentDidMount=="function"&&(t.flags|=4194308)):(typeof s.componentDidMount=="function"&&(t.flags|=4194308),t.memoizedProps=r,t.memoizedState=a),s.props=r,s.state=a,s.context=u,r=l):(typeof s.componentDidMount=="function"&&(t.flags|=4194308),r=!1)}else{s=t.stateNode,Xh(e,t),l=t.memoizedProps,u=t.type===t.elementType?l:qe(t.type,l),s.props=u,d=t.pendingProps,f=s.context,a=n.contextType,typeof a=="object"&&a!==null?a=Xe(a):(a=Le(n)?gn:Ee.current,a=er(t,a));var m=n.getDerivedStateFromProps;(c=typeof m=="function"||typeof s.getSnapshotBeforeUpdate=="function")||typeof s.UNSAFE_componentWillReceiveProps!="function"&&typeof s.componentWillReceiveProps!="function"||(l!==d||f!==a)&&zc(t,s,r,a),Lt=!1,f=t.memoizedState,s.state=f,_o(t,r,s,i);var v=t.memoizedState;l!==d||f!==v||De.current||Lt?(typeof m=="function"&&(zl(t,n,m,r),v=t.memoizedState),(u=Lt||jc(t,n,u,r,f,v,a)||!1)?(c||typeof s.UNSAFE_componentWillUpdate!="function"&&typeof s.componentWillUpdate!="function"||(typeof s.componentWillUpdate=="function"&&s.componentWillUpdate(r,v,a),typeof s.UNSAFE_componentWillUpdate=="function"&&s.UNSAFE_componentWillUpdate(r,v,a)),typeof s.componentDidUpdate=="function"&&(t.flags|=4),typeof s.getSnapshotBeforeUpdate=="function"&&(t.flags|=1024)):(typeof s.componentDidUpdate!="function"||l===e.memoizedProps&&f===e.memoizedState||(t.flags|=4),typeof s.getSnapshotBeforeUpdate!="function"||l===e.memoizedProps&&f===e.memoizedState||(t.flags|=1024),t.memoizedProps=r,t.memoizedState=v),s.props=r,s.state=v,s.context=a,r=u):(typeof s.componentDidUpdate!="function"||l===e.memoizedProps&&f===e.memoizedState||(t.flags|=4),typeof s.getSnapshotBeforeUpdate!="function"||l===e.memoizedProps&&f===e.memoizedState||(t.flags|=1024),r=!1)}return Hl(e,t,n,r,o,i)}function Hl(e,t,n,r,i,o){Sp(e,t);var s=(t.flags&128)!==0;if(!r&&!s)return i&&Dc(t,n,!1),Pt(e,t,o);r=t.stateNode,R0.current=t;var l=s&&typeof n.getDerivedStateFromError!="function"?null:r.render();return t.flags|=1,e!==null&&s?(t.child=nr(t,e.child,null,o),t.child=nr(t,null,l,o)):Ce(e,t,l,o),t.memoizedState=r.state,i&&Dc(t,n,!0),t.child}function xp(e){var t=e.stateNode;t.pendingContext?Mc(e,t.pendingContext,t.pendingContext!==t.context):t.context&&Mc(e,t.context,!1),Qa(e,t.containerInfo)}function Gc(e,t,n,r,i){return tr(),Ua(i),t.flags|=256,Ce(e,t,n,r),t.child}var Wl={dehydrated:null,treeContext:null,retryLane:0};function Kl(e){return{baseLanes:e,cachePool:null,transitions:null}}function Ep(e,t,n){var r=t.pendingProps,i=G.current,o=!1,s=(t.flags&128)!==0,l;if((l=s)||(l=e!==null&&e.memoizedState===null?!1:(i&2)!==0),l?(o=!0,t.flags&=-129):(e===null||e.memoizedState!==null)&&(i|=1),B(G,i&1),e===null)return Fl(t),e=t.memoizedState,e!==null&&(e=e.dehydrated,e!==null)?(t.mode&1?e.data==="$!"?t.lanes=8:t.lanes=1073741824:t.lanes=1,null):(s=r.children,e=r.fallback,o?(r=t.mode,o=t.child,s={mode:"hidden",children:s},!(r&1)&&o!==null?(o.childLanes=0,o.pendingProps=s):o=cs(s,r,0,null),e=hn(e,r,n,null),o.return=t,e.return=t,o.sibling=e,t.child=o,t.child.memoizedState=Kl(n),t.memoizedState=Wl,e):tu(t,s));if(i=e.memoizedState,i!==null&&(l=i.dehydrated,l!==null))return _0(e,t,s,r,l,i,n);if(o){o=r.fallback,s=t.mode,i=e.child,l=i.sibling;var a={mode:"hidden",children:r.children};return!(s&1)&&t.child!==i?(r=t.child,r.childLanes=0,r.pendingProps=a,t.deletions=null):(r=Ht(i,a),r.subtreeFlags=i.subtreeFlags&14680064),l!==null?o=Ht(l,o):(o=hn(o,s,n,null),o.flags|=2),o.return=t,r.return=t,r.sibling=o,t.child=r,r=o,o=t.child,s=e.child.memoizedState,s=s===null?Kl(n):{baseLanes:s.baseLanes|n,cachePool:null,transitions:s.transitions},o.memoizedState=s,o.childLanes=e.childLanes&~n,t.memoizedState=Wl,r}return o=e.child,e=o.sibling,r=Ht(o,{mode:"visible",children:r.children}),!(t.mode&1)&&(r.lanes=n),r.return=t,r.sibling=null,e!==null&&(n=t.deletions,n===null?(t.deletions=[e],t.flags|=16):n.push(e)),t.child=r,t.memoizedState=null,r}function tu(e,t){return t=cs({mode:"visible",children:t},e.mode,0,null),t.return=e,e.child=t}function Ki(e,t,n,r){return r!==null&&Ua(r),nr(t,e.child,null,n),e=tu(t,t.pendingProps.children),e.flags|=2,t.memoizedState=null,e}function _0(e,t,n,r,i,o,s){if(n)return t.flags&256?(t.flags&=-257,r=Ks(Error(T(422))),Ki(e,t,s,r)):t.memoizedState!==null?(t.child=e.child,t.flags|=128,null):(o=r.fallback,i=t.mode,r=cs({mode:"visible",children:r.children},i,0,null),o=hn(o,i,s,null),o.flags|=2,r.return=t,o.return=t,r.sibling=o,t.child=r,t.mode&1&&nr(t,e.child,null,s),t.child.memoizedState=Kl(s),t.memoizedState=Wl,o);if(!(t.mode&1))return Ki(e,t,s,null);if(i.data==="$!"){if(r=i.nextSibling&&i.nextSibling.dataset,r)var l=r.dgst;return r=l,o=Error(T(419)),r=Ks(o,r,void 0),Ki(e,t,s,r)}if(l=(s&e.childLanes)!==0,Me||l){if(r=fe,r!==null){switch(s&-s){case 4:i=2;break;case 16:i=8;break;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:i=32;break;case 536870912:i=268435456;break;default:i=0}i=i&(r.suspendedLanes|s)?0:i,i!==0&&i!==o.retryLane&&(o.retryLane=i,Ct(e,i),it(r,e,i,-1))}return lu(),r=Ks(Error(T(421))),Ki(e,t,s,r)}return i.data==="$?"?(t.flags|=128,t.child=e.child,t=G0.bind(null,e),i._reactRetry=t,null):(e=o.treeContext,Ve=zt(i.nextSibling),Oe=t,W=!0,et=null,e!==null&&(He[We++]=gt,He[We++]=vt,He[We++]=vn,gt=e.id,vt=e.overflow,vn=t),t=tu(t,r.children),t.flags|=4096,t)}function Qc(e,t,n){e.lanes|=t;var r=e.alternate;r!==null&&(r.lanes|=t),jl(e.return,t,n)}function Gs(e,t,n,r,i){var o=e.memoizedState;o===null?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:r,tail:n,tailMode:i}:(o.isBackwards=t,o.rendering=null,o.renderingStartTime=0,o.last=r,o.tail=n,o.tailMode=i)}function Cp(e,t,n){var r=t.pendingProps,i=r.revealOrder,o=r.tail;if(Ce(e,t,r.children,n),r=G.current,r&2)r=r&1|2,t.flags|=128;else{if(e!==null&&e.flags&128)e:for(e=t.child;e!==null;){if(e.tag===13)e.memoizedState!==null&&Qc(e,n,t);else if(e.tag===19)Qc(e,n,t);else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break e;for(;e.sibling===null;){if(e.return===null||e.return===t)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}r&=1}if(B(G,r),!(t.mode&1))t.memoizedState=null;else switch(i){case"forwards":for(n=t.child,i=null;n!==null;)e=n.alternate,e!==null&&Vo(e)===null&&(i=n),n=n.sibling;n=i,n===null?(i=t.child,t.child=null):(i=n.sibling,n.sibling=null),Gs(t,!1,i,n,o);break;case"backwards":for(n=null,i=t.child,t.child=null;i!==null;){if(e=i.alternate,e!==null&&Vo(e)===null){t.child=i;break}e=i.sibling,i.sibling=n,n=i,i=e}Gs(t,!0,n,null,o);break;case"together":Gs(t,!1,null,null,void 0);break;default:t.memoizedState=null}return t.child}function ao(e,t){!(t.mode&1)&&e!==null&&(e.alternate=null,t.alternate=null,t.flags|=2)}function Pt(e,t,n){if(e!==null&&(t.dependencies=e.dependencies),wn|=t.lanes,!(n&t.childLanes))return null;if(e!==null&&t.child!==e.child)throw Error(T(153));if(t.child!==null){for(e=t.child,n=Ht(e,e.pendingProps),t.child=n,n.return=t;e.sibling!==null;)e=e.sibling,n=n.sibling=Ht(e,e.pendingProps),n.return=t;n.sibling=null}return t.child}function V0(e,t,n){switch(t.tag){case 3:xp(t),tr();break;case 5:Yh(t);break;case 1:Le(t.type)&&No(t);break;case 4:Qa(t,t.stateNode.containerInfo);break;case 10:var r=t.type._context,i=t.memoizedProps.value;B(Lo,r._currentValue),r._currentValue=i;break;case 13:if(r=t.memoizedState,r!==null)return r.dehydrated!==null?(B(G,G.current&1),t.flags|=128,null):n&t.child.childLanes?Ep(e,t,n):(B(G,G.current&1),e=Pt(e,t,n),e!==null?e.sibling:null);B(G,G.current&1);break;case 19:if(r=(n&t.childLanes)!==0,e.flags&128){if(r)return Cp(e,t,n);t.flags|=128}if(i=t.memoizedState,i!==null&&(i.rendering=null,i.tail=null,i.lastEffect=null),B(G,G.current),r)break;return null;case 22:case 23:return t.lanes=0,wp(e,t,n)}return Pt(e,t,n)}var Pp,Gl,Tp,kp;Pp=function(e,t){for(var n=t.child;n!==null;){if(n.tag===5||n.tag===6)e.appendChild(n.stateNode);else if(n.tag!==4&&n.child!==null){n.child.return=n,n=n.child;continue}if(n===t)break;for(;n.sibling===null;){if(n.return===null||n.return===t)return;n=n.return}n.sibling.return=n.return,n=n.sibling}};Gl=function(){};Tp=function(e,t,n,r){var i=e.memoizedProps;if(i!==r){e=t.stateNode,un(ct.current);var o=null;switch(n){case"input":i=pl(e,i),r=pl(e,r),o=[];break;case"select":i=Y({},i,{value:void 0}),r=Y({},r,{value:void 0}),o=[];break;case"textarea":i=vl(e,i),r=vl(e,r),o=[];break;default:typeof i.onClick!="function"&&typeof r.onClick=="function"&&(e.onclick=ko)}wl(n,r);var s;n=null;for(u in i)if(!r.hasOwnProperty(u)&&i.hasOwnProperty(u)&&i[u]!=null)if(u==="style"){var l=i[u];for(s in l)l.hasOwnProperty(s)&&(n||(n={}),n[s]="")}else u!=="dangerouslySetInnerHTML"&&u!=="children"&&u!=="suppressContentEditableWarning"&&u!=="suppressHydrationWarning"&&u!=="autoFocus"&&(Yr.hasOwnProperty(u)?o||(o=[]):(o=o||[]).push(u,null));for(u in r){var a=r[u];if(l=i!=null?i[u]:void 0,r.hasOwnProperty(u)&&a!==l&&(a!=null||l!=null))if(u==="style")if(l){for(s in l)!l.hasOwnProperty(s)||a&&a.hasOwnProperty(s)||(n||(n={}),n[s]="");for(s in a)a.hasOwnProperty(s)&&l[s]!==a[s]&&(n||(n={}),n[s]=a[s])}else n||(o||(o=[]),o.push(u,n)),n=a;else u==="dangerouslySetInnerHTML"?(a=a?a.__html:void 0,l=l?l.__html:void 0,a!=null&&l!==a&&(o=o||[]).push(u,a)):u==="children"?typeof a!="string"&&typeof a!="number"||(o=o||[]).push(u,""+a):u!=="suppressContentEditableWarning"&&u!=="suppressHydrationWarning"&&(Yr.hasOwnProperty(u)?(a!=null&&u==="onScroll"&&U("scroll",e),o||l===a||(o=[])):(o=o||[]).push(u,a))}n&&(o=o||[]).push("style",n);var u=o;(t.updateQueue=u)&&(t.flags|=4)}};kp=function(e,t,n,r){n!==r&&(t.flags|=4)};function Pr(e,t){if(!W)switch(e.tailMode){case"hidden":t=e.tail;for(var n=null;t!==null;)t.alternate!==null&&(n=t),t=t.sibling;n===null?e.tail=null:n.sibling=null;break;case"collapsed":n=e.tail;for(var r=null;n!==null;)n.alternate!==null&&(r=n),n=n.sibling;r===null?t||e.tail===null?e.tail=null:e.tail.sibling=null:r.sibling=null}}function ye(e){var t=e.alternate!==null&&e.alternate.child===e.child,n=0,r=0;if(t)for(var i=e.child;i!==null;)n|=i.lanes|i.childLanes,r|=i.subtreeFlags&14680064,r|=i.flags&14680064,i.return=e,i=i.sibling;else for(i=e.child;i!==null;)n|=i.lanes|i.childLanes,r|=i.subtreeFlags,r|=i.flags,i.return=e,i=i.sibling;return e.subtreeFlags|=r,e.childLanes=n,t}function O0(e,t,n){var r=t.pendingProps;switch(Ba(t),t.tag){case 2:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return ye(t),null;case 1:return Le(t.type)&&Ao(),ye(t),null;case 3:return r=t.stateNode,rr(),$(De),$(Ee),Ya(),r.pendingContext&&(r.context=r.pendingContext,r.pendingContext=null),(e===null||e.child===null)&&(Hi(t)?t.flags|=4:e===null||e.memoizedState.isDehydrated&&!(t.flags&256)||(t.flags|=1024,et!==null&&(ea(et),et=null))),Gl(e,t),ye(t),null;case 5:Xa(t);var i=un(li.current);if(n=t.type,e!==null&&t.stateNode!=null)Tp(e,t,n,r,i),e.ref!==t.ref&&(t.flags|=512,t.flags|=2097152);else{if(!r){if(t.stateNode===null)throw Error(T(166));return ye(t),null}if(e=un(ct.current),Hi(t)){r=t.stateNode,n=t.type;var o=t.memoizedProps;switch(r[at]=t,r[oi]=o,e=(t.mode&1)!==0,n){case"dialog":U("cancel",r),U("close",r);break;case"iframe":case"object":case"embed":U("load",r);break;case"video":case"audio":for(i=0;i<Dr.length;i++)U(Dr[i],r);break;case"source":U("error",r);break;case"img":case"image":case"link":U("error",r),U("load",r);break;case"details":U("toggle",r);break;case"input":nc(r,o),U("invalid",r);break;case"select":r._wrapperState={wasMultiple:!!o.multiple},U("invalid",r);break;case"textarea":ic(r,o),U("invalid",r)}wl(n,o),i=null;for(var s in o)if(o.hasOwnProperty(s)){var l=o[s];s==="children"?typeof l=="string"?r.textContent!==l&&(o.suppressHydrationWarning!==!0&&$i(r.textContent,l,e),i=["children",l]):typeof l=="number"&&r.textContent!==""+l&&(o.suppressHydrationWarning!==!0&&$i(r.textContent,l,e),i=["children",""+l]):Yr.hasOwnProperty(s)&&l!=null&&s==="onScroll"&&U("scroll",r)}switch(n){case"input":Vi(r),rc(r,o,!0);break;case"textarea":Vi(r),oc(r);break;case"select":case"option":break;default:typeof o.onClick=="function"&&(r.onclick=ko)}r=i,t.updateQueue=r,r!==null&&(t.flags|=4)}else{s=i.nodeType===9?i:i.ownerDocument,e==="http://www.w3.org/1999/xhtml"&&(e=eh(n)),e==="http://www.w3.org/1999/xhtml"?n==="script"?(e=s.createElement("div"),e.innerHTML="<script><\/script>",e=e.removeChild(e.firstChild)):typeof r.is=="string"?e=s.createElement(n,{is:r.is}):(e=s.createElement(n),n==="select"&&(s=e,r.multiple?s.multiple=!0:r.size&&(s.size=r.size))):e=s.createElementNS(e,n),e[at]=t,e[oi]=r,Pp(e,t,!1,!1),t.stateNode=e;e:{switch(s=Sl(n,r),n){case"dialog":U("cancel",e),U("close",e),i=r;break;case"iframe":case"object":case"embed":U("load",e),i=r;break;case"video":case"audio":for(i=0;i<Dr.length;i++)U(Dr[i],e);i=r;break;case"source":U("error",e),i=r;break;case"img":case"image":case"link":U("error",e),U("load",e),i=r;break;case"details":U("toggle",e),i=r;break;case"input":nc(e,r),i=pl(e,r),U("invalid",e);break;case"option":i=r;break;case"select":e._wrapperState={wasMultiple:!!r.multiple},i=Y({},r,{value:void 0}),U("invalid",e);break;case"textarea":ic(e,r),i=vl(e,r),U("invalid",e);break;default:i=r}wl(n,i),l=i;for(o in l)if(l.hasOwnProperty(o)){var a=l[o];o==="style"?rh(e,a):o==="dangerouslySetInnerHTML"?(a=a?a.__html:void 0,a!=null&&th(e,a)):o==="children"?typeof a=="string"?(n!=="textarea"||a!=="")&&br(e,a):typeof a=="number"&&br(e,""+a):o!=="suppressContentEditableWarning"&&o!=="suppressHydrationWarning"&&o!=="autoFocus"&&(Yr.hasOwnProperty(o)?a!=null&&o==="onScroll"&&U("scroll",e):a!=null&&Ta(e,o,a,s))}switch(n){case"input":Vi(e),rc(e,r,!1);break;case"textarea":Vi(e),oc(e);break;case"option":r.value!=null&&e.setAttribute("value",""+Wt(r.value));break;case"select":e.multiple=!!r.multiple,o=r.value,o!=null?Qn(e,!!r.multiple,o,!1):r.defaultValue!=null&&Qn(e,!!r.multiple,r.defaultValue,!0);break;default:typeof i.onClick=="function"&&(e.onclick=ko)}switch(n){case"button":case"input":case"select":case"textarea":r=!!r.autoFocus;break e;case"img":r=!0;break e;default:r=!1}}r&&(t.flags|=4)}t.ref!==null&&(t.flags|=512,t.flags|=2097152)}return ye(t),null;case 6:if(e&&t.stateNode!=null)kp(e,t,e.memoizedProps,r);else{if(typeof r!="string"&&t.stateNode===null)throw Error(T(166));if(n=un(li.current),un(ct.current),Hi(t)){if(r=t.stateNode,n=t.memoizedProps,r[at]=t,(o=r.nodeValue!==n)&&(e=Oe,e!==null))switch(e.tag){case 3:$i(r.nodeValue,n,(e.mode&1)!==0);break;case 5:e.memoizedProps.suppressHydrationWarning!==!0&&$i(r.nodeValue,n,(e.mode&1)!==0)}o&&(t.flags|=4)}else r=(n.nodeType===9?n:n.ownerDocument).createTextNode(r),r[at]=t,t.stateNode=r}return ye(t),null;case 13:if($(G),r=t.memoizedState,e===null||e.memoizedState!==null&&e.memoizedState.dehydrated!==null){if(W&&Ve!==null&&t.mode&1&&!(t.flags&128))Wh(),tr(),t.flags|=98560,o=!1;else if(o=Hi(t),r!==null&&r.dehydrated!==null){if(e===null){if(!o)throw Error(T(318));if(o=t.memoizedState,o=o!==null?o.dehydrated:null,!o)throw Error(T(317));o[at]=t}else tr(),!(t.flags&128)&&(t.memoizedState=null),t.flags|=4;ye(t),o=!1}else et!==null&&(ea(et),et=null),o=!0;if(!o)return t.flags&65536?t:null}return t.flags&128?(t.lanes=n,t):(r=r!==null,r!==(e!==null&&e.memoizedState!==null)&&r&&(t.child.flags|=8192,t.mode&1&&(e===null||G.current&1?le===0&&(le=3):lu())),t.updateQueue!==null&&(t.flags|=4),ye(t),null);case 4:return rr(),Gl(e,t),e===null&&ri(t.stateNode.containerInfo),ye(t),null;case 10:return Wa(t.type._context),ye(t),null;case 17:return Le(t.type)&&Ao(),ye(t),null;case 19:if($(G),o=t.memoizedState,o===null)return ye(t),null;if(r=(t.flags&128)!==0,s=o.rendering,s===null)if(r)Pr(o,!1);else{if(le!==0||e!==null&&e.flags&128)for(e=t.child;e!==null;){if(s=Vo(e),s!==null){for(t.flags|=128,Pr(o,!1),r=s.updateQueue,r!==null&&(t.updateQueue=r,t.flags|=4),t.subtreeFlags=0,r=n,n=t.child;n!==null;)o=n,e=r,o.flags&=14680066,s=o.alternate,s===null?(o.childLanes=0,o.lanes=e,o.child=null,o.subtreeFlags=0,o.memoizedProps=null,o.memoizedState=null,o.updateQueue=null,o.dependencies=null,o.stateNode=null):(o.childLanes=s.childLanes,o.lanes=s.lanes,o.child=s.child,o.subtreeFlags=0,o.deletions=null,o.memoizedProps=s.memoizedProps,o.memoizedState=s.memoizedState,o.updateQueue=s.updateQueue,o.type=s.type,e=s.dependencies,o.dependencies=e===null?null:{lanes:e.lanes,firstContext:e.firstContext}),n=n.sibling;return B(G,G.current&1|2),t.child}e=e.sibling}o.tail!==null&&re()>or&&(t.flags|=128,r=!0,Pr(o,!1),t.lanes=4194304)}else{if(!r)if(e=Vo(s),e!==null){if(t.flags|=128,r=!0,n=e.updateQueue,n!==null&&(t.updateQueue=n,t.flags|=4),Pr(o,!0),o.tail===null&&o.tailMode==="hidden"&&!s.alternate&&!W)return ye(t),null}else 2*re()-o.renderingStartTime>or&&n!==1073741824&&(t.flags|=128,r=!0,Pr(o,!1),t.lanes=4194304);o.isBackwards?(s.sibling=t.child,t.child=s):(n=o.last,n!==null?n.sibling=s:t.child=s,o.last=s)}return o.tail!==null?(t=o.tail,o.rendering=t,o.tail=t.sibling,o.renderingStartTime=re(),t.sibling=null,n=G.current,B(G,r?n&1|2:n&1),t):(ye(t),null);case 22:case 23:return su(),r=t.memoizedState!==null,e!==null&&e.memoizedState!==null!==r&&(t.flags|=8192),r&&t.mode&1?_e&1073741824&&(ye(t),t.subtreeFlags&6&&(t.flags|=8192)):ye(t),null;case 24:return null;case 25:return null}throw Error(T(156,t.tag))}function I0(e,t){switch(Ba(t),t.tag){case 1:return Le(t.type)&&Ao(),e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 3:return rr(),$(De),$(Ee),Ya(),e=t.flags,e&65536&&!(e&128)?(t.flags=e&-65537|128,t):null;case 5:return Xa(t),null;case 13:if($(G),e=t.memoizedState,e!==null&&e.dehydrated!==null){if(t.alternate===null)throw Error(T(340));tr()}return e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 19:return $(G),null;case 4:return rr(),null;case 10:return Wa(t.type._context),null;case 22:case 23:return su(),null;case 24:return null;default:return null}}var Gi=!1,Se=!1,F0=typeof WeakSet=="function"?WeakSet:Set,D=null;function Bn(e,t){var n=e.ref;if(n!==null)if(typeof n=="function")try{n(null)}catch(r){q(e,t,r)}else n.current=null}function Ql(e,t,n){try{n()}catch(r){q(e,t,r)}}var Xc=!1;function j0(e,t){if(Dl=Co,e=Lh(),ja(e)){if("selectionStart"in e)var n={start:e.selectionStart,end:e.selectionEnd};else e:{n=(n=e.ownerDocument)&&n.defaultView||window;var r=n.getSelection&&n.getSelection();if(r&&r.rangeCount!==0){n=r.anchorNode;var i=r.anchorOffset,o=r.focusNode;r=r.focusOffset;try{n.nodeType,o.nodeType}catch{n=null;break e}var s=0,l=-1,a=-1,u=0,c=0,d=e,f=null;t:for(;;){for(var m;d!==n||i!==0&&d.nodeType!==3||(l=s+i),d!==o||r!==0&&d.nodeType!==3||(a=s+r),d.nodeType===3&&(s+=d.nodeValue.length),(m=d.firstChild)!==null;)f=d,d=m;for(;;){if(d===e)break t;if(f===n&&++u===i&&(l=s),f===o&&++c===r&&(a=s),(m=d.nextSibling)!==null)break;d=f,f=d.parentNode}d=m}n=l===-1||a===-1?null:{start:l,end:a}}else n=null}n=n||{start:0,end:0}}else n=null;for(Ll={focusedElem:e,selectionRange:n},Co=!1,D=t;D!==null;)if(t=D,e=t.child,(t.subtreeFlags&1028)!==0&&e!==null)e.return=t,D=e;else for(;D!==null;){t=D;try{var v=t.alternate;if(t.flags&1024)switch(t.tag){case 0:case 11:case 15:break;case 1:if(v!==null){var w=v.memoizedProps,C=v.memoizedState,p=t.stateNode,h=p.getSnapshotBeforeUpdate(t.elementType===t.type?w:qe(t.type,w),C);p.__reactInternalSnapshotBeforeUpdate=h}break;case 3:var g=t.stateNode.containerInfo;g.nodeType===1?g.textContent="":g.nodeType===9&&g.documentElement&&g.removeChild(g.documentElement);break;case 5:case 6:case 4:case 17:break;default:throw Error(T(163))}}catch(S){q(t,t.return,S)}if(e=t.sibling,e!==null){e.return=t.return,D=e;break}D=t.return}return v=Xc,Xc=!1,v}function Br(e,t,n){var r=t.updateQueue;if(r=r!==null?r.lastEffect:null,r!==null){var i=r=r.next;do{if((i.tag&e)===e){var o=i.destroy;i.destroy=void 0,o!==void 0&&Ql(t,n,o)}i=i.next}while(i!==r)}}function as(e,t){if(t=t.updateQueue,t=t!==null?t.lastEffect:null,t!==null){var n=t=t.next;do{if((n.tag&e)===e){var r=n.create;n.destroy=r()}n=n.next}while(n!==t)}}function Xl(e){var t=e.ref;if(t!==null){var n=e.stateNode;switch(e.tag){case 5:e=n;break;default:e=n}typeof t=="function"?t(e):t.current=e}}function Ap(e){var t=e.alternate;t!==null&&(e.alternate=null,Ap(t)),e.child=null,e.deletions=null,e.sibling=null,e.tag===5&&(t=e.stateNode,t!==null&&(delete t[at],delete t[oi],delete t[Vl],delete t[S0],delete t[x0])),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}function Np(e){return e.tag===5||e.tag===3||e.tag===4}function Yc(e){e:for(;;){for(;e.sibling===null;){if(e.return===null||Np(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;e.tag!==5&&e.tag!==6&&e.tag!==18;){if(e.flags&2||e.child===null||e.tag===4)continue e;e.child.return=e,e=e.child}if(!(e.flags&2))return e.stateNode}}function Yl(e,t,n){var r=e.tag;if(r===5||r===6)e=e.stateNode,t?n.nodeType===8?n.parentNode.insertBefore(e,t):n.insertBefore(e,t):(n.nodeType===8?(t=n.parentNode,t.insertBefore(e,n)):(t=n,t.appendChild(e)),n=n._reactRootContainer,n!=null||t.onclick!==null||(t.onclick=ko));else if(r!==4&&(e=e.child,e!==null))for(Yl(e,t,n),e=e.sibling;e!==null;)Yl(e,t,n),e=e.sibling}function bl(e,t,n){var r=e.tag;if(r===5||r===6)e=e.stateNode,t?n.insertBefore(e,t):n.appendChild(e);else if(r!==4&&(e=e.child,e!==null))for(bl(e,t,n),e=e.sibling;e!==null;)bl(e,t,n),e=e.sibling}var de=null,Je=!1;function Nt(e,t,n){for(n=n.child;n!==null;)Mp(e,t,n),n=n.sibling}function Mp(e,t,n){if(ut&&typeof ut.onCommitFiberUnmount=="function")try{ut.onCommitFiberUnmount(es,n)}catch{}switch(n.tag){case 5:Se||Bn(n,t);case 6:var r=de,i=Je;de=null,Nt(e,t,n),de=r,Je=i,de!==null&&(Je?(e=de,n=n.stateNode,e.nodeType===8?e.parentNode.removeChild(n):e.removeChild(n)):de.removeChild(n.stateNode));break;case 18:de!==null&&(Je?(e=de,n=n.stateNode,e.nodeType===8?zs(e.parentNode,n):e.nodeType===1&&zs(e,n),ei(e)):zs(de,n.stateNode));break;case 4:r=de,i=Je,de=n.stateNode.containerInfo,Je=!0,Nt(e,t,n),de=r,Je=i;break;case 0:case 11:case 14:case 15:if(!Se&&(r=n.updateQueue,r!==null&&(r=r.lastEffect,r!==null))){i=r=r.next;do{var o=i,s=o.destroy;o=o.tag,s!==void 0&&(o&2||o&4)&&Ql(n,t,s),i=i.next}while(i!==r)}Nt(e,t,n);break;case 1:if(!Se&&(Bn(n,t),r=n.stateNode,typeof r.componentWillUnmount=="function"))try{r.props=n.memoizedProps,r.state=n.memoizedState,r.componentWillUnmount()}catch(l){q(n,t,l)}Nt(e,t,n);break;case 21:Nt(e,t,n);break;case 22:n.mode&1?(Se=(r=Se)||n.memoizedState!==null,Nt(e,t,n),Se=r):Nt(e,t,n);break;default:Nt(e,t,n)}}function bc(e){var t=e.updateQueue;if(t!==null){e.updateQueue=null;var n=e.stateNode;n===null&&(n=e.stateNode=new F0),t.forEach(function(r){var i=Q0.bind(null,e,r);n.has(r)||(n.add(r),r.then(i,i))})}}function be(e,t){var n=t.deletions;if(n!==null)for(var r=0;r<n.length;r++){var i=n[r];try{var o=e,s=t,l=s;e:for(;l!==null;){switch(l.tag){case 5:de=l.stateNode,Je=!1;break e;case 3:de=l.stateNode.containerInfo,Je=!0;break e;case 4:de=l.stateNode.containerInfo,Je=!0;break e}l=l.return}if(de===null)throw Error(T(160));Mp(o,s,i),de=null,Je=!1;var a=i.alternate;a!==null&&(a.return=null),i.return=null}catch(u){q(i,t,u)}}if(t.subtreeFlags&12854)for(t=t.child;t!==null;)Dp(t,e),t=t.sibling}function Dp(e,t){var n=e.alternate,r=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:if(be(t,e),st(e),r&4){try{Br(3,e,e.return),as(3,e)}catch(w){q(e,e.return,w)}try{Br(5,e,e.return)}catch(w){q(e,e.return,w)}}break;case 1:be(t,e),st(e),r&512&&n!==null&&Bn(n,n.return);break;case 5:if(be(t,e),st(e),r&512&&n!==null&&Bn(n,n.return),e.flags&32){var i=e.stateNode;try{br(i,"")}catch(w){q(e,e.return,w)}}if(r&4&&(i=e.stateNode,i!=null)){var o=e.memoizedProps,s=n!==null?n.memoizedProps:o,l=e.type,a=e.updateQueue;if(e.updateQueue=null,a!==null)try{l==="input"&&o.type==="radio"&&o.name!=null&&qd(i,o),Sl(l,s);var u=Sl(l,o);for(s=0;s<a.length;s+=2){var c=a[s],d=a[s+1];c==="style"?rh(i,d):c==="dangerouslySetInnerHTML"?th(i,d):c==="children"?br(i,d):Ta(i,c,d,u)}switch(l){case"input":ml(i,o);break;case"textarea":Jd(i,o);break;case"select":var f=i._wrapperState.wasMultiple;i._wrapperState.wasMultiple=!!o.multiple;var m=o.value;m!=null?Qn(i,!!o.multiple,m,!1):f!==!!o.multiple&&(o.defaultValue!=null?Qn(i,!!o.multiple,o.defaultValue,!0):Qn(i,!!o.multiple,o.multiple?[]:"",!1))}i[oi]=o}catch(w){q(e,e.return,w)}}break;case 6:if(be(t,e),st(e),r&4){if(e.stateNode===null)throw Error(T(162));i=e.stateNode,o=e.memoizedProps;try{i.nodeValue=o}catch(w){q(e,e.return,w)}}break;case 3:if(be(t,e),st(e),r&4&&n!==null&&n.memoizedState.isDehydrated)try{ei(t.containerInfo)}catch(w){q(e,e.return,w)}break;case 4:be(t,e),st(e);break;case 13:be(t,e),st(e),i=e.child,i.flags&8192&&(o=i.memoizedState!==null,i.stateNode.isHidden=o,!o||i.alternate!==null&&i.alternate.memoizedState!==null||(iu=re())),r&4&&bc(e);break;case 22:if(c=n!==null&&n.memoizedState!==null,e.mode&1?(Se=(u=Se)||c,be(t,e),Se=u):be(t,e),st(e),r&8192){if(u=e.memoizedState!==null,(e.stateNode.isHidden=u)&&!c&&e.mode&1)for(D=e,c=e.child;c!==null;){for(d=D=c;D!==null;){switch(f=D,m=f.child,f.tag){case 0:case 11:case 14:case 15:Br(4,f,f.return);break;case 1:Bn(f,f.return);var v=f.stateNode;if(typeof v.componentWillUnmount=="function"){r=f,n=f.return;try{t=r,v.props=t.memoizedProps,v.state=t.memoizedState,v.componentWillUnmount()}catch(w){q(r,n,w)}}break;case 5:Bn(f,f.return);break;case 22:if(f.memoizedState!==null){qc(d);continue}}m!==null?(m.return=f,D=m):qc(d)}c=c.sibling}e:for(c=null,d=e;;){if(d.tag===5){if(c===null){c=d;try{i=d.stateNode,u?(o=i.style,typeof o.setProperty=="function"?o.setProperty("display","none","important"):o.display="none"):(l=d.stateNode,a=d.memoizedProps.style,s=a!=null&&a.hasOwnProperty("display")?a.display:null,l.style.display=nh("display",s))}catch(w){q(e,e.return,w)}}}else if(d.tag===6){if(c===null)try{d.stateNode.nodeValue=u?"":d.memoizedProps}catch(w){q(e,e.return,w)}}else if((d.tag!==22&&d.tag!==23||d.memoizedState===null||d===e)&&d.child!==null){d.child.return=d,d=d.child;continue}if(d===e)break e;for(;d.sibling===null;){if(d.return===null||d.return===e)break e;c===d&&(c=null),d=d.return}c===d&&(c=null),d.sibling.return=d.return,d=d.sibling}}break;case 19:be(t,e),st(e),r&4&&bc(e);break;case 21:break;default:be(t,e),st(e)}}function st(e){var t=e.flags;if(t&2){try{e:{for(var n=e.return;n!==null;){if(Np(n)){var r=n;break e}n=n.return}throw Error(T(160))}switch(r.tag){case 5:var i=r.stateNode;r.flags&32&&(br(i,""),r.flags&=-33);var o=Yc(e);bl(e,o,i);break;case 3:case 4:var s=r.stateNode.containerInfo,l=Yc(e);Yl(e,l,s);break;default:throw Error(T(161))}}catch(a){q(e,e.return,a)}e.flags&=-3}t&4096&&(e.flags&=-4097)}function z0(e,t,n){D=e,Lp(e)}function Lp(e,t,n){for(var r=(e.mode&1)!==0;D!==null;){var i=D,o=i.child;if(i.tag===22&&r){var s=i.memoizedState!==null||Gi;if(!s){var l=i.alternate,a=l!==null&&l.memoizedState!==null||Se;l=Gi;var u=Se;if(Gi=s,(Se=a)&&!u)for(D=i;D!==null;)s=D,a=s.child,s.tag===22&&s.memoizedState!==null?Jc(i):a!==null?(a.return=s,D=a):Jc(i);for(;o!==null;)D=o,Lp(o),o=o.sibling;D=i,Gi=l,Se=u}Zc(e)}else i.subtreeFlags&8772&&o!==null?(o.return=i,D=o):Zc(e)}}function Zc(e){for(;D!==null;){var t=D;if(t.flags&8772){var n=t.alternate;try{if(t.flags&8772)switch(t.tag){case 0:case 11:case 15:Se||as(5,t);break;case 1:var r=t.stateNode;if(t.flags&4&&!Se)if(n===null)r.componentDidMount();else{var i=t.elementType===t.type?n.memoizedProps:qe(t.type,n.memoizedProps);r.componentDidUpdate(i,n.memoizedState,r.__reactInternalSnapshotBeforeUpdate)}var o=t.updateQueue;o!==null&&Oc(t,o,r);break;case 3:var s=t.updateQueue;if(s!==null){if(n=null,t.child!==null)switch(t.child.tag){case 5:n=t.child.stateNode;break;case 1:n=t.child.stateNode}Oc(t,s,n)}break;case 5:var l=t.stateNode;if(n===null&&t.flags&4){n=l;var a=t.memoizedProps;switch(t.type){case"button":case"input":case"select":case"textarea":a.autoFocus&&n.focus();break;case"img":a.src&&(n.src=a.src)}}break;case 6:break;case 4:break;case 12:break;case 13:if(t.memoizedState===null){var u=t.alternate;if(u!==null){var c=u.memoizedState;if(c!==null){var d=c.dehydrated;d!==null&&ei(d)}}}break;case 19:case 17:case 21:case 22:case 23:case 25:break;default:throw Error(T(163))}Se||t.flags&512&&Xl(t)}catch(f){q(t,t.return,f)}}if(t===e){D=null;break}if(n=t.sibling,n!==null){n.return=t.return,D=n;break}D=t.return}}function qc(e){for(;D!==null;){var t=D;if(t===e){D=null;break}var n=t.sibling;if(n!==null){n.return=t.return,D=n;break}D=t.return}}function Jc(e){for(;D!==null;){var t=D;try{switch(t.tag){case 0:case 11:case 15:var n=t.return;try{as(4,t)}catch(a){q(t,n,a)}break;case 1:var r=t.stateNode;if(typeof r.componentDidMount=="function"){var i=t.return;try{r.componentDidMount()}catch(a){q(t,i,a)}}var o=t.return;try{Xl(t)}catch(a){q(t,o,a)}break;case 5:var s=t.return;try{Xl(t)}catch(a){q(t,s,a)}}}catch(a){q(t,t.return,a)}if(t===e){D=null;break}var l=t.sibling;if(l!==null){l.return=t.return,D=l;break}D=t.return}}var B0=Math.ceil,Fo=kt.ReactCurrentDispatcher,nu=kt.ReactCurrentOwner,Ge=kt.ReactCurrentBatchConfig,j=0,fe=null,ie=null,pe=0,_e=0,Un=Yt(0),le=0,fi=null,wn=0,us=0,ru=0,Ur=null,Ne=null,iu=0,or=1/0,ht=null,jo=!1,Zl=null,Ut=null,Qi=!1,Ot=null,zo=0,$r=0,ql=null,uo=-1,co=0;function Pe(){return j&6?re():uo!==-1?uo:uo=re()}function $t(e){return e.mode&1?j&2&&pe!==0?pe&-pe:C0.transition!==null?(co===0&&(co=mh()),co):(e=z,e!==0||(e=window.event,e=e===void 0?16:Eh(e.type)),e):1}function it(e,t,n,r){if(50<$r)throw $r=0,ql=null,Error(T(185));wi(e,n,r),(!(j&2)||e!==fe)&&(e===fe&&(!(j&2)&&(us|=n),le===4&&_t(e,pe)),Re(e,r),n===1&&j===0&&!(t.mode&1)&&(or=re()+500,os&&bt()))}function Re(e,t){var n=e.callbackNode;Cv(e,t);var r=Eo(e,e===fe?pe:0);if(r===0)n!==null&&ac(n),e.callbackNode=null,e.callbackPriority=0;else if(t=r&-r,e.callbackPriority!==t){if(n!=null&&ac(n),t===1)e.tag===0?E0(ef.bind(null,e)):Uh(ef.bind(null,e)),y0(function(){!(j&6)&&bt()}),n=null;else{switch(gh(r)){case 1:n=Da;break;case 4:n=hh;break;case 16:n=xo;break;case 536870912:n=ph;break;default:n=xo}n=zp(n,Rp.bind(null,e))}e.callbackPriority=t,e.callbackNode=n}}function Rp(e,t){if(uo=-1,co=0,j&6)throw Error(T(327));var n=e.callbackNode;if(qn()&&e.callbackNode!==n)return null;var r=Eo(e,e===fe?pe:0);if(r===0)return null;if(r&30||r&e.expiredLanes||t)t=Bo(e,r);else{t=r;var i=j;j|=2;var o=Vp();(fe!==e||pe!==t)&&(ht=null,or=re()+500,dn(e,t));do try{H0();break}catch(l){_p(e,l)}while(1);Ha(),Fo.current=o,j=i,ie!==null?t=0:(fe=null,pe=0,t=le)}if(t!==0){if(t===2&&(i=Tl(e),i!==0&&(r=i,t=Jl(e,i))),t===1)throw n=fi,dn(e,0),_t(e,r),Re(e,re()),n;if(t===6)_t(e,r);else{if(i=e.current.alternate,!(r&30)&&!U0(i)&&(t=Bo(e,r),t===2&&(o=Tl(e),o!==0&&(r=o,t=Jl(e,o))),t===1))throw n=fi,dn(e,0),_t(e,r),Re(e,re()),n;switch(e.finishedWork=i,e.finishedLanes=r,t){case 0:case 1:throw Error(T(345));case 2:rn(e,Ne,ht);break;case 3:if(_t(e,r),(r&130023424)===r&&(t=iu+500-re(),10<t)){if(Eo(e,0)!==0)break;if(i=e.suspendedLanes,(i&r)!==r){Pe(),e.pingedLanes|=e.suspendedLanes&i;break}e.timeoutHandle=_l(rn.bind(null,e,Ne,ht),t);break}rn(e,Ne,ht);break;case 4:if(_t(e,r),(r&4194240)===r)break;for(t=e.eventTimes,i=-1;0<r;){var s=31-rt(r);o=1<<s,s=t[s],s>i&&(i=s),r&=~o}if(r=i,r=re()-r,r=(120>r?120:480>r?480:1080>r?1080:1920>r?1920:3e3>r?3e3:4320>r?4320:1960*B0(r/1960))-r,10<r){e.timeoutHandle=_l(rn.bind(null,e,Ne,ht),r);break}rn(e,Ne,ht);break;case 5:rn(e,Ne,ht);break;default:throw Error(T(329))}}}return Re(e,re()),e.callbackNode===n?Rp.bind(null,e):null}function Jl(e,t){var n=Ur;return e.current.memoizedState.isDehydrated&&(dn(e,t).flags|=256),e=Bo(e,t),e!==2&&(t=Ne,Ne=n,t!==null&&ea(t)),e}function ea(e){Ne===null?Ne=e:Ne.push.apply(Ne,e)}function U0(e){for(var t=e;;){if(t.flags&16384){var n=t.updateQueue;if(n!==null&&(n=n.stores,n!==null))for(var r=0;r<n.length;r++){var i=n[r],o=i.getSnapshot;i=i.value;try{if(!ot(o(),i))return!1}catch{return!1}}}if(n=t.child,t.subtreeFlags&16384&&n!==null)n.return=t,t=n;else{if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return!0;t=t.return}t.sibling.return=t.return,t=t.sibling}}return!0}function _t(e,t){for(t&=~ru,t&=~us,e.suspendedLanes|=t,e.pingedLanes&=~t,e=e.expirationTimes;0<t;){var n=31-rt(t),r=1<<n;e[n]=-1,t&=~r}}function ef(e){if(j&6)throw Error(T(327));qn();var t=Eo(e,0);if(!(t&1))return Re(e,re()),null;var n=Bo(e,t);if(e.tag!==0&&n===2){var r=Tl(e);r!==0&&(t=r,n=Jl(e,r))}if(n===1)throw n=fi,dn(e,0),_t(e,t),Re(e,re()),n;if(n===6)throw Error(T(345));return e.finishedWork=e.current.alternate,e.finishedLanes=t,rn(e,Ne,ht),Re(e,re()),null}function ou(e,t){var n=j;j|=1;try{return e(t)}finally{j=n,j===0&&(or=re()+500,os&&bt())}}function Sn(e){Ot!==null&&Ot.tag===0&&!(j&6)&&qn();var t=j;j|=1;var n=Ge.transition,r=z;try{if(Ge.transition=null,z=1,e)return e()}finally{z=r,Ge.transition=n,j=t,!(j&6)&&bt()}}function su(){_e=Un.current,$(Un)}function dn(e,t){e.finishedWork=null,e.finishedLanes=0;var n=e.timeoutHandle;if(n!==-1&&(e.timeoutHandle=-1,v0(n)),ie!==null)for(n=ie.return;n!==null;){var r=n;switch(Ba(r),r.tag){case 1:r=r.type.childContextTypes,r!=null&&Ao();break;case 3:rr(),$(De),$(Ee),Ya();break;case 5:Xa(r);break;case 4:rr();break;case 13:$(G);break;case 19:$(G);break;case 10:Wa(r.type._context);break;case 22:case 23:su()}n=n.return}if(fe=e,ie=e=Ht(e.current,null),pe=_e=t,le=0,fi=null,ru=us=wn=0,Ne=Ur=null,an!==null){for(t=0;t<an.length;t++)if(n=an[t],r=n.interleaved,r!==null){n.interleaved=null;var i=r.next,o=n.pending;if(o!==null){var s=o.next;o.next=i,r.next=s}n.pending=r}an=null}return e}function _p(e,t){do{var n=ie;try{if(Ha(),so.current=Io,Oo){for(var r=X.memoizedState;r!==null;){var i=r.queue;i!==null&&(i.pending=null),r=r.next}Oo=!1}if(yn=0,ce=se=X=null,zr=!1,ai=0,nu.current=null,n===null||n.return===null){le=1,fi=t,ie=null;break}e:{var o=e,s=n.return,l=n,a=t;if(t=pe,l.flags|=32768,a!==null&&typeof a=="object"&&typeof a.then=="function"){var u=a,c=l,d=c.tag;if(!(c.mode&1)&&(d===0||d===11||d===15)){var f=c.alternate;f?(c.updateQueue=f.updateQueue,c.memoizedState=f.memoizedState,c.lanes=f.lanes):(c.updateQueue=null,c.memoizedState=null)}var m=Uc(s);if(m!==null){m.flags&=-257,$c(m,s,l,o,t),m.mode&1&&Bc(o,u,t),t=m,a=u;var v=t.updateQueue;if(v===null){var w=new Set;w.add(a),t.updateQueue=w}else v.add(a);break e}else{if(!(t&1)){Bc(o,u,t),lu();break e}a=Error(T(426))}}else if(W&&l.mode&1){var C=Uc(s);if(C!==null){!(C.flags&65536)&&(C.flags|=256),$c(C,s,l,o,t),Ua(ir(a,l));break e}}o=a=ir(a,l),le!==4&&(le=2),Ur===null?Ur=[o]:Ur.push(o),o=s;do{switch(o.tag){case 3:o.flags|=65536,t&=-t,o.lanes|=t;var p=gp(o,a,t);Vc(o,p);break e;case 1:l=a;var h=o.type,g=o.stateNode;if(!(o.flags&128)&&(typeof h.getDerivedStateFromError=="function"||g!==null&&typeof g.componentDidCatch=="function"&&(Ut===null||!Ut.has(g)))){o.flags|=65536,t&=-t,o.lanes|=t;var S=vp(o,l,t);Vc(o,S);break e}}o=o.return}while(o!==null)}Ip(n)}catch(x){t=x,ie===n&&n!==null&&(ie=n=n.return);continue}break}while(1)}function Vp(){var e=Fo.current;return Fo.current=Io,e===null?Io:e}function lu(){(le===0||le===3||le===2)&&(le=4),fe===null||!(wn&268435455)&&!(us&268435455)||_t(fe,pe)}function Bo(e,t){var n=j;j|=2;var r=Vp();(fe!==e||pe!==t)&&(ht=null,dn(e,t));do try{$0();break}catch(i){_p(e,i)}while(1);if(Ha(),j=n,Fo.current=r,ie!==null)throw Error(T(261));return fe=null,pe=0,le}function $0(){for(;ie!==null;)Op(ie)}function H0(){for(;ie!==null&&!pv();)Op(ie)}function Op(e){var t=jp(e.alternate,e,_e);e.memoizedProps=e.pendingProps,t===null?Ip(e):ie=t,nu.current=null}function Ip(e){var t=e;do{var n=t.alternate;if(e=t.return,t.flags&32768){if(n=I0(n,t),n!==null){n.flags&=32767,ie=n;return}if(e!==null)e.flags|=32768,e.subtreeFlags=0,e.deletions=null;else{le=6,ie=null;return}}else if(n=O0(n,t,_e),n!==null){ie=n;return}if(t=t.sibling,t!==null){ie=t;return}ie=t=e}while(t!==null);le===0&&(le=5)}function rn(e,t,n){var r=z,i=Ge.transition;try{Ge.transition=null,z=1,W0(e,t,n,r)}finally{Ge.transition=i,z=r}return null}function W0(e,t,n,r){do qn();while(Ot!==null);if(j&6)throw Error(T(327));n=e.finishedWork;var i=e.finishedLanes;if(n===null)return null;if(e.finishedWork=null,e.finishedLanes=0,n===e.current)throw Error(T(177));e.callbackNode=null,e.callbackPriority=0;var o=n.lanes|n.childLanes;if(Pv(e,o),e===fe&&(ie=fe=null,pe=0),!(n.subtreeFlags&2064)&&!(n.flags&2064)||Qi||(Qi=!0,zp(xo,function(){return qn(),null})),o=(n.flags&15990)!==0,n.subtreeFlags&15990||o){o=Ge.transition,Ge.transition=null;var s=z;z=1;var l=j;j|=4,nu.current=null,j0(e,n),Dp(n,e),c0(Ll),Co=!!Dl,Ll=Dl=null,e.current=n,z0(n),mv(),j=l,z=s,Ge.transition=o}else e.current=n;if(Qi&&(Qi=!1,Ot=e,zo=i),o=e.pendingLanes,o===0&&(Ut=null),yv(n.stateNode),Re(e,re()),t!==null)for(r=e.onRecoverableError,n=0;n<t.length;n++)i=t[n],r(i.value,{componentStack:i.stack,digest:i.digest});if(jo)throw jo=!1,e=Zl,Zl=null,e;return zo&1&&e.tag!==0&&qn(),o=e.pendingLanes,o&1?e===ql?$r++:($r=0,ql=e):$r=0,bt(),null}function qn(){if(Ot!==null){var e=gh(zo),t=Ge.transition,n=z;try{if(Ge.transition=null,z=16>e?16:e,Ot===null)var r=!1;else{if(e=Ot,Ot=null,zo=0,j&6)throw Error(T(331));var i=j;for(j|=4,D=e.current;D!==null;){var o=D,s=o.child;if(D.flags&16){var l=o.deletions;if(l!==null){for(var a=0;a<l.length;a++){var u=l[a];for(D=u;D!==null;){var c=D;switch(c.tag){case 0:case 11:case 15:Br(8,c,o)}var d=c.child;if(d!==null)d.return=c,D=d;else for(;D!==null;){c=D;var f=c.sibling,m=c.return;if(Ap(c),c===u){D=null;break}if(f!==null){f.return=m,D=f;break}D=m}}}var v=o.alternate;if(v!==null){var w=v.child;if(w!==null){v.child=null;do{var C=w.sibling;w.sibling=null,w=C}while(w!==null)}}D=o}}if(o.subtreeFlags&2064&&s!==null)s.return=o,D=s;else e:for(;D!==null;){if(o=D,o.flags&2048)switch(o.tag){case 0:case 11:case 15:Br(9,o,o.return)}var p=o.sibling;if(p!==null){p.return=o.return,D=p;break e}D=o.return}}var h=e.current;for(D=h;D!==null;){s=D;var g=s.child;if(s.subtreeFlags&2064&&g!==null)g.return=s,D=g;else e:for(s=h;D!==null;){if(l=D,l.flags&2048)try{switch(l.tag){case 0:case 11:case 15:as(9,l)}}catch(x){q(l,l.return,x)}if(l===s){D=null;break e}var S=l.sibling;if(S!==null){S.return=l.return,D=S;break e}D=l.return}}if(j=i,bt(),ut&&typeof ut.onPostCommitFiberRoot=="function")try{ut.onPostCommitFiberRoot(es,e)}catch{}r=!0}return r}finally{z=n,Ge.transition=t}}return!1}function tf(e,t,n){t=ir(n,t),t=gp(e,t,1),e=Bt(e,t,1),t=Pe(),e!==null&&(wi(e,1,t),Re(e,t))}function q(e,t,n){if(e.tag===3)tf(e,e,n);else for(;t!==null;){if(t.tag===3){tf(t,e,n);break}else if(t.tag===1){var r=t.stateNode;if(typeof t.type.getDerivedStateFromError=="function"||typeof r.componentDidCatch=="function"&&(Ut===null||!Ut.has(r))){e=ir(n,e),e=vp(t,e,1),t=Bt(t,e,1),e=Pe(),t!==null&&(wi(t,1,e),Re(t,e));break}}t=t.return}}function K0(e,t,n){var r=e.pingCache;r!==null&&r.delete(t),t=Pe(),e.pingedLanes|=e.suspendedLanes&n,fe===e&&(pe&n)===n&&(le===4||le===3&&(pe&130023424)===pe&&500>re()-iu?dn(e,0):ru|=n),Re(e,t)}function Fp(e,t){t===0&&(e.mode&1?(t=Fi,Fi<<=1,!(Fi&130023424)&&(Fi=4194304)):t=1);var n=Pe();e=Ct(e,t),e!==null&&(wi(e,t,n),Re(e,n))}function G0(e){var t=e.memoizedState,n=0;t!==null&&(n=t.retryLane),Fp(e,n)}function Q0(e,t){var n=0;switch(e.tag){case 13:var r=e.stateNode,i=e.memoizedState;i!==null&&(n=i.retryLane);break;case 19:r=e.stateNode;break;default:throw Error(T(314))}r!==null&&r.delete(t),Fp(e,n)}var jp;jp=function(e,t,n){if(e!==null)if(e.memoizedProps!==t.pendingProps||De.current)Me=!0;else{if(!(e.lanes&n)&&!(t.flags&128))return Me=!1,V0(e,t,n);Me=!!(e.flags&131072)}else Me=!1,W&&t.flags&1048576&&$h(t,Do,t.index);switch(t.lanes=0,t.tag){case 2:var r=t.type;ao(e,t),e=t.pendingProps;var i=er(t,Ee.current);Zn(t,n),i=Za(null,t,r,e,i,n);var o=qa();return t.flags|=1,typeof i=="object"&&i!==null&&typeof i.render=="function"&&i.$$typeof===void 0?(t.tag=1,t.memoizedState=null,t.updateQueue=null,Le(r)?(o=!0,No(t)):o=!1,t.memoizedState=i.state!==null&&i.state!==void 0?i.state:null,Ga(t),i.updater=ls,t.stateNode=i,i._reactInternals=t,Bl(t,r,e,n),t=Hl(null,t,r,!0,o,n)):(t.tag=0,W&&o&&za(t),Ce(null,t,i,n),t=t.child),t;case 16:r=t.elementType;e:{switch(ao(e,t),e=t.pendingProps,i=r._init,r=i(r._payload),t.type=r,i=t.tag=Y0(r),e=qe(r,e),i){case 0:t=$l(null,t,r,e,n);break e;case 1:t=Kc(null,t,r,e,n);break e;case 11:t=Hc(null,t,r,e,n);break e;case 14:t=Wc(null,t,r,qe(r.type,e),n);break e}throw Error(T(306,r,""))}return t;case 0:return r=t.type,i=t.pendingProps,i=t.elementType===r?i:qe(r,i),$l(e,t,r,i,n);case 1:return r=t.type,i=t.pendingProps,i=t.elementType===r?i:qe(r,i),Kc(e,t,r,i,n);case 3:e:{if(xp(t),e===null)throw Error(T(387));r=t.pendingProps,o=t.memoizedState,i=o.element,Xh(e,t),_o(t,r,null,n);var s=t.memoizedState;if(r=s.element,o.isDehydrated)if(o={element:r,isDehydrated:!1,cache:s.cache,pendingSuspenseBoundaries:s.pendingSuspenseBoundaries,transitions:s.transitions},t.updateQueue.baseState=o,t.memoizedState=o,t.flags&256){i=ir(Error(T(423)),t),t=Gc(e,t,r,n,i);break e}else if(r!==i){i=ir(Error(T(424)),t),t=Gc(e,t,r,n,i);break e}else for(Ve=zt(t.stateNode.containerInfo.firstChild),Oe=t,W=!0,et=null,n=Gh(t,null,r,n),t.child=n;n;)n.flags=n.flags&-3|4096,n=n.sibling;else{if(tr(),r===i){t=Pt(e,t,n);break e}Ce(e,t,r,n)}t=t.child}return t;case 5:return Yh(t),e===null&&Fl(t),r=t.type,i=t.pendingProps,o=e!==null?e.memoizedProps:null,s=i.children,Rl(r,i)?s=null:o!==null&&Rl(r,o)&&(t.flags|=32),Sp(e,t),Ce(e,t,s,n),t.child;case 6:return e===null&&Fl(t),null;case 13:return Ep(e,t,n);case 4:return Qa(t,t.stateNode.containerInfo),r=t.pendingProps,e===null?t.child=nr(t,null,r,n):Ce(e,t,r,n),t.child;case 11:return r=t.type,i=t.pendingProps,i=t.elementType===r?i:qe(r,i),Hc(e,t,r,i,n);case 7:return Ce(e,t,t.pendingProps,n),t.child;case 8:return Ce(e,t,t.pendingProps.children,n),t.child;case 12:return Ce(e,t,t.pendingProps.children,n),t.child;case 10:e:{if(r=t.type._context,i=t.pendingProps,o=t.memoizedProps,s=i.value,B(Lo,r._currentValue),r._currentValue=s,o!==null)if(ot(o.value,s)){if(o.children===i.children&&!De.current){t=Pt(e,t,n);break e}}else for(o=t.child,o!==null&&(o.return=t);o!==null;){var l=o.dependencies;if(l!==null){s=o.child;for(var a=l.firstContext;a!==null;){if(a.context===r){if(o.tag===1){a=yt(-1,n&-n),a.tag=2;var u=o.updateQueue;if(u!==null){u=u.shared;var c=u.pending;c===null?a.next=a:(a.next=c.next,c.next=a),u.pending=a}}o.lanes|=n,a=o.alternate,a!==null&&(a.lanes|=n),jl(o.return,n,t),l.lanes|=n;break}a=a.next}}else if(o.tag===10)s=o.type===t.type?null:o.child;else if(o.tag===18){if(s=o.return,s===null)throw Error(T(341));s.lanes|=n,l=s.alternate,l!==null&&(l.lanes|=n),jl(s,n,t),s=o.sibling}else s=o.child;if(s!==null)s.return=o;else for(s=o;s!==null;){if(s===t){s=null;break}if(o=s.sibling,o!==null){o.return=s.return,s=o;break}s=s.return}o=s}Ce(e,t,i.children,n),t=t.child}return t;case 9:return i=t.type,r=t.pendingProps.children,Zn(t,n),i=Xe(i),r=r(i),t.flags|=1,Ce(e,t,r,n),t.child;case 14:return r=t.type,i=qe(r,t.pendingProps),i=qe(r.type,i),Wc(e,t,r,i,n);case 15:return yp(e,t,t.type,t.pendingProps,n);case 17:return r=t.type,i=t.pendingProps,i=t.elementType===r?i:qe(r,i),ao(e,t),t.tag=1,Le(r)?(e=!0,No(t)):e=!1,Zn(t,n),mp(t,r,i),Bl(t,r,i,n),Hl(null,t,r,!0,e,n);case 19:return Cp(e,t,n);case 22:return wp(e,t,n)}throw Error(T(156,t.tag))};function zp(e,t){return dh(e,t)}function X0(e,t,n,r){this.tag=e,this.key=n,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=r,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function Ke(e,t,n,r){return new X0(e,t,n,r)}function au(e){return e=e.prototype,!(!e||!e.isReactComponent)}function Y0(e){if(typeof e=="function")return au(e)?1:0;if(e!=null){if(e=e.$$typeof,e===Aa)return 11;if(e===Na)return 14}return 2}function Ht(e,t){var n=e.alternate;return n===null?(n=Ke(e.tag,t,e.key,e.mode),n.elementType=e.elementType,n.type=e.type,n.stateNode=e.stateNode,n.alternate=e,e.alternate=n):(n.pendingProps=t,n.type=e.type,n.flags=0,n.subtreeFlags=0,n.deletions=null),n.flags=e.flags&14680064,n.childLanes=e.childLanes,n.lanes=e.lanes,n.child=e.child,n.memoizedProps=e.memoizedProps,n.memoizedState=e.memoizedState,n.updateQueue=e.updateQueue,t=e.dependencies,n.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext},n.sibling=e.sibling,n.index=e.index,n.ref=e.ref,n}function fo(e,t,n,r,i,o){var s=2;if(r=e,typeof e=="function")au(e)&&(s=1);else if(typeof e=="string")s=5;else e:switch(e){case Ln:return hn(n.children,i,o,t);case ka:s=8,i|=8;break;case cl:return e=Ke(12,n,t,i|2),e.elementType=cl,e.lanes=o,e;case fl:return e=Ke(13,n,t,i),e.elementType=fl,e.lanes=o,e;case dl:return e=Ke(19,n,t,i),e.elementType=dl,e.lanes=o,e;case Yd:return cs(n,i,o,t);default:if(typeof e=="object"&&e!==null)switch(e.$$typeof){case Qd:s=10;break e;case Xd:s=9;break e;case Aa:s=11;break e;case Na:s=14;break e;case Dt:s=16,r=null;break e}throw Error(T(130,e==null?e:typeof e,""))}return t=Ke(s,n,t,i),t.elementType=e,t.type=r,t.lanes=o,t}function hn(e,t,n,r){return e=Ke(7,e,r,t),e.lanes=n,e}function cs(e,t,n,r){return e=Ke(22,e,r,t),e.elementType=Yd,e.lanes=n,e.stateNode={isHidden:!1},e}function Qs(e,t,n){return e=Ke(6,e,null,t),e.lanes=n,e}function Xs(e,t,n){return t=Ke(4,e.children!==null?e.children:[],e.key,t),t.lanes=n,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}function b0(e,t,n,r,i){this.tag=t,this.containerInfo=e,this.finishedWork=this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.pendingContext=this.context=null,this.callbackPriority=0,this.eventTimes=Ns(0),this.expirationTimes=Ns(-1),this.entangledLanes=this.finishedLanes=this.mutableReadLanes=this.expiredLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=Ns(0),this.identifierPrefix=r,this.onRecoverableError=i,this.mutableSourceEagerHydrationData=null}function uu(e,t,n,r,i,o,s,l,a){return e=new b0(e,t,n,l,a),t===1?(t=1,o===!0&&(t|=8)):t=0,o=Ke(3,null,null,t),e.current=o,o.stateNode=e,o.memoizedState={element:r,isDehydrated:n,cache:null,transitions:null,pendingSuspenseBoundaries:null},Ga(o),e}function Z0(e,t,n){var r=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:Dn,key:r==null?null:""+r,children:e,containerInfo:t,implementation:n}}function Bp(e){if(!e)return Kt;e=e._reactInternals;e:{if(Cn(e)!==e||e.tag!==1)throw Error(T(170));var t=e;do{switch(t.tag){case 3:t=t.stateNode.context;break e;case 1:if(Le(t.type)){t=t.stateNode.__reactInternalMemoizedMergedChildContext;break e}}t=t.return}while(t!==null);throw Error(T(171))}if(e.tag===1){var n=e.type;if(Le(n))return Bh(e,n,t)}return t}function Up(e,t,n,r,i,o,s,l,a){return e=uu(n,r,!0,e,i,o,s,l,a),e.context=Bp(null),n=e.current,r=Pe(),i=$t(n),o=yt(r,i),o.callback=t??null,Bt(n,o,i),e.current.lanes=i,wi(e,i,r),Re(e,r),e}function fs(e,t,n,r){var i=t.current,o=Pe(),s=$t(i);return n=Bp(n),t.context===null?t.context=n:t.pendingContext=n,t=yt(o,s),t.payload={element:e},r=r===void 0?null:r,r!==null&&(t.callback=r),e=Bt(i,t,s),e!==null&&(it(e,i,s,o),oo(e,i,s)),s}function Uo(e){if(e=e.current,!e.child)return null;switch(e.child.tag){case 5:return e.child.stateNode;default:return e.child.stateNode}}function nf(e,t){if(e=e.memoizedState,e!==null&&e.dehydrated!==null){var n=e.retryLane;e.retryLane=n!==0&&n<t?n:t}}function cu(e,t){nf(e,t),(e=e.alternate)&&nf(e,t)}function q0(){return null}var $p=typeof reportError=="function"?reportError:function(e){console.error(e)};function fu(e){this._internalRoot=e}ds.prototype.render=fu.prototype.render=function(e){var t=this._internalRoot;if(t===null)throw Error(T(409));fs(e,t,null,null)};ds.prototype.unmount=fu.prototype.unmount=function(){var e=this._internalRoot;if(e!==null){this._internalRoot=null;var t=e.containerInfo;Sn(function(){fs(null,e,null,null)}),t[Et]=null}};function ds(e){this._internalRoot=e}ds.prototype.unstable_scheduleHydration=function(e){if(e){var t=wh();e={blockedOn:null,target:e,priority:t};for(var n=0;n<Rt.length&&t!==0&&t<Rt[n].priority;n++);Rt.splice(n,0,e),n===0&&xh(e)}};function du(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11)}function hs(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11&&(e.nodeType!==8||e.nodeValue!==" react-mount-point-unstable "))}function rf(){}function J0(e,t,n,r,i){if(i){if(typeof r=="function"){var o=r;r=function(){var u=Uo(s);o.call(u)}}var s=Up(t,r,e,0,null,!1,!1,"",rf);return e._reactRootContainer=s,e[Et]=s.current,ri(e.nodeType===8?e.parentNode:e),Sn(),s}for(;i=e.lastChild;)e.removeChild(i);if(typeof r=="function"){var l=r;r=function(){var u=Uo(a);l.call(u)}}var a=uu(e,0,!1,null,null,!1,!1,"",rf);return e._reactRootContainer=a,e[Et]=a.current,ri(e.nodeType===8?e.parentNode:e),Sn(function(){fs(t,a,n,r)}),a}function ps(e,t,n,r,i){var o=n._reactRootContainer;if(o){var s=o;if(typeof i=="function"){var l=i;i=function(){var a=Uo(s);l.call(a)}}fs(t,s,e,i)}else s=J0(n,t,e,i,r);return Uo(s)}vh=function(e){switch(e.tag){case 3:var t=e.stateNode;if(t.current.memoizedState.isDehydrated){var n=Mr(t.pendingLanes);n!==0&&(La(t,n|1),Re(t,re()),!(j&6)&&(or=re()+500,bt()))}break;case 13:Sn(function(){var r=Ct(e,1);if(r!==null){var i=Pe();it(r,e,1,i)}}),cu(e,1)}};Ra=function(e){if(e.tag===13){var t=Ct(e,134217728);if(t!==null){var n=Pe();it(t,e,134217728,n)}cu(e,134217728)}};yh=function(e){if(e.tag===13){var t=$t(e),n=Ct(e,t);if(n!==null){var r=Pe();it(n,e,t,r)}cu(e,t)}};wh=function(){return z};Sh=function(e,t){var n=z;try{return z=e,t()}finally{z=n}};El=function(e,t,n){switch(t){case"input":if(ml(e,n),t=n.name,n.type==="radio"&&t!=null){for(n=e;n.parentNode;)n=n.parentNode;for(n=n.querySelectorAll("input[name="+JSON.stringify(""+t)+'][type="radio"]'),t=0;t<n.length;t++){var r=n[t];if(r!==e&&r.form===e.form){var i=is(r);if(!i)throw Error(T(90));Zd(r),ml(r,i)}}}break;case"textarea":Jd(e,n);break;case"select":t=n.value,t!=null&&Qn(e,!!n.multiple,t,!1)}};sh=ou;lh=Sn;var ey={usingClientEntryPoint:!1,Events:[xi,On,is,ih,oh,ou]},Tr={findFiberByHostInstance:ln,bundleType:0,version:"18.3.1",rendererPackageName:"react-dom"},ty={bundleType:Tr.bundleType,version:Tr.version,rendererPackageName:Tr.rendererPackageName,rendererConfig:Tr.rendererConfig,overrideHookState:null,overrideHookStateDeletePath:null,overrideHookStateRenamePath:null,overrideProps:null,overridePropsDeletePath:null,overridePropsRenamePath:null,setErrorHandler:null,setSuspenseHandler:null,scheduleUpdate:null,currentDispatcherRef:kt.ReactCurrentDispatcher,findHostInstanceByFiber:function(e){return e=ch(e),e===null?null:e.stateNode},findFiberByHostInstance:Tr.findFiberByHostInstance||q0,findHostInstancesForRefresh:null,scheduleRefresh:null,scheduleRoot:null,setRefreshHandler:null,getCurrentFiber:null,reconcilerVersion:"18.3.1-next-f1338f8080-20240426"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"){var Xi=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!Xi.isDisabled&&Xi.supportsFiber)try{es=Xi.inject(ty),ut=Xi}catch{}}ze.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=ey;ze.createPortal=function(e,t){var n=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!du(t))throw Error(T(200));return Z0(e,t,null,n)};ze.createRoot=function(e,t){if(!du(e))throw Error(T(299));var n=!1,r="",i=$p;return t!=null&&(t.unstable_strictMode===!0&&(n=!0),t.identifierPrefix!==void 0&&(r=t.identifierPrefix),t.onRecoverableError!==void 0&&(i=t.onRecoverableError)),t=uu(e,1,!1,null,null,n,!1,r,i),e[Et]=t.current,ri(e.nodeType===8?e.parentNode:e),new fu(t)};ze.findDOMNode=function(e){if(e==null)return null;if(e.nodeType===1)return e;var t=e._reactInternals;if(t===void 0)throw typeof e.render=="function"?Error(T(188)):(e=Object.keys(e).join(","),Error(T(268,e)));return e=ch(t),e=e===null?null:e.stateNode,e};ze.flushSync=function(e){return Sn(e)};ze.hydrate=function(e,t,n){if(!hs(t))throw Error(T(200));return ps(null,e,t,!0,n)};ze.hydrateRoot=function(e,t,n){if(!du(e))throw Error(T(405));var r=n!=null&&n.hydratedSources||null,i=!1,o="",s=$p;if(n!=null&&(n.unstable_strictMode===!0&&(i=!0),n.identifierPrefix!==void 0&&(o=n.identifierPrefix),n.onRecoverableError!==void 0&&(s=n.onRecoverableError)),t=Up(t,null,e,1,n??null,i,!1,o,s),e[Et]=t.current,ri(e),r)for(e=0;e<r.length;e++)n=r[e],i=n._getVersion,i=i(n._source),t.mutableSourceEagerHydrationData==null?t.mutableSourceEagerHydrationData=[n,i]:t.mutableSourceEagerHydrationData.push(n,i);return new ds(t)};ze.render=function(e,t,n){if(!hs(t))throw Error(T(200));return ps(null,e,t,!1,n)};ze.unmountComponentAtNode=function(e){if(!hs(e))throw Error(T(40));return e._reactRootContainer?(Sn(function(){ps(null,null,e,!1,function(){e._reactRootContainer=null,e[Et]=null})}),!0):!1};ze.unstable_batchedUpdates=ou;ze.unstable_renderSubtreeIntoContainer=function(e,t,n,r){if(!hs(n))throw Error(T(200));if(e==null||e._reactInternals===void 0)throw Error(T(38));return ps(e,t,n,!1,r)};ze.version="18.3.1-next-f1338f8080-20240426";function Hp(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(Hp)}catch(e){console.error(e)}}Hp(),Hd.exports=ze;var ny=Hd.exports,of=ny;al.createRoot=of.createRoot,al.hydrateRoot=of.hydrateRoot;const hu=E.createContext({});function pu(e){const t=E.useRef(null);return t.current===null&&(t.current=e()),t.current}const ms=E.createContext(null),mu=E.createContext({transformPagePoint:e=>e,isStatic:!1,reducedMotion:"never"});class ry extends E.Component{getSnapshotBeforeUpdate(t){const n=this.props.childRef.current;if(n&&t.isPresent&&!this.props.isPresent){const r=this.props.sizeRef.current;r.height=n.offsetHeight||0,r.width=n.offsetWidth||0,r.top=n.offsetTop,r.left=n.offsetLeft}return null}componentDidUpdate(){}render(){return this.props.children}}function iy({children:e,isPresent:t}){const n=E.useId(),r=E.useRef(null),i=E.useRef({width:0,height:0,top:0,left:0}),{nonce:o}=E.useContext(mu);return E.useInsertionEffect(()=>{const{width:s,height:l,top:a,left:u}=i.current;if(t||!r.current||!s||!l)return;r.current.dataset.motionPopId=n;const c=document.createElement("style");return o&&(c.nonce=o),document.head.appendChild(c),c.sheet&&c.sheet.insertRule(`
          [data-motion-pop-id="${n}"] {
            position: absolute !important;
            width: ${s}px !important;
            height: ${l}px !important;
            top: ${a}px !important;
            left: ${u}px !important;
          }
        `),()=>{document.head.removeChild(c)}},[t]),y(ry,{isPresent:t,childRef:r,sizeRef:i,children:E.cloneElement(e,{ref:r})})}const oy=({children:e,initial:t,isPresent:n,onExitComplete:r,custom:i,presenceAffectsLayout:o,mode:s})=>{const l=pu(sy),a=E.useId(),u=E.useCallback(d=>{l.set(d,!0);for(const f of l.values())if(!f)return;r&&r()},[l,r]),c=E.useMemo(()=>({id:a,initial:t,isPresent:n,custom:i,onExitComplete:u,register:d=>(l.set(d,!1),()=>l.delete(d))}),o?[Math.random(),u]:[n,u]);return E.useMemo(()=>{l.forEach((d,f)=>l.set(f,!1))},[n]),E.useEffect(()=>{!n&&!l.size&&r&&r()},[n]),s==="popLayout"&&(e=y(iy,{isPresent:n,children:e})),y(ms.Provider,{value:c,children:e})};function sy(){return new Map}function Wp(e=!0){const t=E.useContext(ms);if(t===null)return[!0,null];const{isPresent:n,onExitComplete:r,register:i}=t,o=E.useId();E.useEffect(()=>{e&&i(o)},[e]);const s=E.useCallback(()=>e&&r&&r(o),[o,r,e]);return!n&&r?[!1,s]:[!0]}const Yi=e=>e.key||"";function sf(e){const t=[];return E.Children.forEach(e,n=>{E.isValidElement(n)&&t.push(n)}),t}const gu=typeof window<"u",Kp=gu?E.useLayoutEffect:E.useEffect,vu=({children:e,custom:t,initial:n=!0,onExitComplete:r,presenceAffectsLayout:i=!0,mode:o="sync",propagate:s=!1})=>{const[l,a]=Wp(s),u=E.useMemo(()=>sf(e),[e]),c=s&&!l?[]:u.map(Yi),d=E.useRef(!0),f=E.useRef(u),m=pu(()=>new Map),[v,w]=E.useState(u),[C,p]=E.useState(u);Kp(()=>{d.current=!1,f.current=u;for(let S=0;S<C.length;S++){const x=Yi(C[S]);c.includes(x)?m.delete(x):m.get(x)!==!0&&m.set(x,!1)}},[C,c.length,c.join("-")]);const h=[];if(u!==v){let S=[...u];for(let x=0;x<C.length;x++){const k=C[x],A=Yi(k);c.includes(A)||(S.splice(x,0,k),h.push(k))}o==="wait"&&h.length&&(S=h),p(sf(S)),w(u);return}const{forceRender:g}=E.useContext(hu);return y(Qe,{children:C.map(S=>{const x=Yi(S),k=s&&!l?!1:u===C||c.includes(x),A=()=>{if(m.has(x))m.set(x,!0);else return;let P=!0;m.forEach(I=>{I||(P=!1)}),P&&(g==null||g(),p(f.current),s&&(a==null||a()),r&&r())};return y(oy,{isPresent:k,initial:!d.current||n?void 0:!1,custom:k?void 0:t,presenceAffectsLayout:i,mode:o,onExitComplete:k?void 0:A,children:S},x)})})},Ie=e=>e;let Gp=Ie;function yu(e){let t;return()=>(t===void 0&&(t=e()),t)}const sr=(e,t,n)=>{const r=t-e;return r===0?1:(n-e)/r},wt=e=>e*1e3,St=e=>e/1e3,ly={skipAnimations:!1,useManualTiming:!1};function ay(e){let t=new Set,n=new Set,r=!1,i=!1;const o=new WeakSet;let s={delta:0,timestamp:0,isProcessing:!1};function l(u){o.has(u)&&(a.schedule(u),e()),u(s)}const a={schedule:(u,c=!1,d=!1)=>{const m=d&&r?t:n;return c&&o.add(u),m.has(u)||m.add(u),u},cancel:u=>{n.delete(u),o.delete(u)},process:u=>{if(s=u,r){i=!0;return}r=!0,[t,n]=[n,t],t.forEach(l),t.clear(),r=!1,i&&(i=!1,a.process(u))}};return a}const bi=["read","resolveKeyframes","update","preRender","render","postRender"],uy=40;function Qp(e,t){let n=!1,r=!0;const i={delta:0,timestamp:0,isProcessing:!1},o=()=>n=!0,s=bi.reduce((p,h)=>(p[h]=ay(o),p),{}),{read:l,resolveKeyframes:a,update:u,preRender:c,render:d,postRender:f}=s,m=()=>{const p=performance.now();n=!1,i.delta=r?1e3/60:Math.max(Math.min(p-i.timestamp,uy),1),i.timestamp=p,i.isProcessing=!0,l.process(i),a.process(i),u.process(i),c.process(i),d.process(i),f.process(i),i.isProcessing=!1,n&&t&&(r=!1,e(m))},v=()=>{n=!0,r=!0,i.isProcessing||e(m)};return{schedule:bi.reduce((p,h)=>{const g=s[h];return p[h]=(S,x=!1,k=!1)=>(n||v(),g.schedule(S,x,k)),p},{}),cancel:p=>{for(let h=0;h<bi.length;h++)s[bi[h]].cancel(p)},state:i,steps:s}}const{schedule:H,cancel:Gt,state:he,steps:Ys}=Qp(typeof requestAnimationFrame<"u"?requestAnimationFrame:Ie,!0),Xp=E.createContext({strict:!1}),lf={animation:["animate","variants","whileHover","whileTap","exit","whileInView","whileFocus","whileDrag"],exit:["exit"],drag:["drag","dragControls"],focus:["whileFocus"],hover:["whileHover","onHoverStart","onHoverEnd"],tap:["whileTap","onTap","onTapStart","onTapCancel"],pan:["onPan","onPanStart","onPanSessionStart","onPanEnd"],inView:["whileInView","onViewportEnter","onViewportLeave"],layout:["layout","layoutId"]},lr={};for(const e in lf)lr[e]={isEnabled:t=>lf[e].some(n=>!!t[n])};function cy(e){for(const t in e)lr[t]={...lr[t],...e[t]}}const fy=new Set(["animate","exit","variants","initial","style","values","variants","transition","transformTemplate","custom","inherit","onBeforeLayoutMeasure","onAnimationStart","onAnimationComplete","onUpdate","onDragStart","onDrag","onDragEnd","onMeasureDragConstraints","onDirectionLock","onDragTransitionEnd","_dragX","_dragY","onHoverStart","onHoverEnd","onViewportEnter","onViewportLeave","globalTapTarget","ignoreStrict","viewport"]);function $o(e){return e.startsWith("while")||e.startsWith("drag")&&e!=="draggable"||e.startsWith("layout")||e.startsWith("onTap")||e.startsWith("onPan")||e.startsWith("onLayout")||fy.has(e)}let Yp=e=>!$o(e);function dy(e){e&&(Yp=t=>t.startsWith("on")?!$o(t):e(t))}try{dy(require("@emotion/is-prop-valid").default)}catch{}function hy(e,t,n){const r={};for(const i in e)i==="values"&&typeof e.values=="object"||(Yp(i)||n===!0&&$o(i)||!t&&!$o(i)||e.draggable&&i.startsWith("onDrag"))&&(r[i]=e[i]);return r}function py(e){if(typeof Proxy>"u")return e;const t=new Map,n=(...r)=>e(...r);return new Proxy(n,{get:(r,i)=>i==="create"?e:(t.has(i)||t.set(i,e(i)),t.get(i))})}const gs=E.createContext({});function di(e){return typeof e=="string"||Array.isArray(e)}function vs(e){return e!==null&&typeof e=="object"&&typeof e.start=="function"}const wu=["animate","whileInView","whileFocus","whileHover","whileTap","whileDrag","exit"],Su=["initial",...wu];function ys(e){return vs(e.animate)||Su.some(t=>di(e[t]))}function bp(e){return!!(ys(e)||e.variants)}function my(e,t){if(ys(e)){const{initial:n,animate:r}=e;return{initial:n===!1||di(n)?n:void 0,animate:di(r)?r:void 0}}return e.inherit!==!1?t:{}}function gy(e){const{initial:t,animate:n}=my(e,E.useContext(gs));return E.useMemo(()=>({initial:t,animate:n}),[af(t),af(n)])}function af(e){return Array.isArray(e)?e.join(" "):e}const vy=Symbol.for("motionComponentSymbol");function $n(e){return e&&typeof e=="object"&&Object.prototype.hasOwnProperty.call(e,"current")}function yy(e,t,n){return E.useCallback(r=>{r&&e.onMount&&e.onMount(r),t&&(r?t.mount(r):t.unmount()),n&&(typeof n=="function"?n(r):$n(n)&&(n.current=r))},[t])}const xu=e=>e.replace(/([a-z])([A-Z])/gu,"$1-$2").toLowerCase(),wy="framerAppearId",Zp="data-"+xu(wy),{schedule:Eu,cancel:Nx}=Qp(queueMicrotask,!1),qp=E.createContext({});function Sy(e,t,n,r,i){var o,s;const{visualElement:l}=E.useContext(gs),a=E.useContext(Xp),u=E.useContext(ms),c=E.useContext(mu).reducedMotion,d=E.useRef(null);r=r||a.renderer,!d.current&&r&&(d.current=r(e,{visualState:t,parent:l,props:n,presenceContext:u,blockInitialAnimation:u?u.initial===!1:!1,reducedMotionConfig:c}));const f=d.current,m=E.useContext(qp);f&&!f.projection&&i&&(f.type==="html"||f.type==="svg")&&xy(d.current,n,i,m);const v=E.useRef(!1);E.useInsertionEffect(()=>{f&&v.current&&f.update(n,u)});const w=n[Zp],C=E.useRef(!!w&&!(!((o=window.MotionHandoffIsComplete)===null||o===void 0)&&o.call(window,w))&&((s=window.MotionHasOptimisedAnimation)===null||s===void 0?void 0:s.call(window,w)));return Kp(()=>{f&&(v.current=!0,window.MotionIsMounted=!0,f.updateFeatures(),Eu.render(f.render),C.current&&f.animationState&&f.animationState.animateChanges())}),E.useEffect(()=>{f&&(!C.current&&f.animationState&&f.animationState.animateChanges(),C.current&&(queueMicrotask(()=>{var p;(p=window.MotionHandoffMarkAsComplete)===null||p===void 0||p.call(window,w)}),C.current=!1))}),f}function xy(e,t,n,r){const{layoutId:i,layout:o,drag:s,dragConstraints:l,layoutScroll:a,layoutRoot:u}=t;e.projection=new n(e.latestValues,t["data-framer-portal-id"]?void 0:Jp(e.parent)),e.projection.setOptions({layoutId:i,layout:o,alwaysMeasureLayout:!!s||l&&$n(l),visualElement:e,animationType:typeof o=="string"?o:"both",initialPromotionConfig:r,layoutScroll:a,layoutRoot:u})}function Jp(e){if(e)return e.options.allowProjection!==!1?e.projection:Jp(e.parent)}function Ey({preloadedFeatures:e,createVisualElement:t,useRender:n,useVisualState:r,Component:i}){var o,s;e&&cy(e);function l(u,c){let d;const f={...E.useContext(mu),...u,layoutId:Cy(u)},{isStatic:m}=f,v=gy(u),w=r(u,m);if(!m&&gu){Py();const C=Ty(f);d=C.MeasureLayout,v.visualElement=Sy(i,w,f,t,C.ProjectionNode)}return L(gs.Provider,{value:v,children:[d&&v.visualElement?y(d,{visualElement:v.visualElement,...f}):null,n(i,u,yy(w,v.visualElement,c),w,m,v.visualElement)]})}l.displayName=`motion.${typeof i=="string"?i:`create(${(s=(o=i.displayName)!==null&&o!==void 0?o:i.name)!==null&&s!==void 0?s:""})`}`;const a=E.forwardRef(l);return a[vy]=i,a}function Cy({layoutId:e}){const t=E.useContext(hu).id;return t&&e!==void 0?t+"-"+e:e}function Py(e,t){E.useContext(Xp).strict}function Ty(e){const{drag:t,layout:n}=lr;if(!t&&!n)return{};const r={...t,...n};return{MeasureLayout:t!=null&&t.isEnabled(e)||n!=null&&n.isEnabled(e)?r.MeasureLayout:void 0,ProjectionNode:r.ProjectionNode}}const ky=["animate","circle","defs","desc","ellipse","g","image","line","filter","marker","mask","metadata","path","pattern","polygon","polyline","rect","stop","switch","symbol","svg","text","tspan","use","view"];function Cu(e){return typeof e!="string"||e.includes("-")?!1:!!(ky.indexOf(e)>-1||/[A-Z]/u.test(e))}function uf(e){const t=[{},{}];return e==null||e.values.forEach((n,r)=>{t[0][r]=n.get(),t[1][r]=n.getVelocity()}),t}function Pu(e,t,n,r){if(typeof t=="function"){const[i,o]=uf(r);t=t(n!==void 0?n:e.custom,i,o)}if(typeof t=="string"&&(t=e.variants&&e.variants[t]),typeof t=="function"){const[i,o]=uf(r);t=t(n!==void 0?n:e.custom,i,o)}return t}const ta=e=>Array.isArray(e),Ay=e=>!!(e&&typeof e=="object"&&e.mix&&e.toValue),Ny=e=>ta(e)?e[e.length-1]||0:e,xe=e=>!!(e&&e.getVelocity);function ho(e){const t=xe(e)?e.get():e;return Ay(t)?t.toValue():t}function My({scrapeMotionValuesFromProps:e,createRenderState:t,onUpdate:n},r,i,o){const s={latestValues:Dy(r,i,o,e),renderState:t()};return n&&(s.onMount=l=>n({props:r,current:l,...s}),s.onUpdate=l=>n(l)),s}const em=e=>(t,n)=>{const r=E.useContext(gs),i=E.useContext(ms),o=()=>My(e,t,r,i);return n?o():pu(o)};function Dy(e,t,n,r){const i={},o=r(e,{});for(const f in o)i[f]=ho(o[f]);let{initial:s,animate:l}=e;const a=ys(e),u=bp(e);t&&u&&!a&&e.inherit!==!1&&(s===void 0&&(s=t.initial),l===void 0&&(l=t.animate));let c=n?n.initial===!1:!1;c=c||s===!1;const d=c?l:s;if(d&&typeof d!="boolean"&&!vs(d)){const f=Array.isArray(d)?d:[d];for(let m=0;m<f.length;m++){const v=Pu(e,f[m]);if(v){const{transitionEnd:w,transition:C,...p}=v;for(const h in p){let g=p[h];if(Array.isArray(g)){const S=c?g.length-1:0;g=g[S]}g!==null&&(i[h]=g)}for(const h in w)i[h]=w[h]}}}return i}const pr=["transformPerspective","x","y","z","translateX","translateY","translateZ","scale","scaleX","scaleY","rotate","rotateX","rotateY","rotateZ","skew","skewX","skewY"],Pn=new Set(pr),tm=e=>t=>typeof t=="string"&&t.startsWith(e),nm=tm("--"),Ly=tm("var(--"),Tu=e=>Ly(e)?Ry.test(e.split("/*")[0].trim()):!1,Ry=/var\(--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)$/iu,rm=(e,t)=>t&&typeof e=="number"?t.transform(e):e,Tt=(e,t,n)=>n>t?t:n<e?e:n,mr={test:e=>typeof e=="number",parse:parseFloat,transform:e=>e},hi={...mr,transform:e=>Tt(0,1,e)},Zi={...mr,default:1},Ci=e=>({test:t=>typeof t=="string"&&t.endsWith(e)&&t.split(" ").length===1,parse:parseFloat,transform:t=>`${t}${e}`}),Mt=Ci("deg"),ft=Ci("%"),R=Ci("px"),_y=Ci("vh"),Vy=Ci("vw"),cf={...ft,parse:e=>ft.parse(e)/100,transform:e=>ft.transform(e*100)},Oy={borderWidth:R,borderTopWidth:R,borderRightWidth:R,borderBottomWidth:R,borderLeftWidth:R,borderRadius:R,radius:R,borderTopLeftRadius:R,borderTopRightRadius:R,borderBottomRightRadius:R,borderBottomLeftRadius:R,width:R,maxWidth:R,height:R,maxHeight:R,top:R,right:R,bottom:R,left:R,padding:R,paddingTop:R,paddingRight:R,paddingBottom:R,paddingLeft:R,margin:R,marginTop:R,marginRight:R,marginBottom:R,marginLeft:R,backgroundPositionX:R,backgroundPositionY:R},Iy={rotate:Mt,rotateX:Mt,rotateY:Mt,rotateZ:Mt,scale:Zi,scaleX:Zi,scaleY:Zi,scaleZ:Zi,skew:Mt,skewX:Mt,skewY:Mt,distance:R,translateX:R,translateY:R,translateZ:R,x:R,y:R,z:R,perspective:R,transformPerspective:R,opacity:hi,originX:cf,originY:cf,originZ:R},ff={...mr,transform:Math.round},ku={...Oy,...Iy,zIndex:ff,size:R,fillOpacity:hi,strokeOpacity:hi,numOctaves:ff},Fy={x:"translateX",y:"translateY",z:"translateZ",transformPerspective:"perspective"},jy=pr.length;function zy(e,t,n){let r="",i=!0;for(let o=0;o<jy;o++){const s=pr[o],l=e[s];if(l===void 0)continue;let a=!0;if(typeof l=="number"?a=l===(s.startsWith("scale")?1:0):a=parseFloat(l)===0,!a||n){const u=rm(l,ku[s]);if(!a){i=!1;const c=Fy[s]||s;r+=`${c}(${u}) `}n&&(t[s]=u)}}return r=r.trim(),n?r=n(t,i?"":r):i&&(r="none"),r}function Au(e,t,n){const{style:r,vars:i,transformOrigin:o}=e;let s=!1,l=!1;for(const a in t){const u=t[a];if(Pn.has(a)){s=!0;continue}else if(nm(a)){i[a]=u;continue}else{const c=rm(u,ku[a]);a.startsWith("origin")?(l=!0,o[a]=c):r[a]=c}}if(t.transform||(s||n?r.transform=zy(t,e.transform,n):r.transform&&(r.transform="none")),l){const{originX:a="50%",originY:u="50%",originZ:c=0}=o;r.transformOrigin=`${a} ${u} ${c}`}}const By={offset:"stroke-dashoffset",array:"stroke-dasharray"},Uy={offset:"strokeDashoffset",array:"strokeDasharray"};function $y(e,t,n=1,r=0,i=!0){e.pathLength=1;const o=i?By:Uy;e[o.offset]=R.transform(-r);const s=R.transform(t),l=R.transform(n);e[o.array]=`${s} ${l}`}function df(e,t,n){return typeof e=="string"?e:R.transform(t+n*e)}function Hy(e,t,n){const r=df(t,e.x,e.width),i=df(n,e.y,e.height);return`${r} ${i}`}function Nu(e,{attrX:t,attrY:n,attrScale:r,originX:i,originY:o,pathLength:s,pathSpacing:l=1,pathOffset:a=0,...u},c,d){if(Au(e,u,d),c){e.style.viewBox&&(e.attrs.viewBox=e.style.viewBox);return}e.attrs=e.style,e.style={};const{attrs:f,style:m,dimensions:v}=e;f.transform&&(v&&(m.transform=f.transform),delete f.transform),v&&(i!==void 0||o!==void 0||m.transform)&&(m.transformOrigin=Hy(v,i!==void 0?i:.5,o!==void 0?o:.5)),t!==void 0&&(f.x=t),n!==void 0&&(f.y=n),r!==void 0&&(f.scale=r),s!==void 0&&$y(f,s,l,a,!1)}const Mu=()=>({style:{},transform:{},transformOrigin:{},vars:{}}),im=()=>({...Mu(),attrs:{}}),Du=e=>typeof e=="string"&&e.toLowerCase()==="svg";function om(e,{style:t,vars:n},r,i){Object.assign(e.style,t,i&&i.getProjectionStyles(r));for(const o in n)e.style.setProperty(o,n[o])}const sm=new Set(["baseFrequency","diffuseConstant","kernelMatrix","kernelUnitLength","keySplines","keyTimes","limitingConeAngle","markerHeight","markerWidth","numOctaves","targetX","targetY","surfaceScale","specularConstant","specularExponent","stdDeviation","tableValues","viewBox","gradientTransform","pathLength","startOffset","textLength","lengthAdjust"]);function lm(e,t,n,r){om(e,t,void 0,r);for(const i in t.attrs)e.setAttribute(sm.has(i)?i:xu(i),t.attrs[i])}const Ho={};function Wy(e){Object.assign(Ho,e)}function am(e,{layout:t,layoutId:n}){return Pn.has(e)||e.startsWith("origin")||(t||n!==void 0)&&(!!Ho[e]||e==="opacity")}function Lu(e,t,n){var r;const{style:i}=e,o={};for(const s in i)(xe(i[s])||t.style&&xe(t.style[s])||am(s,e)||((r=n==null?void 0:n.getValue(s))===null||r===void 0?void 0:r.liveStyle)!==void 0)&&(o[s]=i[s]);return o}function um(e,t,n){const r=Lu(e,t,n);for(const i in e)if(xe(e[i])||xe(t[i])){const o=pr.indexOf(i)!==-1?"attr"+i.charAt(0).toUpperCase()+i.substring(1):i;r[o]=e[i]}return r}function Ky(e,t){try{t.dimensions=typeof e.getBBox=="function"?e.getBBox():e.getBoundingClientRect()}catch{t.dimensions={x:0,y:0,width:0,height:0}}}const hf=["x","y","width","height","cx","cy","r"],Gy={useVisualState:em({scrapeMotionValuesFromProps:um,createRenderState:im,onUpdate:({props:e,prevProps:t,current:n,renderState:r,latestValues:i})=>{if(!n)return;let o=!!e.drag;if(!o){for(const l in i)if(Pn.has(l)){o=!0;break}}if(!o)return;let s=!t;if(t)for(let l=0;l<hf.length;l++){const a=hf[l];e[a]!==t[a]&&(s=!0)}s&&H.read(()=>{Ky(n,r),H.render(()=>{Nu(r,i,Du(n.tagName),e.transformTemplate),lm(n,r)})})}})},Qy={useVisualState:em({scrapeMotionValuesFromProps:Lu,createRenderState:Mu})};function cm(e,t,n){for(const r in t)!xe(t[r])&&!am(r,n)&&(e[r]=t[r])}function Xy({transformTemplate:e},t){return E.useMemo(()=>{const n=Mu();return Au(n,t,e),Object.assign({},n.vars,n.style)},[t])}function Yy(e,t){const n=e.style||{},r={};return cm(r,n,e),Object.assign(r,Xy(e,t)),r}function by(e,t){const n={},r=Yy(e,t);return e.drag&&e.dragListener!==!1&&(n.draggable=!1,r.userSelect=r.WebkitUserSelect=r.WebkitTouchCallout="none",r.touchAction=e.drag===!0?"none":`pan-${e.drag==="x"?"y":"x"}`),e.tabIndex===void 0&&(e.onTap||e.onTapStart||e.whileTap)&&(n.tabIndex=0),n.style=r,n}function Zy(e,t,n,r){const i=E.useMemo(()=>{const o=im();return Nu(o,t,Du(r),e.transformTemplate),{...o.attrs,style:{...o.style}}},[t]);if(e.style){const o={};cm(o,e.style,e),i.style={...o,...i.style}}return i}function qy(e=!1){return(n,r,i,{latestValues:o},s)=>{const a=(Cu(n)?Zy:by)(r,o,s,n),u=hy(r,typeof n=="string",e),c=n!==E.Fragment?{...u,...a,ref:i}:{},{children:d}=r,f=E.useMemo(()=>xe(d)?d.get():d,[d]);return E.createElement(n,{...c,children:f})}}function Jy(e,t){return function(r,{forwardMotionProps:i}={forwardMotionProps:!1}){const s={...Cu(r)?Gy:Qy,preloadedFeatures:e,useRender:qy(i),createVisualElement:t,Component:r};return Ey(s)}}function fm(e,t){if(!Array.isArray(t))return!1;const n=t.length;if(n!==e.length)return!1;for(let r=0;r<n;r++)if(t[r]!==e[r])return!1;return!0}function ws(e,t,n){const r=e.getProps();return Pu(r,t,n!==void 0?n:r.custom,e)}const e1=yu(()=>window.ScrollTimeline!==void 0);class t1{constructor(t){this.stop=()=>this.runAll("stop"),this.animations=t.filter(Boolean)}get finished(){return Promise.all(this.animations.map(t=>"finished"in t?t.finished:t))}getAll(t){return this.animations[0][t]}setAll(t,n){for(let r=0;r<this.animations.length;r++)this.animations[r][t]=n}attachTimeline(t,n){const r=this.animations.map(i=>{if(e1()&&i.attachTimeline)return i.attachTimeline(t);if(typeof n=="function")return n(i)});return()=>{r.forEach((i,o)=>{i&&i(),this.animations[o].stop()})}}get time(){return this.getAll("time")}set time(t){this.setAll("time",t)}get speed(){return this.getAll("speed")}set speed(t){this.setAll("speed",t)}get startTime(){return this.getAll("startTime")}get duration(){let t=0;for(let n=0;n<this.animations.length;n++)t=Math.max(t,this.animations[n].duration);return t}runAll(t){this.animations.forEach(n=>n[t]())}flatten(){this.runAll("flatten")}play(){this.runAll("play")}pause(){this.runAll("pause")}cancel(){this.runAll("cancel")}complete(){this.runAll("complete")}}class n1 extends t1{then(t,n){return Promise.all(this.animations).then(t).catch(n)}}function Ru(e,t){return e?e[t]||e.default||e:void 0}const na=2e4;function dm(e){let t=0;const n=50;let r=e.next(t);for(;!r.done&&t<na;)t+=n,r=e.next(t);return t>=na?1/0:t}function _u(e){return typeof e=="function"}function pf(e,t){e.timeline=t,e.onfinish=null}const Vu=e=>Array.isArray(e)&&typeof e[0]=="number",r1={linearEasing:void 0};function i1(e,t){const n=yu(e);return()=>{var r;return(r=r1[t])!==null&&r!==void 0?r:n()}}const Wo=i1(()=>{try{document.createElement("div").animate({opacity:0},{easing:"linear(0, 1)"})}catch{return!1}return!0},"linearEasing"),hm=(e,t,n=10)=>{let r="";const i=Math.max(Math.round(t/n),2);for(let o=0;o<i;o++)r+=e(sr(0,i-1,o))+", ";return`linear(${r.substring(0,r.length-2)})`};function pm(e){return!!(typeof e=="function"&&Wo()||!e||typeof e=="string"&&(e in ra||Wo())||Vu(e)||Array.isArray(e)&&e.every(pm))}const Lr=([e,t,n,r])=>`cubic-bezier(${e}, ${t}, ${n}, ${r})`,ra={linear:"linear",ease:"ease",easeIn:"ease-in",easeOut:"ease-out",easeInOut:"ease-in-out",circIn:Lr([0,.65,.55,1]),circOut:Lr([.55,0,1,.45]),backIn:Lr([.31,.01,.66,-.59]),backOut:Lr([.33,1.53,.69,.99])};function mm(e,t){if(e)return typeof e=="function"&&Wo()?hm(e,t):Vu(e)?Lr(e):Array.isArray(e)?e.map(n=>mm(n,t)||ra.easeOut):ra[e]}const Ze={x:!1,y:!1};function gm(){return Ze.x||Ze.y}function o1(e,t,n){var r;if(e instanceof Element)return[e];if(typeof e=="string"){let i=document;t&&(i=t.current);const o=(r=n==null?void 0:n[e])!==null&&r!==void 0?r:i.querySelectorAll(e);return o?Array.from(o):[]}return Array.from(e)}function vm(e,t){const n=o1(e),r=new AbortController,i={passive:!0,...t,signal:r.signal};return[n,i,()=>r.abort()]}function mf(e){return t=>{t.pointerType==="touch"||gm()||e(t)}}function s1(e,t,n={}){const[r,i,o]=vm(e,n),s=mf(l=>{const{target:a}=l,u=t(l);if(typeof u!="function"||!a)return;const c=mf(d=>{u(d),a.removeEventListener("pointerleave",c)});a.addEventListener("pointerleave",c,i)});return r.forEach(l=>{l.addEventListener("pointerenter",s,i)}),o}const ym=(e,t)=>t?e===t?!0:ym(e,t.parentElement):!1,Ou=e=>e.pointerType==="mouse"?typeof e.button!="number"||e.button<=0:e.isPrimary!==!1,l1=new Set(["BUTTON","INPUT","SELECT","TEXTAREA","A"]);function a1(e){return l1.has(e.tagName)||e.tabIndex!==-1}const Rr=new WeakSet;function gf(e){return t=>{t.key==="Enter"&&e(t)}}function bs(e,t){e.dispatchEvent(new PointerEvent("pointer"+t,{isPrimary:!0,bubbles:!0}))}const u1=(e,t)=>{const n=e.currentTarget;if(!n)return;const r=gf(()=>{if(Rr.has(n))return;bs(n,"down");const i=gf(()=>{bs(n,"up")}),o=()=>bs(n,"cancel");n.addEventListener("keyup",i,t),n.addEventListener("blur",o,t)});n.addEventListener("keydown",r,t),n.addEventListener("blur",()=>n.removeEventListener("keydown",r),t)};function vf(e){return Ou(e)&&!gm()}function c1(e,t,n={}){const[r,i,o]=vm(e,n),s=l=>{const a=l.currentTarget;if(!vf(l)||Rr.has(a))return;Rr.add(a);const u=t(l),c=(m,v)=>{window.removeEventListener("pointerup",d),window.removeEventListener("pointercancel",f),!(!vf(m)||!Rr.has(a))&&(Rr.delete(a),typeof u=="function"&&u(m,{success:v}))},d=m=>{c(m,n.useGlobalTarget||ym(a,m.target))},f=m=>{c(m,!1)};window.addEventListener("pointerup",d,i),window.addEventListener("pointercancel",f,i)};return r.forEach(l=>{!a1(l)&&l.getAttribute("tabindex")===null&&(l.tabIndex=0),(n.useGlobalTarget?window:l).addEventListener("pointerdown",s,i),l.addEventListener("focus",u=>u1(u,i),i)}),o}function f1(e){return e==="x"||e==="y"?Ze[e]?null:(Ze[e]=!0,()=>{Ze[e]=!1}):Ze.x||Ze.y?null:(Ze.x=Ze.y=!0,()=>{Ze.x=Ze.y=!1})}const wm=new Set(["width","height","top","left","right","bottom",...pr]);let po;function d1(){po=void 0}const dt={now:()=>(po===void 0&&dt.set(he.isProcessing||ly.useManualTiming?he.timestamp:performance.now()),po),set:e=>{po=e,queueMicrotask(d1)}};function Iu(e,t){e.indexOf(t)===-1&&e.push(t)}function Fu(e,t){const n=e.indexOf(t);n>-1&&e.splice(n,1)}class ju{constructor(){this.subscriptions=[]}add(t){return Iu(this.subscriptions,t),()=>Fu(this.subscriptions,t)}notify(t,n,r){const i=this.subscriptions.length;if(i)if(i===1)this.subscriptions[0](t,n,r);else for(let o=0;o<i;o++){const s=this.subscriptions[o];s&&s(t,n,r)}}getSize(){return this.subscriptions.length}clear(){this.subscriptions.length=0}}function Sm(e,t){return t?e*(1e3/t):0}const yf=30,h1=e=>!isNaN(parseFloat(e));class p1{constructor(t,n={}){this.version="11.18.2",this.canTrackVelocity=null,this.events={},this.updateAndNotify=(r,i=!0)=>{const o=dt.now();this.updatedAt!==o&&this.setPrevFrameValue(),this.prev=this.current,this.setCurrent(r),this.current!==this.prev&&this.events.change&&this.events.change.notify(this.current),i&&this.events.renderRequest&&this.events.renderRequest.notify(this.current)},this.hasAnimated=!1,this.setCurrent(t),this.owner=n.owner}setCurrent(t){this.current=t,this.updatedAt=dt.now(),this.canTrackVelocity===null&&t!==void 0&&(this.canTrackVelocity=h1(this.current))}setPrevFrameValue(t=this.current){this.prevFrameValue=t,this.prevUpdatedAt=this.updatedAt}onChange(t){return this.on("change",t)}on(t,n){this.events[t]||(this.events[t]=new ju);const r=this.events[t].add(n);return t==="change"?()=>{r(),H.read(()=>{this.events.change.getSize()||this.stop()})}:r}clearListeners(){for(const t in this.events)this.events[t].clear()}attach(t,n){this.passiveEffect=t,this.stopPassiveEffect=n}set(t,n=!0){!n||!this.passiveEffect?this.updateAndNotify(t,n):this.passiveEffect(t,this.updateAndNotify)}setWithVelocity(t,n,r){this.set(n),this.prev=void 0,this.prevFrameValue=t,this.prevUpdatedAt=this.updatedAt-r}jump(t,n=!0){this.updateAndNotify(t),this.prev=t,this.prevUpdatedAt=this.prevFrameValue=void 0,n&&this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}get(){return this.current}getPrevious(){return this.prev}getVelocity(){const t=dt.now();if(!this.canTrackVelocity||this.prevFrameValue===void 0||t-this.updatedAt>yf)return 0;const n=Math.min(this.updatedAt-this.prevUpdatedAt,yf);return Sm(parseFloat(this.current)-parseFloat(this.prevFrameValue),n)}start(t){return this.stop(),new Promise(n=>{this.hasAnimated=!0,this.animation=t(n),this.events.animationStart&&this.events.animationStart.notify()}).then(()=>{this.events.animationComplete&&this.events.animationComplete.notify(),this.clearAnimation()})}stop(){this.animation&&(this.animation.stop(),this.events.animationCancel&&this.events.animationCancel.notify()),this.clearAnimation()}isAnimating(){return!!this.animation}clearAnimation(){delete this.animation}destroy(){this.clearListeners(),this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}}function pi(e,t){return new p1(e,t)}function m1(e,t,n){e.hasValue(t)?e.getValue(t).set(n):e.addValue(t,pi(n))}function g1(e,t){const n=ws(e,t);let{transitionEnd:r={},transition:i={},...o}=n||{};o={...o,...r};for(const s in o){const l=Ny(o[s]);m1(e,s,l)}}function v1(e){return!!(xe(e)&&e.add)}function ia(e,t){const n=e.getValue("willChange");if(v1(n))return n.add(t)}function xm(e){return e.props[Zp]}const Em=(e,t,n)=>(((1-3*n+3*t)*e+(3*n-6*t))*e+3*t)*e,y1=1e-7,w1=12;function S1(e,t,n,r,i){let o,s,l=0;do s=t+(n-t)/2,o=Em(s,r,i)-e,o>0?n=s:t=s;while(Math.abs(o)>y1&&++l<w1);return s}function Pi(e,t,n,r){if(e===t&&n===r)return Ie;const i=o=>S1(o,0,1,e,n);return o=>o===0||o===1?o:Em(i(o),t,r)}const Cm=e=>t=>t<=.5?e(2*t)/2:(2-e(2*(1-t)))/2,Pm=e=>t=>1-e(1-t),Tm=Pi(.33,1.53,.69,.99),zu=Pm(Tm),km=Cm(zu),Am=e=>(e*=2)<1?.5*zu(e):.5*(2-Math.pow(2,-10*(e-1))),Bu=e=>1-Math.sin(Math.acos(e)),Nm=Pm(Bu),Mm=Cm(Bu),Dm=e=>/^0[^.\s]+$/u.test(e);function x1(e){return typeof e=="number"?e===0:e!==null?e==="none"||e==="0"||Dm(e):!0}const Hr=e=>Math.round(e*1e5)/1e5,Uu=/-?(?:\d+(?:\.\d+)?|\.\d+)/gu;function E1(e){return e==null}const C1=/^(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))$/iu,$u=(e,t)=>n=>!!(typeof n=="string"&&C1.test(n)&&n.startsWith(e)||t&&!E1(n)&&Object.prototype.hasOwnProperty.call(n,t)),Lm=(e,t,n)=>r=>{if(typeof r!="string")return r;const[i,o,s,l]=r.match(Uu);return{[e]:parseFloat(i),[t]:parseFloat(o),[n]:parseFloat(s),alpha:l!==void 0?parseFloat(l):1}},P1=e=>Tt(0,255,e),Zs={...mr,transform:e=>Math.round(P1(e))},cn={test:$u("rgb","red"),parse:Lm("red","green","blue"),transform:({red:e,green:t,blue:n,alpha:r=1})=>"rgba("+Zs.transform(e)+", "+Zs.transform(t)+", "+Zs.transform(n)+", "+Hr(hi.transform(r))+")"};function T1(e){let t="",n="",r="",i="";return e.length>5?(t=e.substring(1,3),n=e.substring(3,5),r=e.substring(5,7),i=e.substring(7,9)):(t=e.substring(1,2),n=e.substring(2,3),r=e.substring(3,4),i=e.substring(4,5),t+=t,n+=n,r+=r,i+=i),{red:parseInt(t,16),green:parseInt(n,16),blue:parseInt(r,16),alpha:i?parseInt(i,16)/255:1}}const oa={test:$u("#"),parse:T1,transform:cn.transform},Hn={test:$u("hsl","hue"),parse:Lm("hue","saturation","lightness"),transform:({hue:e,saturation:t,lightness:n,alpha:r=1})=>"hsla("+Math.round(e)+", "+ft.transform(Hr(t))+", "+ft.transform(Hr(n))+", "+Hr(hi.transform(r))+")"},we={test:e=>cn.test(e)||oa.test(e)||Hn.test(e),parse:e=>cn.test(e)?cn.parse(e):Hn.test(e)?Hn.parse(e):oa.parse(e),transform:e=>typeof e=="string"?e:e.hasOwnProperty("red")?cn.transform(e):Hn.transform(e)},k1=/(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))/giu;function A1(e){var t,n;return isNaN(e)&&typeof e=="string"&&(((t=e.match(Uu))===null||t===void 0?void 0:t.length)||0)+(((n=e.match(k1))===null||n===void 0?void 0:n.length)||0)>0}const Rm="number",_m="color",N1="var",M1="var(",wf="${}",D1=/var\s*\(\s*--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)|#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\)|-?(?:\d+(?:\.\d+)?|\.\d+)/giu;function mi(e){const t=e.toString(),n=[],r={color:[],number:[],var:[]},i=[];let o=0;const l=t.replace(D1,a=>(we.test(a)?(r.color.push(o),i.push(_m),n.push(we.parse(a))):a.startsWith(M1)?(r.var.push(o),i.push(N1),n.push(a)):(r.number.push(o),i.push(Rm),n.push(parseFloat(a))),++o,wf)).split(wf);return{values:n,split:l,indexes:r,types:i}}function Vm(e){return mi(e).values}function Om(e){const{split:t,types:n}=mi(e),r=t.length;return i=>{let o="";for(let s=0;s<r;s++)if(o+=t[s],i[s]!==void 0){const l=n[s];l===Rm?o+=Hr(i[s]):l===_m?o+=we.transform(i[s]):o+=i[s]}return o}}const L1=e=>typeof e=="number"?0:e;function R1(e){const t=Vm(e);return Om(e)(t.map(L1))}const Qt={test:A1,parse:Vm,createTransformer:Om,getAnimatableNone:R1},_1=new Set(["brightness","contrast","saturate","opacity"]);function V1(e){const[t,n]=e.slice(0,-1).split("(");if(t==="drop-shadow")return e;const[r]=n.match(Uu)||[];if(!r)return e;const i=n.replace(r,"");let o=_1.has(t)?1:0;return r!==n&&(o*=100),t+"("+o+i+")"}const O1=/\b([a-z-]*)\(.*?\)/gu,sa={...Qt,getAnimatableNone:e=>{const t=e.match(O1);return t?t.map(V1).join(" "):e}},I1={...ku,color:we,backgroundColor:we,outlineColor:we,fill:we,stroke:we,borderColor:we,borderTopColor:we,borderRightColor:we,borderBottomColor:we,borderLeftColor:we,filter:sa,WebkitFilter:sa},Hu=e=>I1[e];function Im(e,t){let n=Hu(e);return n!==sa&&(n=Qt),n.getAnimatableNone?n.getAnimatableNone(t):void 0}const F1=new Set(["auto","none","0"]);function j1(e,t,n){let r=0,i;for(;r<e.length&&!i;){const o=e[r];typeof o=="string"&&!F1.has(o)&&mi(o).values.length&&(i=e[r]),r++}if(i&&n)for(const o of t)e[o]=Im(n,i)}const Sf=e=>e===mr||e===R,xf=(e,t)=>parseFloat(e.split(", ")[t]),Ef=(e,t)=>(n,{transform:r})=>{if(r==="none"||!r)return 0;const i=r.match(/^matrix3d\((.+)\)$/u);if(i)return xf(i[1],t);{const o=r.match(/^matrix\((.+)\)$/u);return o?xf(o[1],e):0}},z1=new Set(["x","y","z"]),B1=pr.filter(e=>!z1.has(e));function U1(e){const t=[];return B1.forEach(n=>{const r=e.getValue(n);r!==void 0&&(t.push([n,r.get()]),r.set(n.startsWith("scale")?1:0))}),t}const ar={width:({x:e},{paddingLeft:t="0",paddingRight:n="0"})=>e.max-e.min-parseFloat(t)-parseFloat(n),height:({y:e},{paddingTop:t="0",paddingBottom:n="0"})=>e.max-e.min-parseFloat(t)-parseFloat(n),top:(e,{top:t})=>parseFloat(t),left:(e,{left:t})=>parseFloat(t),bottom:({y:e},{top:t})=>parseFloat(t)+(e.max-e.min),right:({x:e},{left:t})=>parseFloat(t)+(e.max-e.min),x:Ef(4,13),y:Ef(5,14)};ar.translateX=ar.x;ar.translateY=ar.y;const pn=new Set;let la=!1,aa=!1;function Fm(){if(aa){const e=Array.from(pn).filter(r=>r.needsMeasurement),t=new Set(e.map(r=>r.element)),n=new Map;t.forEach(r=>{const i=U1(r);i.length&&(n.set(r,i),r.render())}),e.forEach(r=>r.measureInitialState()),t.forEach(r=>{r.render();const i=n.get(r);i&&i.forEach(([o,s])=>{var l;(l=r.getValue(o))===null||l===void 0||l.set(s)})}),e.forEach(r=>r.measureEndState()),e.forEach(r=>{r.suspendedScrollY!==void 0&&window.scrollTo(0,r.suspendedScrollY)})}aa=!1,la=!1,pn.forEach(e=>e.complete()),pn.clear()}function jm(){pn.forEach(e=>{e.readKeyframes(),e.needsMeasurement&&(aa=!0)})}function $1(){jm(),Fm()}class Wu{constructor(t,n,r,i,o,s=!1){this.isComplete=!1,this.isAsync=!1,this.needsMeasurement=!1,this.isScheduled=!1,this.unresolvedKeyframes=[...t],this.onComplete=n,this.name=r,this.motionValue=i,this.element=o,this.isAsync=s}scheduleResolve(){this.isScheduled=!0,this.isAsync?(pn.add(this),la||(la=!0,H.read(jm),H.resolveKeyframes(Fm))):(this.readKeyframes(),this.complete())}readKeyframes(){const{unresolvedKeyframes:t,name:n,element:r,motionValue:i}=this;for(let o=0;o<t.length;o++)if(t[o]===null)if(o===0){const s=i==null?void 0:i.get(),l=t[t.length-1];if(s!==void 0)t[0]=s;else if(r&&n){const a=r.readValue(n,l);a!=null&&(t[0]=a)}t[0]===void 0&&(t[0]=l),i&&s===void 0&&i.set(t[0])}else t[o]=t[o-1]}setFinalKeyframe(){}measureInitialState(){}renderEndStyles(){}measureEndState(){}complete(){this.isComplete=!0,this.onComplete(this.unresolvedKeyframes,this.finalKeyframe),pn.delete(this)}cancel(){this.isComplete||(this.isScheduled=!1,pn.delete(this))}resume(){this.isComplete||this.scheduleResolve()}}const zm=e=>/^-?(?:\d+(?:\.\d+)?|\.\d+)$/u.test(e),H1=/^var\(--(?:([\w-]+)|([\w-]+), ?([a-zA-Z\d ()%#.,-]+))\)/u;function W1(e){const t=H1.exec(e);if(!t)return[,];const[,n,r,i]=t;return[`--${n??r}`,i]}function Bm(e,t,n=1){const[r,i]=W1(e);if(!r)return;const o=window.getComputedStyle(t).getPropertyValue(r);if(o){const s=o.trim();return zm(s)?parseFloat(s):s}return Tu(i)?Bm(i,t,n+1):i}const Um=e=>t=>t.test(e),K1={test:e=>e==="auto",parse:e=>e},$m=[mr,R,ft,Mt,Vy,_y,K1],Cf=e=>$m.find(Um(e));class Hm extends Wu{constructor(t,n,r,i,o){super(t,n,r,i,o,!0)}readKeyframes(){const{unresolvedKeyframes:t,element:n,name:r}=this;if(!n||!n.current)return;super.readKeyframes();for(let a=0;a<t.length;a++){let u=t[a];if(typeof u=="string"&&(u=u.trim(),Tu(u))){const c=Bm(u,n.current);c!==void 0&&(t[a]=c),a===t.length-1&&(this.finalKeyframe=u)}}if(this.resolveNoneKeyframes(),!wm.has(r)||t.length!==2)return;const[i,o]=t,s=Cf(i),l=Cf(o);if(s!==l)if(Sf(s)&&Sf(l))for(let a=0;a<t.length;a++){const u=t[a];typeof u=="string"&&(t[a]=parseFloat(u))}else this.needsMeasurement=!0}resolveNoneKeyframes(){const{unresolvedKeyframes:t,name:n}=this,r=[];for(let i=0;i<t.length;i++)x1(t[i])&&r.push(i);r.length&&j1(t,r,n)}measureInitialState(){const{element:t,unresolvedKeyframes:n,name:r}=this;if(!t||!t.current)return;r==="height"&&(this.suspendedScrollY=window.pageYOffset),this.measuredOrigin=ar[r](t.measureViewportBox(),window.getComputedStyle(t.current)),n[0]=this.measuredOrigin;const i=n[n.length-1];i!==void 0&&t.getValue(r,i).jump(i,!1)}measureEndState(){var t;const{element:n,name:r,unresolvedKeyframes:i}=this;if(!n||!n.current)return;const o=n.getValue(r);o&&o.jump(this.measuredOrigin,!1);const s=i.length-1,l=i[s];i[s]=ar[r](n.measureViewportBox(),window.getComputedStyle(n.current)),l!==null&&this.finalKeyframe===void 0&&(this.finalKeyframe=l),!((t=this.removedTransforms)===null||t===void 0)&&t.length&&this.removedTransforms.forEach(([a,u])=>{n.getValue(a).set(u)}),this.resolveNoneKeyframes()}}const Pf=(e,t)=>t==="zIndex"?!1:!!(typeof e=="number"||Array.isArray(e)||typeof e=="string"&&(Qt.test(e)||e==="0")&&!e.startsWith("url("));function G1(e){const t=e[0];if(e.length===1)return!0;for(let n=0;n<e.length;n++)if(e[n]!==t)return!0}function Q1(e,t,n,r){const i=e[0];if(i===null)return!1;if(t==="display"||t==="visibility")return!0;const o=e[e.length-1],s=Pf(i,t),l=Pf(o,t);return!s||!l?!1:G1(e)||(n==="spring"||_u(n))&&r}const X1=e=>e!==null;function Ss(e,{repeat:t,repeatType:n="loop"},r){const i=e.filter(X1),o=t&&n!=="loop"&&t%2===1?0:i.length-1;return!o||r===void 0?i[o]:r}const Y1=40;class Wm{constructor({autoplay:t=!0,delay:n=0,type:r="keyframes",repeat:i=0,repeatDelay:o=0,repeatType:s="loop",...l}){this.isStopped=!1,this.hasAttemptedResolve=!1,this.createdAt=dt.now(),this.options={autoplay:t,delay:n,type:r,repeat:i,repeatDelay:o,repeatType:s,...l},this.updateFinishedPromise()}calcStartTime(){return this.resolvedAt?this.resolvedAt-this.createdAt>Y1?this.resolvedAt:this.createdAt:this.createdAt}get resolved(){return!this._resolved&&!this.hasAttemptedResolve&&$1(),this._resolved}onKeyframesResolved(t,n){this.resolvedAt=dt.now(),this.hasAttemptedResolve=!0;const{name:r,type:i,velocity:o,delay:s,onComplete:l,onUpdate:a,isGenerator:u}=this.options;if(!u&&!Q1(t,r,i,o))if(s)this.options.duration=0;else{a&&a(Ss(t,this.options,n)),l&&l(),this.resolveFinishedPromise();return}const c=this.initPlayback(t,n);c!==!1&&(this._resolved={keyframes:t,finalKeyframe:n,...c},this.onPostResolved())}onPostResolved(){}then(t,n){return this.currentFinishedPromise.then(t,n)}flatten(){this.options.type="keyframes",this.options.ease="linear"}updateFinishedPromise(){this.currentFinishedPromise=new Promise(t=>{this.resolveFinishedPromise=t})}}const Q=(e,t,n)=>e+(t-e)*n;function qs(e,t,n){return n<0&&(n+=1),n>1&&(n-=1),n<1/6?e+(t-e)*6*n:n<1/2?t:n<2/3?e+(t-e)*(2/3-n)*6:e}function b1({hue:e,saturation:t,lightness:n,alpha:r}){e/=360,t/=100,n/=100;let i=0,o=0,s=0;if(!t)i=o=s=n;else{const l=n<.5?n*(1+t):n+t-n*t,a=2*n-l;i=qs(a,l,e+1/3),o=qs(a,l,e),s=qs(a,l,e-1/3)}return{red:Math.round(i*255),green:Math.round(o*255),blue:Math.round(s*255),alpha:r}}function Ko(e,t){return n=>n>0?t:e}const Js=(e,t,n)=>{const r=e*e,i=n*(t*t-r)+r;return i<0?0:Math.sqrt(i)},Z1=[oa,cn,Hn],q1=e=>Z1.find(t=>t.test(e));function Tf(e){const t=q1(e);if(!t)return!1;let n=t.parse(e);return t===Hn&&(n=b1(n)),n}const kf=(e,t)=>{const n=Tf(e),r=Tf(t);if(!n||!r)return Ko(e,t);const i={...n};return o=>(i.red=Js(n.red,r.red,o),i.green=Js(n.green,r.green,o),i.blue=Js(n.blue,r.blue,o),i.alpha=Q(n.alpha,r.alpha,o),cn.transform(i))},J1=(e,t)=>n=>t(e(n)),Ti=(...e)=>e.reduce(J1),ua=new Set(["none","hidden"]);function ew(e,t){return ua.has(e)?n=>n<=0?e:t:n=>n>=1?t:e}function tw(e,t){return n=>Q(e,t,n)}function Ku(e){return typeof e=="number"?tw:typeof e=="string"?Tu(e)?Ko:we.test(e)?kf:iw:Array.isArray(e)?Km:typeof e=="object"?we.test(e)?kf:nw:Ko}function Km(e,t){const n=[...e],r=n.length,i=e.map((o,s)=>Ku(o)(o,t[s]));return o=>{for(let s=0;s<r;s++)n[s]=i[s](o);return n}}function nw(e,t){const n={...e,...t},r={};for(const i in n)e[i]!==void 0&&t[i]!==void 0&&(r[i]=Ku(e[i])(e[i],t[i]));return i=>{for(const o in r)n[o]=r[o](i);return n}}function rw(e,t){var n;const r=[],i={color:0,var:0,number:0};for(let o=0;o<t.values.length;o++){const s=t.types[o],l=e.indexes[s][i[s]],a=(n=e.values[l])!==null&&n!==void 0?n:0;r[o]=a,i[s]++}return r}const iw=(e,t)=>{const n=Qt.createTransformer(t),r=mi(e),i=mi(t);return r.indexes.var.length===i.indexes.var.length&&r.indexes.color.length===i.indexes.color.length&&r.indexes.number.length>=i.indexes.number.length?ua.has(e)&&!i.values.length||ua.has(t)&&!r.values.length?ew(e,t):Ti(Km(rw(r,i),i.values),n):Ko(e,t)};function Gm(e,t,n){return typeof e=="number"&&typeof t=="number"&&typeof n=="number"?Q(e,t,n):Ku(e)(e,t)}const ow=5;function Qm(e,t,n){const r=Math.max(t-ow,0);return Sm(n-e(r),t-r)}const Z={stiffness:100,damping:10,mass:1,velocity:0,duration:800,bounce:.3,visualDuration:.3,restSpeed:{granular:.01,default:2},restDelta:{granular:.005,default:.5},minDuration:.01,maxDuration:10,minDamping:.05,maxDamping:1},el=.001;function sw({duration:e=Z.duration,bounce:t=Z.bounce,velocity:n=Z.velocity,mass:r=Z.mass}){let i,o,s=1-t;s=Tt(Z.minDamping,Z.maxDamping,s),e=Tt(Z.minDuration,Z.maxDuration,St(e)),s<1?(i=u=>{const c=u*s,d=c*e,f=c-n,m=ca(u,s),v=Math.exp(-d);return el-f/m*v},o=u=>{const d=u*s*e,f=d*n+n,m=Math.pow(s,2)*Math.pow(u,2)*e,v=Math.exp(-d),w=ca(Math.pow(u,2),s);return(-i(u)+el>0?-1:1)*((f-m)*v)/w}):(i=u=>{const c=Math.exp(-u*e),d=(u-n)*e+1;return-el+c*d},o=u=>{const c=Math.exp(-u*e),d=(n-u)*(e*e);return c*d});const l=5/e,a=aw(i,o,l);if(e=wt(e),isNaN(a))return{stiffness:Z.stiffness,damping:Z.damping,duration:e};{const u=Math.pow(a,2)*r;return{stiffness:u,damping:s*2*Math.sqrt(r*u),duration:e}}}const lw=12;function aw(e,t,n){let r=n;for(let i=1;i<lw;i++)r=r-e(r)/t(r);return r}function ca(e,t){return e*Math.sqrt(1-t*t)}const uw=["duration","bounce"],cw=["stiffness","damping","mass"];function Af(e,t){return t.some(n=>e[n]!==void 0)}function fw(e){let t={velocity:Z.velocity,stiffness:Z.stiffness,damping:Z.damping,mass:Z.mass,isResolvedFromDuration:!1,...e};if(!Af(e,cw)&&Af(e,uw))if(e.visualDuration){const n=e.visualDuration,r=2*Math.PI/(n*1.2),i=r*r,o=2*Tt(.05,1,1-(e.bounce||0))*Math.sqrt(i);t={...t,mass:Z.mass,stiffness:i,damping:o}}else{const n=sw(e);t={...t,...n,mass:Z.mass},t.isResolvedFromDuration=!0}return t}function Xm(e=Z.visualDuration,t=Z.bounce){const n=typeof e!="object"?{visualDuration:e,keyframes:[0,1],bounce:t}:e;let{restSpeed:r,restDelta:i}=n;const o=n.keyframes[0],s=n.keyframes[n.keyframes.length-1],l={done:!1,value:o},{stiffness:a,damping:u,mass:c,duration:d,velocity:f,isResolvedFromDuration:m}=fw({...n,velocity:-St(n.velocity||0)}),v=f||0,w=u/(2*Math.sqrt(a*c)),C=s-o,p=St(Math.sqrt(a/c)),h=Math.abs(C)<5;r||(r=h?Z.restSpeed.granular:Z.restSpeed.default),i||(i=h?Z.restDelta.granular:Z.restDelta.default);let g;if(w<1){const x=ca(p,w);g=k=>{const A=Math.exp(-w*p*k);return s-A*((v+w*p*C)/x*Math.sin(x*k)+C*Math.cos(x*k))}}else if(w===1)g=x=>s-Math.exp(-p*x)*(C+(v+p*C)*x);else{const x=p*Math.sqrt(w*w-1);g=k=>{const A=Math.exp(-w*p*k),P=Math.min(x*k,300);return s-A*((v+w*p*C)*Math.sinh(P)+x*C*Math.cosh(P))/x}}const S={calculatedDuration:m&&d||null,next:x=>{const k=g(x);if(m)l.done=x>=d;else{let A=0;w<1&&(A=x===0?wt(v):Qm(g,x,k));const P=Math.abs(A)<=r,I=Math.abs(s-k)<=i;l.done=P&&I}return l.value=l.done?s:k,l},toString:()=>{const x=Math.min(dm(S),na),k=hm(A=>S.next(x*A).value,x,30);return x+"ms "+k}};return S}function Nf({keyframes:e,velocity:t=0,power:n=.8,timeConstant:r=325,bounceDamping:i=10,bounceStiffness:o=500,modifyTarget:s,min:l,max:a,restDelta:u=.5,restSpeed:c}){const d=e[0],f={done:!1,value:d},m=P=>l!==void 0&&P<l||a!==void 0&&P>a,v=P=>l===void 0?a:a===void 0||Math.abs(l-P)<Math.abs(a-P)?l:a;let w=n*t;const C=d+w,p=s===void 0?C:s(C);p!==C&&(w=p-d);const h=P=>-w*Math.exp(-P/r),g=P=>p+h(P),S=P=>{const I=h(P),_=g(P);f.done=Math.abs(I)<=u,f.value=f.done?p:_};let x,k;const A=P=>{m(f.value)&&(x=P,k=Xm({keyframes:[f.value,v(f.value)],velocity:Qm(g,P,f.value),damping:i,stiffness:o,restDelta:u,restSpeed:c}))};return A(0),{calculatedDuration:null,next:P=>{let I=!1;return!k&&x===void 0&&(I=!0,S(P),A(P)),x!==void 0&&P>=x?k.next(P-x):(!I&&S(P),f)}}}const dw=Pi(.42,0,1,1),hw=Pi(0,0,.58,1),Ym=Pi(.42,0,.58,1),pw=e=>Array.isArray(e)&&typeof e[0]!="number",mw={linear:Ie,easeIn:dw,easeInOut:Ym,easeOut:hw,circIn:Bu,circInOut:Mm,circOut:Nm,backIn:zu,backInOut:km,backOut:Tm,anticipate:Am},Mf=e=>{if(Vu(e)){Gp(e.length===4);const[t,n,r,i]=e;return Pi(t,n,r,i)}else if(typeof e=="string")return mw[e];return e};function gw(e,t,n){const r=[],i=n||Gm,o=e.length-1;for(let s=0;s<o;s++){let l=i(e[s],e[s+1]);if(t){const a=Array.isArray(t)?t[s]||Ie:t;l=Ti(a,l)}r.push(l)}return r}function vw(e,t,{clamp:n=!0,ease:r,mixer:i}={}){const o=e.length;if(Gp(o===t.length),o===1)return()=>t[0];if(o===2&&t[0]===t[1])return()=>t[1];const s=e[0]===e[1];e[0]>e[o-1]&&(e=[...e].reverse(),t=[...t].reverse());const l=gw(t,r,i),a=l.length,u=c=>{if(s&&c<e[0])return t[0];let d=0;if(a>1)for(;d<e.length-2&&!(c<e[d+1]);d++);const f=sr(e[d],e[d+1],c);return l[d](f)};return n?c=>u(Tt(e[0],e[o-1],c)):u}function yw(e,t){const n=e[e.length-1];for(let r=1;r<=t;r++){const i=sr(0,t,r);e.push(Q(n,1,i))}}function ww(e){const t=[0];return yw(t,e.length-1),t}function Sw(e,t){return e.map(n=>n*t)}function xw(e,t){return e.map(()=>t||Ym).splice(0,e.length-1)}function Go({duration:e=300,keyframes:t,times:n,ease:r="easeInOut"}){const i=pw(r)?r.map(Mf):Mf(r),o={done:!1,value:t[0]},s=Sw(n&&n.length===t.length?n:ww(t),e),l=vw(s,t,{ease:Array.isArray(i)?i:xw(t,i)});return{calculatedDuration:e,next:a=>(o.value=l(a),o.done=a>=e,o)}}const Ew=e=>{const t=({timestamp:n})=>e(n);return{start:()=>H.update(t,!0),stop:()=>Gt(t),now:()=>he.isProcessing?he.timestamp:dt.now()}},Cw={decay:Nf,inertia:Nf,tween:Go,keyframes:Go,spring:Xm},Pw=e=>e/100;class Gu extends Wm{constructor(t){super(t),this.holdTime=null,this.cancelTime=null,this.currentTime=0,this.playbackSpeed=1,this.pendingPlayState="running",this.startTime=null,this.state="idle",this.stop=()=>{if(this.resolver.cancel(),this.isStopped=!0,this.state==="idle")return;this.teardown();const{onStop:a}=this.options;a&&a()};const{name:n,motionValue:r,element:i,keyframes:o}=this.options,s=(i==null?void 0:i.KeyframeResolver)||Wu,l=(a,u)=>this.onKeyframesResolved(a,u);this.resolver=new s(o,l,n,r,i),this.resolver.scheduleResolve()}flatten(){super.flatten(),this._resolved&&Object.assign(this._resolved,this.initPlayback(this._resolved.keyframes))}initPlayback(t){const{type:n="keyframes",repeat:r=0,repeatDelay:i=0,repeatType:o,velocity:s=0}=this.options,l=_u(n)?n:Cw[n]||Go;let a,u;l!==Go&&typeof t[0]!="number"&&(a=Ti(Pw,Gm(t[0],t[1])),t=[0,100]);const c=l({...this.options,keyframes:t});o==="mirror"&&(u=l({...this.options,keyframes:[...t].reverse(),velocity:-s})),c.calculatedDuration===null&&(c.calculatedDuration=dm(c));const{calculatedDuration:d}=c,f=d+i,m=f*(r+1)-i;return{generator:c,mirroredGenerator:u,mapPercentToKeyframes:a,calculatedDuration:d,resolvedDuration:f,totalDuration:m}}onPostResolved(){const{autoplay:t=!0}=this.options;this.play(),this.pendingPlayState==="paused"||!t?this.pause():this.state=this.pendingPlayState}tick(t,n=!1){const{resolved:r}=this;if(!r){const{keyframes:P}=this.options;return{done:!0,value:P[P.length-1]}}const{finalKeyframe:i,generator:o,mirroredGenerator:s,mapPercentToKeyframes:l,keyframes:a,calculatedDuration:u,totalDuration:c,resolvedDuration:d}=r;if(this.startTime===null)return o.next(0);const{delay:f,repeat:m,repeatType:v,repeatDelay:w,onUpdate:C}=this.options;this.speed>0?this.startTime=Math.min(this.startTime,t):this.speed<0&&(this.startTime=Math.min(t-c/this.speed,this.startTime)),n?this.currentTime=t:this.holdTime!==null?this.currentTime=this.holdTime:this.currentTime=Math.round(t-this.startTime)*this.speed;const p=this.currentTime-f*(this.speed>=0?1:-1),h=this.speed>=0?p<0:p>c;this.currentTime=Math.max(p,0),this.state==="finished"&&this.holdTime===null&&(this.currentTime=c);let g=this.currentTime,S=o;if(m){const P=Math.min(this.currentTime,c)/d;let I=Math.floor(P),_=P%1;!_&&P>=1&&(_=1),_===1&&I--,I=Math.min(I,m+1),!!(I%2)&&(v==="reverse"?(_=1-_,w&&(_-=w/d)):v==="mirror"&&(S=s)),g=Tt(0,1,_)*d}const x=h?{done:!1,value:a[0]}:S.next(g);l&&(x.value=l(x.value));let{done:k}=x;!h&&u!==null&&(k=this.speed>=0?this.currentTime>=c:this.currentTime<=0);const A=this.holdTime===null&&(this.state==="finished"||this.state==="running"&&k);return A&&i!==void 0&&(x.value=Ss(a,this.options,i)),C&&C(x.value),A&&this.finish(),x}get duration(){const{resolved:t}=this;return t?St(t.calculatedDuration):0}get time(){return St(this.currentTime)}set time(t){t=wt(t),this.currentTime=t,this.holdTime!==null||this.speed===0?this.holdTime=t:this.driver&&(this.startTime=this.driver.now()-t/this.speed)}get speed(){return this.playbackSpeed}set speed(t){const n=this.playbackSpeed!==t;this.playbackSpeed=t,n&&(this.time=St(this.currentTime))}play(){if(this.resolver.isScheduled||this.resolver.resume(),!this._resolved){this.pendingPlayState="running";return}if(this.isStopped)return;const{driver:t=Ew,onPlay:n,startTime:r}=this.options;this.driver||(this.driver=t(o=>this.tick(o))),n&&n();const i=this.driver.now();this.holdTime!==null?this.startTime=i-this.holdTime:this.startTime?this.state==="finished"&&(this.startTime=i):this.startTime=r??this.calcStartTime(),this.state==="finished"&&this.updateFinishedPromise(),this.cancelTime=this.startTime,this.holdTime=null,this.state="running",this.driver.start()}pause(){var t;if(!this._resolved){this.pendingPlayState="paused";return}this.state="paused",this.holdTime=(t=this.currentTime)!==null&&t!==void 0?t:0}complete(){this.state!=="running"&&this.play(),this.pendingPlayState=this.state="finished",this.holdTime=null}finish(){this.teardown(),this.state="finished";const{onComplete:t}=this.options;t&&t()}cancel(){this.cancelTime!==null&&this.tick(this.cancelTime),this.teardown(),this.updateFinishedPromise()}teardown(){this.state="idle",this.stopDriver(),this.resolveFinishedPromise(),this.updateFinishedPromise(),this.startTime=this.cancelTime=null,this.resolver.cancel()}stopDriver(){this.driver&&(this.driver.stop(),this.driver=void 0)}sample(t){return this.startTime=0,this.tick(t,!0)}}const Tw=new Set(["opacity","clipPath","filter","transform"]);function kw(e,t,n,{delay:r=0,duration:i=300,repeat:o=0,repeatType:s="loop",ease:l="easeInOut",times:a}={}){const u={[t]:n};a&&(u.offset=a);const c=mm(l,i);return Array.isArray(c)&&(u.easing=c),e.animate(u,{delay:r,duration:i,easing:Array.isArray(c)?"linear":c,fill:"both",iterations:o+1,direction:s==="reverse"?"alternate":"normal"})}const Aw=yu(()=>Object.hasOwnProperty.call(Element.prototype,"animate")),Qo=10,Nw=2e4;function Mw(e){return _u(e.type)||e.type==="spring"||!pm(e.ease)}function Dw(e,t){const n=new Gu({...t,keyframes:e,repeat:0,delay:0,isGenerator:!0});let r={done:!1,value:e[0]};const i=[];let o=0;for(;!r.done&&o<Nw;)r=n.sample(o),i.push(r.value),o+=Qo;return{times:void 0,keyframes:i,duration:o-Qo,ease:"linear"}}const bm={anticipate:Am,backInOut:km,circInOut:Mm};function Lw(e){return e in bm}class Df extends Wm{constructor(t){super(t);const{name:n,motionValue:r,element:i,keyframes:o}=this.options;this.resolver=new Hm(o,(s,l)=>this.onKeyframesResolved(s,l),n,r,i),this.resolver.scheduleResolve()}initPlayback(t,n){let{duration:r=300,times:i,ease:o,type:s,motionValue:l,name:a,startTime:u}=this.options;if(!l.owner||!l.owner.current)return!1;if(typeof o=="string"&&Wo()&&Lw(o)&&(o=bm[o]),Mw(this.options)){const{onComplete:d,onUpdate:f,motionValue:m,element:v,...w}=this.options,C=Dw(t,w);t=C.keyframes,t.length===1&&(t[1]=t[0]),r=C.duration,i=C.times,o=C.ease,s="keyframes"}const c=kw(l.owner.current,a,t,{...this.options,duration:r,times:i,ease:o});return c.startTime=u??this.calcStartTime(),this.pendingTimeline?(pf(c,this.pendingTimeline),this.pendingTimeline=void 0):c.onfinish=()=>{const{onComplete:d}=this.options;l.set(Ss(t,this.options,n)),d&&d(),this.cancel(),this.resolveFinishedPromise()},{animation:c,duration:r,times:i,type:s,ease:o,keyframes:t}}get duration(){const{resolved:t}=this;if(!t)return 0;const{duration:n}=t;return St(n)}get time(){const{resolved:t}=this;if(!t)return 0;const{animation:n}=t;return St(n.currentTime||0)}set time(t){const{resolved:n}=this;if(!n)return;const{animation:r}=n;r.currentTime=wt(t)}get speed(){const{resolved:t}=this;if(!t)return 1;const{animation:n}=t;return n.playbackRate}set speed(t){const{resolved:n}=this;if(!n)return;const{animation:r}=n;r.playbackRate=t}get state(){const{resolved:t}=this;if(!t)return"idle";const{animation:n}=t;return n.playState}get startTime(){const{resolved:t}=this;if(!t)return null;const{animation:n}=t;return n.startTime}attachTimeline(t){if(!this._resolved)this.pendingTimeline=t;else{const{resolved:n}=this;if(!n)return Ie;const{animation:r}=n;pf(r,t)}return Ie}play(){if(this.isStopped)return;const{resolved:t}=this;if(!t)return;const{animation:n}=t;n.playState==="finished"&&this.updateFinishedPromise(),n.play()}pause(){const{resolved:t}=this;if(!t)return;const{animation:n}=t;n.pause()}stop(){if(this.resolver.cancel(),this.isStopped=!0,this.state==="idle")return;this.resolveFinishedPromise(),this.updateFinishedPromise();const{resolved:t}=this;if(!t)return;const{animation:n,keyframes:r,duration:i,type:o,ease:s,times:l}=t;if(n.playState==="idle"||n.playState==="finished")return;if(this.time){const{motionValue:u,onUpdate:c,onComplete:d,element:f,...m}=this.options,v=new Gu({...m,keyframes:r,duration:i,type:o,ease:s,times:l,isGenerator:!0}),w=wt(this.time);u.setWithVelocity(v.sample(w-Qo).value,v.sample(w).value,Qo)}const{onStop:a}=this.options;a&&a(),this.cancel()}complete(){const{resolved:t}=this;t&&t.animation.finish()}cancel(){const{resolved:t}=this;t&&t.animation.cancel()}static supports(t){const{motionValue:n,name:r,repeatDelay:i,repeatType:o,damping:s,type:l}=t;if(!n||!n.owner||!(n.owner.current instanceof HTMLElement))return!1;const{onUpdate:a,transformTemplate:u}=n.owner.getProps();return Aw()&&r&&Tw.has(r)&&!a&&!u&&!i&&o!=="mirror"&&s!==0&&l!=="inertia"}}const Rw={type:"spring",stiffness:500,damping:25,restSpeed:10},_w=e=>({type:"spring",stiffness:550,damping:e===0?2*Math.sqrt(550):30,restSpeed:10}),Vw={type:"keyframes",duration:.8},Ow={type:"keyframes",ease:[.25,.1,.35,1],duration:.3},Iw=(e,{keyframes:t})=>t.length>2?Vw:Pn.has(e)?e.startsWith("scale")?_w(t[1]):Rw:Ow;function Fw({when:e,delay:t,delayChildren:n,staggerChildren:r,staggerDirection:i,repeat:o,repeatType:s,repeatDelay:l,from:a,elapsed:u,...c}){return!!Object.keys(c).length}const Qu=(e,t,n,r={},i,o)=>s=>{const l=Ru(r,e)||{},a=l.delay||r.delay||0;let{elapsed:u=0}=r;u=u-wt(a);let c={keyframes:Array.isArray(n)?n:[null,n],ease:"easeOut",velocity:t.getVelocity(),...l,delay:-u,onUpdate:f=>{t.set(f),l.onUpdate&&l.onUpdate(f)},onComplete:()=>{s(),l.onComplete&&l.onComplete()},name:e,motionValue:t,element:o?void 0:i};Fw(l)||(c={...c,...Iw(e,c)}),c.duration&&(c.duration=wt(c.duration)),c.repeatDelay&&(c.repeatDelay=wt(c.repeatDelay)),c.from!==void 0&&(c.keyframes[0]=c.from);let d=!1;if((c.type===!1||c.duration===0&&!c.repeatDelay)&&(c.duration=0,c.delay===0&&(d=!0)),d&&!o&&t.get()!==void 0){const f=Ss(c.keyframes,l);if(f!==void 0)return H.update(()=>{c.onUpdate(f),c.onComplete()}),new n1([])}return!o&&Df.supports(c)?new Df(c):new Gu(c)};function jw({protectedKeys:e,needsAnimating:t},n){const r=e.hasOwnProperty(n)&&t[n]!==!0;return t[n]=!1,r}function Zm(e,t,{delay:n=0,transitionOverride:r,type:i}={}){var o;let{transition:s=e.getDefaultTransition(),transitionEnd:l,...a}=t;r&&(s=r);const u=[],c=i&&e.animationState&&e.animationState.getState()[i];for(const d in a){const f=e.getValue(d,(o=e.latestValues[d])!==null&&o!==void 0?o:null),m=a[d];if(m===void 0||c&&jw(c,d))continue;const v={delay:n,...Ru(s||{},d)};let w=!1;if(window.MotionHandoffAnimation){const p=xm(e);if(p){const h=window.MotionHandoffAnimation(p,d,H);h!==null&&(v.startTime=h,w=!0)}}ia(e,d),f.start(Qu(d,f,m,e.shouldReduceMotion&&wm.has(d)?{type:!1}:v,e,w));const C=f.animation;C&&u.push(C)}return l&&Promise.all(u).then(()=>{H.update(()=>{l&&g1(e,l)})}),u}function fa(e,t,n={}){var r;const i=ws(e,t,n.type==="exit"?(r=e.presenceContext)===null||r===void 0?void 0:r.custom:void 0);let{transition:o=e.getDefaultTransition()||{}}=i||{};n.transitionOverride&&(o=n.transitionOverride);const s=i?()=>Promise.all(Zm(e,i,n)):()=>Promise.resolve(),l=e.variantChildren&&e.variantChildren.size?(u=0)=>{const{delayChildren:c=0,staggerChildren:d,staggerDirection:f}=o;return zw(e,t,c+u,d,f,n)}:()=>Promise.resolve(),{when:a}=o;if(a){const[u,c]=a==="beforeChildren"?[s,l]:[l,s];return u().then(()=>c())}else return Promise.all([s(),l(n.delay)])}function zw(e,t,n=0,r=0,i=1,o){const s=[],l=(e.variantChildren.size-1)*r,a=i===1?(u=0)=>u*r:(u=0)=>l-u*r;return Array.from(e.variantChildren).sort(Bw).forEach((u,c)=>{u.notify("AnimationStart",t),s.push(fa(u,t,{...o,delay:n+a(c)}).then(()=>u.notify("AnimationComplete",t)))}),Promise.all(s)}function Bw(e,t){return e.sortNodePosition(t)}function Uw(e,t,n={}){e.notify("AnimationStart",t);let r;if(Array.isArray(t)){const i=t.map(o=>fa(e,o,n));r=Promise.all(i)}else if(typeof t=="string")r=fa(e,t,n);else{const i=typeof t=="function"?ws(e,t,n.custom):t;r=Promise.all(Zm(e,i,n))}return r.then(()=>{e.notify("AnimationComplete",t)})}const $w=Su.length;function qm(e){if(!e)return;if(!e.isControllingVariants){const n=e.parent?qm(e.parent)||{}:{};return e.props.initial!==void 0&&(n.initial=e.props.initial),n}const t={};for(let n=0;n<$w;n++){const r=Su[n],i=e.props[r];(di(i)||i===!1)&&(t[r]=i)}return t}const Hw=[...wu].reverse(),Ww=wu.length;function Kw(e){return t=>Promise.all(t.map(({animation:n,options:r})=>Uw(e,n,r)))}function Gw(e){let t=Kw(e),n=Lf(),r=!0;const i=a=>(u,c)=>{var d;const f=ws(e,c,a==="exit"?(d=e.presenceContext)===null||d===void 0?void 0:d.custom:void 0);if(f){const{transition:m,transitionEnd:v,...w}=f;u={...u,...w,...v}}return u};function o(a){t=a(e)}function s(a){const{props:u}=e,c=qm(e.parent)||{},d=[],f=new Set;let m={},v=1/0;for(let C=0;C<Ww;C++){const p=Hw[C],h=n[p],g=u[p]!==void 0?u[p]:c[p],S=di(g),x=p===a?h.isActive:null;x===!1&&(v=C);let k=g===c[p]&&g!==u[p]&&S;if(k&&r&&e.manuallyAnimateOnMount&&(k=!1),h.protectedKeys={...m},!h.isActive&&x===null||!g&&!h.prevProp||vs(g)||typeof g=="boolean")continue;const A=Qw(h.prevProp,g);let P=A||p===a&&h.isActive&&!k&&S||C>v&&S,I=!1;const _=Array.isArray(g)?g:[g];let ae=_.reduce(i(p),{});x===!1&&(ae={});const{prevResolvedValues:At={}}=h,qt={...At,...ae},vr=oe=>{P=!0,f.has(oe)&&(I=!0,f.delete(oe)),h.needsAnimating[oe]=!0;const N=e.getValue(oe);N&&(N.liveStyle=!1)};for(const oe in qt){const N=ae[oe],V=At[oe];if(m.hasOwnProperty(oe))continue;let O=!1;ta(N)&&ta(V)?O=!fm(N,V):O=N!==V,O?N!=null?vr(oe):f.add(oe):N!==void 0&&f.has(oe)?vr(oe):h.protectedKeys[oe]=!0}h.prevProp=g,h.prevResolvedValues=ae,h.isActive&&(m={...m,...ae}),r&&e.blockInitialAnimation&&(P=!1),P&&(!(k&&A)||I)&&d.push(..._.map(oe=>({animation:oe,options:{type:p}})))}if(f.size){const C={};f.forEach(p=>{const h=e.getBaseTarget(p),g=e.getValue(p);g&&(g.liveStyle=!0),C[p]=h??null}),d.push({animation:C})}let w=!!d.length;return r&&(u.initial===!1||u.initial===u.animate)&&!e.manuallyAnimateOnMount&&(w=!1),r=!1,w?t(d):Promise.resolve()}function l(a,u){var c;if(n[a].isActive===u)return Promise.resolve();(c=e.variantChildren)===null||c===void 0||c.forEach(f=>{var m;return(m=f.animationState)===null||m===void 0?void 0:m.setActive(a,u)}),n[a].isActive=u;const d=s(a);for(const f in n)n[f].protectedKeys={};return d}return{animateChanges:s,setActive:l,setAnimateFunction:o,getState:()=>n,reset:()=>{n=Lf(),r=!0}}}function Qw(e,t){return typeof t=="string"?t!==e:Array.isArray(t)?!fm(t,e):!1}function tn(e=!1){return{isActive:e,protectedKeys:{},needsAnimating:{},prevResolvedValues:{}}}function Lf(){return{animate:tn(!0),whileInView:tn(),whileHover:tn(),whileTap:tn(),whileDrag:tn(),whileFocus:tn(),exit:tn()}}class Zt{constructor(t){this.isMounted=!1,this.node=t}update(){}}class Xw extends Zt{constructor(t){super(t),t.animationState||(t.animationState=Gw(t))}updateAnimationControlsSubscription(){const{animate:t}=this.node.getProps();vs(t)&&(this.unmountControls=t.subscribe(this.node))}mount(){this.updateAnimationControlsSubscription()}update(){const{animate:t}=this.node.getProps(),{animate:n}=this.node.prevProps||{};t!==n&&this.updateAnimationControlsSubscription()}unmount(){var t;this.node.animationState.reset(),(t=this.unmountControls)===null||t===void 0||t.call(this)}}let Yw=0;class bw extends Zt{constructor(){super(...arguments),this.id=Yw++}update(){if(!this.node.presenceContext)return;const{isPresent:t,onExitComplete:n}=this.node.presenceContext,{isPresent:r}=this.node.prevPresenceContext||{};if(!this.node.animationState||t===r)return;const i=this.node.animationState.setActive("exit",!t);n&&!t&&i.then(()=>n(this.id))}mount(){const{register:t}=this.node.presenceContext||{};t&&(this.unmount=t(this.id))}unmount(){}}const Zw={animation:{Feature:Xw},exit:{Feature:bw}};function gi(e,t,n,r={passive:!0}){return e.addEventListener(t,n,r),()=>e.removeEventListener(t,n)}function ki(e){return{point:{x:e.pageX,y:e.pageY}}}const qw=e=>t=>Ou(t)&&e(t,ki(t));function Wr(e,t,n,r){return gi(e,t,qw(n),r)}const Rf=(e,t)=>Math.abs(e-t);function Jw(e,t){const n=Rf(e.x,t.x),r=Rf(e.y,t.y);return Math.sqrt(n**2+r**2)}class Jm{constructor(t,n,{transformPagePoint:r,contextWindow:i,dragSnapToOrigin:o=!1}={}){if(this.startEvent=null,this.lastMoveEvent=null,this.lastMoveEventInfo=null,this.handlers={},this.contextWindow=window,this.updatePoint=()=>{if(!(this.lastMoveEvent&&this.lastMoveEventInfo))return;const d=nl(this.lastMoveEventInfo,this.history),f=this.startEvent!==null,m=Jw(d.offset,{x:0,y:0})>=3;if(!f&&!m)return;const{point:v}=d,{timestamp:w}=he;this.history.push({...v,timestamp:w});const{onStart:C,onMove:p}=this.handlers;f||(C&&C(this.lastMoveEvent,d),this.startEvent=this.lastMoveEvent),p&&p(this.lastMoveEvent,d)},this.handlePointerMove=(d,f)=>{this.lastMoveEvent=d,this.lastMoveEventInfo=tl(f,this.transformPagePoint),H.update(this.updatePoint,!0)},this.handlePointerUp=(d,f)=>{this.end();const{onEnd:m,onSessionEnd:v,resumeAnimation:w}=this.handlers;if(this.dragSnapToOrigin&&w&&w(),!(this.lastMoveEvent&&this.lastMoveEventInfo))return;const C=nl(d.type==="pointercancel"?this.lastMoveEventInfo:tl(f,this.transformPagePoint),this.history);this.startEvent&&m&&m(d,C),v&&v(d,C)},!Ou(t))return;this.dragSnapToOrigin=o,this.handlers=n,this.transformPagePoint=r,this.contextWindow=i||window;const s=ki(t),l=tl(s,this.transformPagePoint),{point:a}=l,{timestamp:u}=he;this.history=[{...a,timestamp:u}];const{onSessionStart:c}=n;c&&c(t,nl(l,this.history)),this.removeListeners=Ti(Wr(this.contextWindow,"pointermove",this.handlePointerMove),Wr(this.contextWindow,"pointerup",this.handlePointerUp),Wr(this.contextWindow,"pointercancel",this.handlePointerUp))}updateHandlers(t){this.handlers=t}end(){this.removeListeners&&this.removeListeners(),Gt(this.updatePoint)}}function tl(e,t){return t?{point:t(e.point)}:e}function _f(e,t){return{x:e.x-t.x,y:e.y-t.y}}function nl({point:e},t){return{point:e,delta:_f(e,eg(t)),offset:_f(e,eS(t)),velocity:tS(t,.1)}}function eS(e){return e[0]}function eg(e){return e[e.length-1]}function tS(e,t){if(e.length<2)return{x:0,y:0};let n=e.length-1,r=null;const i=eg(e);for(;n>=0&&(r=e[n],!(i.timestamp-r.timestamp>wt(t)));)n--;if(!r)return{x:0,y:0};const o=St(i.timestamp-r.timestamp);if(o===0)return{x:0,y:0};const s={x:(i.x-r.x)/o,y:(i.y-r.y)/o};return s.x===1/0&&(s.x=0),s.y===1/0&&(s.y=0),s}const tg=1e-4,nS=1-tg,rS=1+tg,ng=.01,iS=0-ng,oS=0+ng;function je(e){return e.max-e.min}function sS(e,t,n){return Math.abs(e-t)<=n}function Vf(e,t,n,r=.5){e.origin=r,e.originPoint=Q(t.min,t.max,e.origin),e.scale=je(n)/je(t),e.translate=Q(n.min,n.max,e.origin)-e.originPoint,(e.scale>=nS&&e.scale<=rS||isNaN(e.scale))&&(e.scale=1),(e.translate>=iS&&e.translate<=oS||isNaN(e.translate))&&(e.translate=0)}function Kr(e,t,n,r){Vf(e.x,t.x,n.x,r?r.originX:void 0),Vf(e.y,t.y,n.y,r?r.originY:void 0)}function Of(e,t,n){e.min=n.min+t.min,e.max=e.min+je(t)}function lS(e,t,n){Of(e.x,t.x,n.x),Of(e.y,t.y,n.y)}function If(e,t,n){e.min=t.min-n.min,e.max=e.min+je(t)}function Gr(e,t,n){If(e.x,t.x,n.x),If(e.y,t.y,n.y)}function aS(e,{min:t,max:n},r){return t!==void 0&&e<t?e=r?Q(t,e,r.min):Math.max(e,t):n!==void 0&&e>n&&(e=r?Q(n,e,r.max):Math.min(e,n)),e}function Ff(e,t,n){return{min:t!==void 0?e.min+t:void 0,max:n!==void 0?e.max+n-(e.max-e.min):void 0}}function uS(e,{top:t,left:n,bottom:r,right:i}){return{x:Ff(e.x,n,i),y:Ff(e.y,t,r)}}function jf(e,t){let n=t.min-e.min,r=t.max-e.max;return t.max-t.min<e.max-e.min&&([n,r]=[r,n]),{min:n,max:r}}function cS(e,t){return{x:jf(e.x,t.x),y:jf(e.y,t.y)}}function fS(e,t){let n=.5;const r=je(e),i=je(t);return i>r?n=sr(t.min,t.max-r,e.min):r>i&&(n=sr(e.min,e.max-i,t.min)),Tt(0,1,n)}function dS(e,t){const n={};return t.min!==void 0&&(n.min=t.min-e.min),t.max!==void 0&&(n.max=t.max-e.min),n}const da=.35;function hS(e=da){return e===!1?e=0:e===!0&&(e=da),{x:zf(e,"left","right"),y:zf(e,"top","bottom")}}function zf(e,t,n){return{min:Bf(e,t),max:Bf(e,n)}}function Bf(e,t){return typeof e=="number"?e:e[t]||0}const Uf=()=>({translate:0,scale:1,origin:0,originPoint:0}),Wn=()=>({x:Uf(),y:Uf()}),$f=()=>({min:0,max:0}),ne=()=>({x:$f(),y:$f()});function $e(e){return[e("x"),e("y")]}function rg({top:e,left:t,right:n,bottom:r}){return{x:{min:t,max:n},y:{min:e,max:r}}}function pS({x:e,y:t}){return{top:t.min,right:e.max,bottom:t.max,left:e.min}}function mS(e,t){if(!t)return e;const n=t({x:e.left,y:e.top}),r=t({x:e.right,y:e.bottom});return{top:n.y,left:n.x,bottom:r.y,right:r.x}}function rl(e){return e===void 0||e===1}function ha({scale:e,scaleX:t,scaleY:n}){return!rl(e)||!rl(t)||!rl(n)}function on(e){return ha(e)||ig(e)||e.z||e.rotate||e.rotateX||e.rotateY||e.skewX||e.skewY}function ig(e){return Hf(e.x)||Hf(e.y)}function Hf(e){return e&&e!=="0%"}function Xo(e,t,n){const r=e-n,i=t*r;return n+i}function Wf(e,t,n,r,i){return i!==void 0&&(e=Xo(e,i,r)),Xo(e,n,r)+t}function pa(e,t=0,n=1,r,i){e.min=Wf(e.min,t,n,r,i),e.max=Wf(e.max,t,n,r,i)}function og(e,{x:t,y:n}){pa(e.x,t.translate,t.scale,t.originPoint),pa(e.y,n.translate,n.scale,n.originPoint)}const Kf=.999999999999,Gf=1.0000000000001;function gS(e,t,n,r=!1){const i=n.length;if(!i)return;t.x=t.y=1;let o,s;for(let l=0;l<i;l++){o=n[l],s=o.projectionDelta;const{visualElement:a}=o.options;a&&a.props.style&&a.props.style.display==="contents"||(r&&o.options.layoutScroll&&o.scroll&&o!==o.root&&Gn(e,{x:-o.scroll.offset.x,y:-o.scroll.offset.y}),s&&(t.x*=s.x.scale,t.y*=s.y.scale,og(e,s)),r&&on(o.latestValues)&&Gn(e,o.latestValues))}t.x<Gf&&t.x>Kf&&(t.x=1),t.y<Gf&&t.y>Kf&&(t.y=1)}function Kn(e,t){e.min=e.min+t,e.max=e.max+t}function Qf(e,t,n,r,i=.5){const o=Q(e.min,e.max,i);pa(e,t,n,o,r)}function Gn(e,t){Qf(e.x,t.x,t.scaleX,t.scale,t.originX),Qf(e.y,t.y,t.scaleY,t.scale,t.originY)}function sg(e,t){return rg(mS(e.getBoundingClientRect(),t))}function vS(e,t,n){const r=sg(e,n),{scroll:i}=t;return i&&(Kn(r.x,i.offset.x),Kn(r.y,i.offset.y)),r}const lg=({current:e})=>e?e.ownerDocument.defaultView:null,yS=new WeakMap;class wS{constructor(t){this.openDragLock=null,this.isDragging=!1,this.currentDirection=null,this.originPoint={x:0,y:0},this.constraints=!1,this.hasMutatedConstraints=!1,this.elastic=ne(),this.visualElement=t}start(t,{snapToCursor:n=!1}={}){const{presenceContext:r}=this.visualElement;if(r&&r.isPresent===!1)return;const i=c=>{const{dragSnapToOrigin:d}=this.getProps();d?this.pauseAnimation():this.stopAnimation(),n&&this.snapToCursor(ki(c).point)},o=(c,d)=>{const{drag:f,dragPropagation:m,onDragStart:v}=this.getProps();if(f&&!m&&(this.openDragLock&&this.openDragLock(),this.openDragLock=f1(f),!this.openDragLock))return;this.isDragging=!0,this.currentDirection=null,this.resolveConstraints(),this.visualElement.projection&&(this.visualElement.projection.isAnimationBlocked=!0,this.visualElement.projection.target=void 0),$e(C=>{let p=this.getAxisMotionValue(C).get()||0;if(ft.test(p)){const{projection:h}=this.visualElement;if(h&&h.layout){const g=h.layout.layoutBox[C];g&&(p=je(g)*(parseFloat(p)/100))}}this.originPoint[C]=p}),v&&H.postRender(()=>v(c,d)),ia(this.visualElement,"transform");const{animationState:w}=this.visualElement;w&&w.setActive("whileDrag",!0)},s=(c,d)=>{const{dragPropagation:f,dragDirectionLock:m,onDirectionLock:v,onDrag:w}=this.getProps();if(!f&&!this.openDragLock)return;const{offset:C}=d;if(m&&this.currentDirection===null){this.currentDirection=SS(C),this.currentDirection!==null&&v&&v(this.currentDirection);return}this.updateAxis("x",d.point,C),this.updateAxis("y",d.point,C),this.visualElement.render(),w&&w(c,d)},l=(c,d)=>this.stop(c,d),a=()=>$e(c=>{var d;return this.getAnimationState(c)==="paused"&&((d=this.getAxisMotionValue(c).animation)===null||d===void 0?void 0:d.play())}),{dragSnapToOrigin:u}=this.getProps();this.panSession=new Jm(t,{onSessionStart:i,onStart:o,onMove:s,onSessionEnd:l,resumeAnimation:a},{transformPagePoint:this.visualElement.getTransformPagePoint(),dragSnapToOrigin:u,contextWindow:lg(this.visualElement)})}stop(t,n){const r=this.isDragging;if(this.cancel(),!r)return;const{velocity:i}=n;this.startAnimation(i);const{onDragEnd:o}=this.getProps();o&&H.postRender(()=>o(t,n))}cancel(){this.isDragging=!1;const{projection:t,animationState:n}=this.visualElement;t&&(t.isAnimationBlocked=!1),this.panSession&&this.panSession.end(),this.panSession=void 0;const{dragPropagation:r}=this.getProps();!r&&this.openDragLock&&(this.openDragLock(),this.openDragLock=null),n&&n.setActive("whileDrag",!1)}updateAxis(t,n,r){const{drag:i}=this.getProps();if(!r||!qi(t,i,this.currentDirection))return;const o=this.getAxisMotionValue(t);let s=this.originPoint[t]+r[t];this.constraints&&this.constraints[t]&&(s=aS(s,this.constraints[t],this.elastic[t])),o.set(s)}resolveConstraints(){var t;const{dragConstraints:n,dragElastic:r}=this.getProps(),i=this.visualElement.projection&&!this.visualElement.projection.layout?this.visualElement.projection.measure(!1):(t=this.visualElement.projection)===null||t===void 0?void 0:t.layout,o=this.constraints;n&&$n(n)?this.constraints||(this.constraints=this.resolveRefConstraints()):n&&i?this.constraints=uS(i.layoutBox,n):this.constraints=!1,this.elastic=hS(r),o!==this.constraints&&i&&this.constraints&&!this.hasMutatedConstraints&&$e(s=>{this.constraints!==!1&&this.getAxisMotionValue(s)&&(this.constraints[s]=dS(i.layoutBox[s],this.constraints[s]))})}resolveRefConstraints(){const{dragConstraints:t,onMeasureDragConstraints:n}=this.getProps();if(!t||!$n(t))return!1;const r=t.current,{projection:i}=this.visualElement;if(!i||!i.layout)return!1;const o=vS(r,i.root,this.visualElement.getTransformPagePoint());let s=cS(i.layout.layoutBox,o);if(n){const l=n(pS(s));this.hasMutatedConstraints=!!l,l&&(s=rg(l))}return s}startAnimation(t){const{drag:n,dragMomentum:r,dragElastic:i,dragTransition:o,dragSnapToOrigin:s,onDragTransitionEnd:l}=this.getProps(),a=this.constraints||{},u=$e(c=>{if(!qi(c,n,this.currentDirection))return;let d=a&&a[c]||{};s&&(d={min:0,max:0});const f=i?200:1e6,m=i?40:1e7,v={type:"inertia",velocity:r?t[c]:0,bounceStiffness:f,bounceDamping:m,timeConstant:750,restDelta:1,restSpeed:10,...o,...d};return this.startAxisValueAnimation(c,v)});return Promise.all(u).then(l)}startAxisValueAnimation(t,n){const r=this.getAxisMotionValue(t);return ia(this.visualElement,t),r.start(Qu(t,r,0,n,this.visualElement,!1))}stopAnimation(){$e(t=>this.getAxisMotionValue(t).stop())}pauseAnimation(){$e(t=>{var n;return(n=this.getAxisMotionValue(t).animation)===null||n===void 0?void 0:n.pause()})}getAnimationState(t){var n;return(n=this.getAxisMotionValue(t).animation)===null||n===void 0?void 0:n.state}getAxisMotionValue(t){const n=`_drag${t.toUpperCase()}`,r=this.visualElement.getProps(),i=r[n];return i||this.visualElement.getValue(t,(r.initial?r.initial[t]:void 0)||0)}snapToCursor(t){$e(n=>{const{drag:r}=this.getProps();if(!qi(n,r,this.currentDirection))return;const{projection:i}=this.visualElement,o=this.getAxisMotionValue(n);if(i&&i.layout){const{min:s,max:l}=i.layout.layoutBox[n];o.set(t[n]-Q(s,l,.5))}})}scalePositionWithinConstraints(){if(!this.visualElement.current)return;const{drag:t,dragConstraints:n}=this.getProps(),{projection:r}=this.visualElement;if(!$n(n)||!r||!this.constraints)return;this.stopAnimation();const i={x:0,y:0};$e(s=>{const l=this.getAxisMotionValue(s);if(l&&this.constraints!==!1){const a=l.get();i[s]=fS({min:a,max:a},this.constraints[s])}});const{transformTemplate:o}=this.visualElement.getProps();this.visualElement.current.style.transform=o?o({},""):"none",r.root&&r.root.updateScroll(),r.updateLayout(),this.resolveConstraints(),$e(s=>{if(!qi(s,t,null))return;const l=this.getAxisMotionValue(s),{min:a,max:u}=this.constraints[s];l.set(Q(a,u,i[s]))})}addListeners(){if(!this.visualElement.current)return;yS.set(this.visualElement,this);const t=this.visualElement.current,n=Wr(t,"pointerdown",a=>{const{drag:u,dragListener:c=!0}=this.getProps();u&&c&&this.start(a)}),r=()=>{const{dragConstraints:a}=this.getProps();$n(a)&&a.current&&(this.constraints=this.resolveRefConstraints())},{projection:i}=this.visualElement,o=i.addEventListener("measure",r);i&&!i.layout&&(i.root&&i.root.updateScroll(),i.updateLayout()),H.read(r);const s=gi(window,"resize",()=>this.scalePositionWithinConstraints()),l=i.addEventListener("didUpdate",({delta:a,hasLayoutChanged:u})=>{this.isDragging&&u&&($e(c=>{const d=this.getAxisMotionValue(c);d&&(this.originPoint[c]+=a[c].translate,d.set(d.get()+a[c].translate))}),this.visualElement.render())});return()=>{s(),n(),o(),l&&l()}}getProps(){const t=this.visualElement.getProps(),{drag:n=!1,dragDirectionLock:r=!1,dragPropagation:i=!1,dragConstraints:o=!1,dragElastic:s=da,dragMomentum:l=!0}=t;return{...t,drag:n,dragDirectionLock:r,dragPropagation:i,dragConstraints:o,dragElastic:s,dragMomentum:l}}}function qi(e,t,n){return(t===!0||t===e)&&(n===null||n===e)}function SS(e,t=10){let n=null;return Math.abs(e.y)>t?n="y":Math.abs(e.x)>t&&(n="x"),n}class xS extends Zt{constructor(t){super(t),this.removeGroupControls=Ie,this.removeListeners=Ie,this.controls=new wS(t)}mount(){const{dragControls:t}=this.node.getProps();t&&(this.removeGroupControls=t.subscribe(this.controls)),this.removeListeners=this.controls.addListeners()||Ie}unmount(){this.removeGroupControls(),this.removeListeners()}}const Xf=e=>(t,n)=>{e&&H.postRender(()=>e(t,n))};class ES extends Zt{constructor(){super(...arguments),this.removePointerDownListener=Ie}onPointerDown(t){this.session=new Jm(t,this.createPanHandlers(),{transformPagePoint:this.node.getTransformPagePoint(),contextWindow:lg(this.node)})}createPanHandlers(){const{onPanSessionStart:t,onPanStart:n,onPan:r,onPanEnd:i}=this.node.getProps();return{onSessionStart:Xf(t),onStart:Xf(n),onMove:r,onEnd:(o,s)=>{delete this.session,i&&H.postRender(()=>i(o,s))}}}mount(){this.removePointerDownListener=Wr(this.node.current,"pointerdown",t=>this.onPointerDown(t))}update(){this.session&&this.session.updateHandlers(this.createPanHandlers())}unmount(){this.removePointerDownListener(),this.session&&this.session.end()}}const mo={hasAnimatedSinceResize:!0,hasEverUpdated:!1};function Yf(e,t){return t.max===t.min?0:e/(t.max-t.min)*100}const kr={correct:(e,t)=>{if(!t.target)return e;if(typeof e=="string")if(R.test(e))e=parseFloat(e);else return e;const n=Yf(e,t.target.x),r=Yf(e,t.target.y);return`${n}% ${r}%`}},CS={correct:(e,{treeScale:t,projectionDelta:n})=>{const r=e,i=Qt.parse(e);if(i.length>5)return r;const o=Qt.createTransformer(e),s=typeof i[0]!="number"?1:0,l=n.x.scale*t.x,a=n.y.scale*t.y;i[0+s]/=l,i[1+s]/=a;const u=Q(l,a,.5);return typeof i[2+s]=="number"&&(i[2+s]/=u),typeof i[3+s]=="number"&&(i[3+s]/=u),o(i)}};class PS extends E.Component{componentDidMount(){const{visualElement:t,layoutGroup:n,switchLayoutGroup:r,layoutId:i}=this.props,{projection:o}=t;Wy(TS),o&&(n.group&&n.group.add(o),r&&r.register&&i&&r.register(o),o.root.didUpdate(),o.addEventListener("animationComplete",()=>{this.safeToRemove()}),o.setOptions({...o.options,onExitComplete:()=>this.safeToRemove()})),mo.hasEverUpdated=!0}getSnapshotBeforeUpdate(t){const{layoutDependency:n,visualElement:r,drag:i,isPresent:o}=this.props,s=r.projection;return s&&(s.isPresent=o,i||t.layoutDependency!==n||n===void 0?s.willUpdate():this.safeToRemove(),t.isPresent!==o&&(o?s.promote():s.relegate()||H.postRender(()=>{const l=s.getStack();(!l||!l.members.length)&&this.safeToRemove()}))),null}componentDidUpdate(){const{projection:t}=this.props.visualElement;t&&(t.root.didUpdate(),Eu.postRender(()=>{!t.currentAnimation&&t.isLead()&&this.safeToRemove()}))}componentWillUnmount(){const{visualElement:t,layoutGroup:n,switchLayoutGroup:r}=this.props,{projection:i}=t;i&&(i.scheduleCheckAfterUnmount(),n&&n.group&&n.group.remove(i),r&&r.deregister&&r.deregister(i))}safeToRemove(){const{safeToRemove:t}=this.props;t&&t()}render(){return null}}function ag(e){const[t,n]=Wp(),r=E.useContext(hu);return y(PS,{...e,layoutGroup:r,switchLayoutGroup:E.useContext(qp),isPresent:t,safeToRemove:n})}const TS={borderRadius:{...kr,applyTo:["borderTopLeftRadius","borderTopRightRadius","borderBottomLeftRadius","borderBottomRightRadius"]},borderTopLeftRadius:kr,borderTopRightRadius:kr,borderBottomLeftRadius:kr,borderBottomRightRadius:kr,boxShadow:CS};function kS(e,t,n){const r=xe(e)?e:pi(e);return r.start(Qu("",r,t,n)),r.animation}function AS(e){return e instanceof SVGElement&&e.tagName!=="svg"}const NS=(e,t)=>e.depth-t.depth;class MS{constructor(){this.children=[],this.isDirty=!1}add(t){Iu(this.children,t),this.isDirty=!0}remove(t){Fu(this.children,t),this.isDirty=!0}forEach(t){this.isDirty&&this.children.sort(NS),this.isDirty=!1,this.children.forEach(t)}}function DS(e,t){const n=dt.now(),r=({timestamp:i})=>{const o=i-n;o>=t&&(Gt(r),e(o-t))};return H.read(r,!0),()=>Gt(r)}const ug=["TopLeft","TopRight","BottomLeft","BottomRight"],LS=ug.length,bf=e=>typeof e=="string"?parseFloat(e):e,Zf=e=>typeof e=="number"||R.test(e);function RS(e,t,n,r,i,o){i?(e.opacity=Q(0,n.opacity!==void 0?n.opacity:1,_S(r)),e.opacityExit=Q(t.opacity!==void 0?t.opacity:1,0,VS(r))):o&&(e.opacity=Q(t.opacity!==void 0?t.opacity:1,n.opacity!==void 0?n.opacity:1,r));for(let s=0;s<LS;s++){const l=`border${ug[s]}Radius`;let a=qf(t,l),u=qf(n,l);if(a===void 0&&u===void 0)continue;a||(a=0),u||(u=0),a===0||u===0||Zf(a)===Zf(u)?(e[l]=Math.max(Q(bf(a),bf(u),r),0),(ft.test(u)||ft.test(a))&&(e[l]+="%")):e[l]=u}(t.rotate||n.rotate)&&(e.rotate=Q(t.rotate||0,n.rotate||0,r))}function qf(e,t){return e[t]!==void 0?e[t]:e.borderRadius}const _S=cg(0,.5,Nm),VS=cg(.5,.95,Ie);function cg(e,t,n){return r=>r<e?0:r>t?1:n(sr(e,t,r))}function Jf(e,t){e.min=t.min,e.max=t.max}function Ue(e,t){Jf(e.x,t.x),Jf(e.y,t.y)}function ed(e,t){e.translate=t.translate,e.scale=t.scale,e.originPoint=t.originPoint,e.origin=t.origin}function td(e,t,n,r,i){return e-=t,e=Xo(e,1/n,r),i!==void 0&&(e=Xo(e,1/i,r)),e}function OS(e,t=0,n=1,r=.5,i,o=e,s=e){if(ft.test(t)&&(t=parseFloat(t),t=Q(s.min,s.max,t/100)-s.min),typeof t!="number")return;let l=Q(o.min,o.max,r);e===o&&(l-=t),e.min=td(e.min,t,n,l,i),e.max=td(e.max,t,n,l,i)}function nd(e,t,[n,r,i],o,s){OS(e,t[n],t[r],t[i],t.scale,o,s)}const IS=["x","scaleX","originX"],FS=["y","scaleY","originY"];function rd(e,t,n,r){nd(e.x,t,IS,n?n.x:void 0,r?r.x:void 0),nd(e.y,t,FS,n?n.y:void 0,r?r.y:void 0)}function id(e){return e.translate===0&&e.scale===1}function fg(e){return id(e.x)&&id(e.y)}function od(e,t){return e.min===t.min&&e.max===t.max}function jS(e,t){return od(e.x,t.x)&&od(e.y,t.y)}function sd(e,t){return Math.round(e.min)===Math.round(t.min)&&Math.round(e.max)===Math.round(t.max)}function dg(e,t){return sd(e.x,t.x)&&sd(e.y,t.y)}function ld(e){return je(e.x)/je(e.y)}function ad(e,t){return e.translate===t.translate&&e.scale===t.scale&&e.originPoint===t.originPoint}class zS{constructor(){this.members=[]}add(t){Iu(this.members,t),t.scheduleRender()}remove(t){if(Fu(this.members,t),t===this.prevLead&&(this.prevLead=void 0),t===this.lead){const n=this.members[this.members.length-1];n&&this.promote(n)}}relegate(t){const n=this.members.findIndex(i=>t===i);if(n===0)return!1;let r;for(let i=n;i>=0;i--){const o=this.members[i];if(o.isPresent!==!1){r=o;break}}return r?(this.promote(r),!0):!1}promote(t,n){const r=this.lead;if(t!==r&&(this.prevLead=r,this.lead=t,t.show(),r)){r.instance&&r.scheduleRender(),t.scheduleRender(),t.resumeFrom=r,n&&(t.resumeFrom.preserveOpacity=!0),r.snapshot&&(t.snapshot=r.snapshot,t.snapshot.latestValues=r.animationValues||r.latestValues),t.root&&t.root.isUpdating&&(t.isLayoutDirty=!0);const{crossfade:i}=t.options;i===!1&&r.hide()}}exitAnimationComplete(){this.members.forEach(t=>{const{options:n,resumingFrom:r}=t;n.onExitComplete&&n.onExitComplete(),r&&r.options.onExitComplete&&r.options.onExitComplete()})}scheduleRender(){this.members.forEach(t=>{t.instance&&t.scheduleRender(!1)})}removeLeadSnapshot(){this.lead&&this.lead.snapshot&&(this.lead.snapshot=void 0)}}function BS(e,t,n){let r="";const i=e.x.translate/t.x,o=e.y.translate/t.y,s=(n==null?void 0:n.z)||0;if((i||o||s)&&(r=`translate3d(${i}px, ${o}px, ${s}px) `),(t.x!==1||t.y!==1)&&(r+=`scale(${1/t.x}, ${1/t.y}) `),n){const{transformPerspective:u,rotate:c,rotateX:d,rotateY:f,skewX:m,skewY:v}=n;u&&(r=`perspective(${u}px) ${r}`),c&&(r+=`rotate(${c}deg) `),d&&(r+=`rotateX(${d}deg) `),f&&(r+=`rotateY(${f}deg) `),m&&(r+=`skewX(${m}deg) `),v&&(r+=`skewY(${v}deg) `)}const l=e.x.scale*t.x,a=e.y.scale*t.y;return(l!==1||a!==1)&&(r+=`scale(${l}, ${a})`),r||"none"}const sn={type:"projectionFrame",totalNodes:0,resolvedTargetDeltas:0,recalculatedProjection:0},_r=typeof window<"u"&&window.MotionDebug!==void 0,il=["","X","Y","Z"],US={visibility:"hidden"},ud=1e3;let $S=0;function ol(e,t,n,r){const{latestValues:i}=t;i[e]&&(n[e]=i[e],t.setStaticValue(e,0),r&&(r[e]=0))}function hg(e){if(e.hasCheckedOptimisedAppear=!0,e.root===e)return;const{visualElement:t}=e.options;if(!t)return;const n=xm(t);if(window.MotionHasOptimisedAnimation(n,"transform")){const{layout:i,layoutId:o}=e.options;window.MotionCancelOptimisedAnimation(n,"transform",H,!(i||o))}const{parent:r}=e;r&&!r.hasCheckedOptimisedAppear&&hg(r)}function pg({attachResizeListener:e,defaultParent:t,measureScroll:n,checkIsScrollRoot:r,resetTransform:i}){return class{constructor(s={},l=t==null?void 0:t()){this.id=$S++,this.animationId=0,this.children=new Set,this.options={},this.isTreeAnimating=!1,this.isAnimationBlocked=!1,this.isLayoutDirty=!1,this.isProjectionDirty=!1,this.isSharedProjectionDirty=!1,this.isTransformDirty=!1,this.updateManuallyBlocked=!1,this.updateBlockedByResize=!1,this.isUpdating=!1,this.isSVG=!1,this.needsReset=!1,this.shouldResetTransform=!1,this.hasCheckedOptimisedAppear=!1,this.treeScale={x:1,y:1},this.eventHandlers=new Map,this.hasTreeAnimated=!1,this.updateScheduled=!1,this.scheduleUpdate=()=>this.update(),this.projectionUpdateScheduled=!1,this.checkUpdateFailed=()=>{this.isUpdating&&(this.isUpdating=!1,this.clearAllSnapshots())},this.updateProjection=()=>{this.projectionUpdateScheduled=!1,_r&&(sn.totalNodes=sn.resolvedTargetDeltas=sn.recalculatedProjection=0),this.nodes.forEach(KS),this.nodes.forEach(bS),this.nodes.forEach(ZS),this.nodes.forEach(GS),_r&&window.MotionDebug.record(sn)},this.resolvedRelativeTargetAt=0,this.hasProjected=!1,this.isVisible=!0,this.animationProgress=0,this.sharedNodes=new Map,this.latestValues=s,this.root=l?l.root||l:this,this.path=l?[...l.path,l]:[],this.parent=l,this.depth=l?l.depth+1:0;for(let a=0;a<this.path.length;a++)this.path[a].shouldResetTransform=!0;this.root===this&&(this.nodes=new MS)}addEventListener(s,l){return this.eventHandlers.has(s)||this.eventHandlers.set(s,new ju),this.eventHandlers.get(s).add(l)}notifyListeners(s,...l){const a=this.eventHandlers.get(s);a&&a.notify(...l)}hasListeners(s){return this.eventHandlers.has(s)}mount(s,l=this.root.hasTreeAnimated){if(this.instance)return;this.isSVG=AS(s),this.instance=s;const{layoutId:a,layout:u,visualElement:c}=this.options;if(c&&!c.current&&c.mount(s),this.root.nodes.add(this),this.parent&&this.parent.children.add(this),l&&(u||a)&&(this.isLayoutDirty=!0),e){let d;const f=()=>this.root.updateBlockedByResize=!1;e(s,()=>{this.root.updateBlockedByResize=!0,d&&d(),d=DS(f,250),mo.hasAnimatedSinceResize&&(mo.hasAnimatedSinceResize=!1,this.nodes.forEach(fd))})}a&&this.root.registerSharedNode(a,this),this.options.animate!==!1&&c&&(a||u)&&this.addEventListener("didUpdate",({delta:d,hasLayoutChanged:f,hasRelativeTargetChanged:m,layout:v})=>{if(this.isTreeAnimationBlocked()){this.target=void 0,this.relativeTarget=void 0;return}const w=this.options.transition||c.getDefaultTransition()||n2,{onLayoutAnimationStart:C,onLayoutAnimationComplete:p}=c.getProps(),h=!this.targetLayout||!dg(this.targetLayout,v)||m,g=!f&&m;if(this.options.layoutRoot||this.resumeFrom&&this.resumeFrom.instance||g||f&&(h||!this.currentAnimation)){this.resumeFrom&&(this.resumingFrom=this.resumeFrom,this.resumingFrom.resumingFrom=void 0),this.setAnimationOrigin(d,g);const S={...Ru(w,"layout"),onPlay:C,onComplete:p};(c.shouldReduceMotion||this.options.layoutRoot)&&(S.delay=0,S.type=!1),this.startAnimation(S)}else f||fd(this),this.isLead()&&this.options.onExitComplete&&this.options.onExitComplete();this.targetLayout=v})}unmount(){this.options.layoutId&&this.willUpdate(),this.root.nodes.remove(this);const s=this.getStack();s&&s.remove(this),this.parent&&this.parent.children.delete(this),this.instance=void 0,Gt(this.updateProjection)}blockUpdate(){this.updateManuallyBlocked=!0}unblockUpdate(){this.updateManuallyBlocked=!1}isUpdateBlocked(){return this.updateManuallyBlocked||this.updateBlockedByResize}isTreeAnimationBlocked(){return this.isAnimationBlocked||this.parent&&this.parent.isTreeAnimationBlocked()||!1}startUpdate(){this.isUpdateBlocked()||(this.isUpdating=!0,this.nodes&&this.nodes.forEach(qS),this.animationId++)}getTransformTemplate(){const{visualElement:s}=this.options;return s&&s.getProps().transformTemplate}willUpdate(s=!0){if(this.root.hasTreeAnimated=!0,this.root.isUpdateBlocked()){this.options.onExitComplete&&this.options.onExitComplete();return}if(window.MotionCancelOptimisedAnimation&&!this.hasCheckedOptimisedAppear&&hg(this),!this.root.isUpdating&&this.root.startUpdate(),this.isLayoutDirty)return;this.isLayoutDirty=!0;for(let c=0;c<this.path.length;c++){const d=this.path[c];d.shouldResetTransform=!0,d.updateScroll("snapshot"),d.options.layoutRoot&&d.willUpdate(!1)}const{layoutId:l,layout:a}=this.options;if(l===void 0&&!a)return;const u=this.getTransformTemplate();this.prevTransformTemplateValue=u?u(this.latestValues,""):void 0,this.updateSnapshot(),s&&this.notifyListeners("willUpdate")}update(){if(this.updateScheduled=!1,this.isUpdateBlocked()){this.unblockUpdate(),this.clearAllSnapshots(),this.nodes.forEach(cd);return}this.isUpdating||this.nodes.forEach(XS),this.isUpdating=!1,this.nodes.forEach(YS),this.nodes.forEach(HS),this.nodes.forEach(WS),this.clearAllSnapshots();const l=dt.now();he.delta=Tt(0,1e3/60,l-he.timestamp),he.timestamp=l,he.isProcessing=!0,Ys.update.process(he),Ys.preRender.process(he),Ys.render.process(he),he.isProcessing=!1}didUpdate(){this.updateScheduled||(this.updateScheduled=!0,Eu.read(this.scheduleUpdate))}clearAllSnapshots(){this.nodes.forEach(QS),this.sharedNodes.forEach(JS)}scheduleUpdateProjection(){this.projectionUpdateScheduled||(this.projectionUpdateScheduled=!0,H.preRender(this.updateProjection,!1,!0))}scheduleCheckAfterUnmount(){H.postRender(()=>{this.isLayoutDirty?this.root.didUpdate():this.root.checkUpdateFailed()})}updateSnapshot(){this.snapshot||!this.instance||(this.snapshot=this.measure())}updateLayout(){if(!this.instance||(this.updateScroll(),!(this.options.alwaysMeasureLayout&&this.isLead())&&!this.isLayoutDirty))return;if(this.resumeFrom&&!this.resumeFrom.instance)for(let a=0;a<this.path.length;a++)this.path[a].updateScroll();const s=this.layout;this.layout=this.measure(!1),this.layoutCorrected=ne(),this.isLayoutDirty=!1,this.projectionDelta=void 0,this.notifyListeners("measure",this.layout.layoutBox);const{visualElement:l}=this.options;l&&l.notify("LayoutMeasure",this.layout.layoutBox,s?s.layoutBox:void 0)}updateScroll(s="measure"){let l=!!(this.options.layoutScroll&&this.instance);if(this.scroll&&this.scroll.animationId===this.root.animationId&&this.scroll.phase===s&&(l=!1),l){const a=r(this.instance);this.scroll={animationId:this.root.animationId,phase:s,isRoot:a,offset:n(this.instance),wasRoot:this.scroll?this.scroll.isRoot:a}}}resetTransform(){if(!i)return;const s=this.isLayoutDirty||this.shouldResetTransform||this.options.alwaysMeasureLayout,l=this.projectionDelta&&!fg(this.projectionDelta),a=this.getTransformTemplate(),u=a?a(this.latestValues,""):void 0,c=u!==this.prevTransformTemplateValue;s&&(l||on(this.latestValues)||c)&&(i(this.instance,u),this.shouldResetTransform=!1,this.scheduleRender())}measure(s=!0){const l=this.measurePageBox();let a=this.removeElementScroll(l);return s&&(a=this.removeTransform(a)),r2(a),{animationId:this.root.animationId,measuredBox:l,layoutBox:a,latestValues:{},source:this.id}}measurePageBox(){var s;const{visualElement:l}=this.options;if(!l)return ne();const a=l.measureViewportBox();if(!(((s=this.scroll)===null||s===void 0?void 0:s.wasRoot)||this.path.some(i2))){const{scroll:c}=this.root;c&&(Kn(a.x,c.offset.x),Kn(a.y,c.offset.y))}return a}removeElementScroll(s){var l;const a=ne();if(Ue(a,s),!((l=this.scroll)===null||l===void 0)&&l.wasRoot)return a;for(let u=0;u<this.path.length;u++){const c=this.path[u],{scroll:d,options:f}=c;c!==this.root&&d&&f.layoutScroll&&(d.wasRoot&&Ue(a,s),Kn(a.x,d.offset.x),Kn(a.y,d.offset.y))}return a}applyTransform(s,l=!1){const a=ne();Ue(a,s);for(let u=0;u<this.path.length;u++){const c=this.path[u];!l&&c.options.layoutScroll&&c.scroll&&c!==c.root&&Gn(a,{x:-c.scroll.offset.x,y:-c.scroll.offset.y}),on(c.latestValues)&&Gn(a,c.latestValues)}return on(this.latestValues)&&Gn(a,this.latestValues),a}removeTransform(s){const l=ne();Ue(l,s);for(let a=0;a<this.path.length;a++){const u=this.path[a];if(!u.instance||!on(u.latestValues))continue;ha(u.latestValues)&&u.updateSnapshot();const c=ne(),d=u.measurePageBox();Ue(c,d),rd(l,u.latestValues,u.snapshot?u.snapshot.layoutBox:void 0,c)}return on(this.latestValues)&&rd(l,this.latestValues),l}setTargetDelta(s){this.targetDelta=s,this.root.scheduleUpdateProjection(),this.isProjectionDirty=!0}setOptions(s){this.options={...this.options,...s,crossfade:s.crossfade!==void 0?s.crossfade:!0}}clearMeasurements(){this.scroll=void 0,this.layout=void 0,this.snapshot=void 0,this.prevTransformTemplateValue=void 0,this.targetDelta=void 0,this.target=void 0,this.isLayoutDirty=!1}forceRelativeParentToResolveTarget(){this.relativeParent&&this.relativeParent.resolvedRelativeTargetAt!==he.timestamp&&this.relativeParent.resolveTargetDelta(!0)}resolveTargetDelta(s=!1){var l;const a=this.getLead();this.isProjectionDirty||(this.isProjectionDirty=a.isProjectionDirty),this.isTransformDirty||(this.isTransformDirty=a.isTransformDirty),this.isSharedProjectionDirty||(this.isSharedProjectionDirty=a.isSharedProjectionDirty);const u=!!this.resumingFrom||this!==a;if(!(s||u&&this.isSharedProjectionDirty||this.isProjectionDirty||!((l=this.parent)===null||l===void 0)&&l.isProjectionDirty||this.attemptToResolveRelativeTarget||this.root.updateBlockedByResize))return;const{layout:d,layoutId:f}=this.options;if(!(!this.layout||!(d||f))){if(this.resolvedRelativeTargetAt=he.timestamp,!this.targetDelta&&!this.relativeTarget){const m=this.getClosestProjectingParent();m&&m.layout&&this.animationProgress!==1?(this.relativeParent=m,this.forceRelativeParentToResolveTarget(),this.relativeTarget=ne(),this.relativeTargetOrigin=ne(),Gr(this.relativeTargetOrigin,this.layout.layoutBox,m.layout.layoutBox),Ue(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}if(!(!this.relativeTarget&&!this.targetDelta)){if(this.target||(this.target=ne(),this.targetWithTransforms=ne()),this.relativeTarget&&this.relativeTargetOrigin&&this.relativeParent&&this.relativeParent.target?(this.forceRelativeParentToResolveTarget(),lS(this.target,this.relativeTarget,this.relativeParent.target)):this.targetDelta?(this.resumingFrom?this.target=this.applyTransform(this.layout.layoutBox):Ue(this.target,this.layout.layoutBox),og(this.target,this.targetDelta)):Ue(this.target,this.layout.layoutBox),this.attemptToResolveRelativeTarget){this.attemptToResolveRelativeTarget=!1;const m=this.getClosestProjectingParent();m&&!!m.resumingFrom==!!this.resumingFrom&&!m.options.layoutScroll&&m.target&&this.animationProgress!==1?(this.relativeParent=m,this.forceRelativeParentToResolveTarget(),this.relativeTarget=ne(),this.relativeTargetOrigin=ne(),Gr(this.relativeTargetOrigin,this.target,m.target),Ue(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}_r&&sn.resolvedTargetDeltas++}}}getClosestProjectingParent(){if(!(!this.parent||ha(this.parent.latestValues)||ig(this.parent.latestValues)))return this.parent.isProjecting()?this.parent:this.parent.getClosestProjectingParent()}isProjecting(){return!!((this.relativeTarget||this.targetDelta||this.options.layoutRoot)&&this.layout)}calcProjection(){var s;const l=this.getLead(),a=!!this.resumingFrom||this!==l;let u=!0;if((this.isProjectionDirty||!((s=this.parent)===null||s===void 0)&&s.isProjectionDirty)&&(u=!1),a&&(this.isSharedProjectionDirty||this.isTransformDirty)&&(u=!1),this.resolvedRelativeTargetAt===he.timestamp&&(u=!1),u)return;const{layout:c,layoutId:d}=this.options;if(this.isTreeAnimating=!!(this.parent&&this.parent.isTreeAnimating||this.currentAnimation||this.pendingAnimation),this.isTreeAnimating||(this.targetDelta=this.relativeTarget=void 0),!this.layout||!(c||d))return;Ue(this.layoutCorrected,this.layout.layoutBox);const f=this.treeScale.x,m=this.treeScale.y;gS(this.layoutCorrected,this.treeScale,this.path,a),l.layout&&!l.target&&(this.treeScale.x!==1||this.treeScale.y!==1)&&(l.target=l.layout.layoutBox,l.targetWithTransforms=ne());const{target:v}=l;if(!v){this.prevProjectionDelta&&(this.createProjectionDeltas(),this.scheduleRender());return}!this.projectionDelta||!this.prevProjectionDelta?this.createProjectionDeltas():(ed(this.prevProjectionDelta.x,this.projectionDelta.x),ed(this.prevProjectionDelta.y,this.projectionDelta.y)),Kr(this.projectionDelta,this.layoutCorrected,v,this.latestValues),(this.treeScale.x!==f||this.treeScale.y!==m||!ad(this.projectionDelta.x,this.prevProjectionDelta.x)||!ad(this.projectionDelta.y,this.prevProjectionDelta.y))&&(this.hasProjected=!0,this.scheduleRender(),this.notifyListeners("projectionUpdate",v)),_r&&sn.recalculatedProjection++}hide(){this.isVisible=!1}show(){this.isVisible=!0}scheduleRender(s=!0){var l;if((l=this.options.visualElement)===null||l===void 0||l.scheduleRender(),s){const a=this.getStack();a&&a.scheduleRender()}this.resumingFrom&&!this.resumingFrom.instance&&(this.resumingFrom=void 0)}createProjectionDeltas(){this.prevProjectionDelta=Wn(),this.projectionDelta=Wn(),this.projectionDeltaWithTransform=Wn()}setAnimationOrigin(s,l=!1){const a=this.snapshot,u=a?a.latestValues:{},c={...this.latestValues},d=Wn();(!this.relativeParent||!this.relativeParent.options.layoutRoot)&&(this.relativeTarget=this.relativeTargetOrigin=void 0),this.attemptToResolveRelativeTarget=!l;const f=ne(),m=a?a.source:void 0,v=this.layout?this.layout.source:void 0,w=m!==v,C=this.getStack(),p=!C||C.members.length<=1,h=!!(w&&!p&&this.options.crossfade===!0&&!this.path.some(t2));this.animationProgress=0;let g;this.mixTargetDelta=S=>{const x=S/1e3;dd(d.x,s.x,x),dd(d.y,s.y,x),this.setTargetDelta(d),this.relativeTarget&&this.relativeTargetOrigin&&this.layout&&this.relativeParent&&this.relativeParent.layout&&(Gr(f,this.layout.layoutBox,this.relativeParent.layout.layoutBox),e2(this.relativeTarget,this.relativeTargetOrigin,f,x),g&&jS(this.relativeTarget,g)&&(this.isProjectionDirty=!1),g||(g=ne()),Ue(g,this.relativeTarget)),w&&(this.animationValues=c,RS(c,u,this.latestValues,x,h,p)),this.root.scheduleUpdateProjection(),this.scheduleRender(),this.animationProgress=x},this.mixTargetDelta(this.options.layoutRoot?1e3:0)}startAnimation(s){this.notifyListeners("animationStart"),this.currentAnimation&&this.currentAnimation.stop(),this.resumingFrom&&this.resumingFrom.currentAnimation&&this.resumingFrom.currentAnimation.stop(),this.pendingAnimation&&(Gt(this.pendingAnimation),this.pendingAnimation=void 0),this.pendingAnimation=H.update(()=>{mo.hasAnimatedSinceResize=!0,this.currentAnimation=kS(0,ud,{...s,onUpdate:l=>{this.mixTargetDelta(l),s.onUpdate&&s.onUpdate(l)},onComplete:()=>{s.onComplete&&s.onComplete(),this.completeAnimation()}}),this.resumingFrom&&(this.resumingFrom.currentAnimation=this.currentAnimation),this.pendingAnimation=void 0})}completeAnimation(){this.resumingFrom&&(this.resumingFrom.currentAnimation=void 0,this.resumingFrom.preserveOpacity=void 0);const s=this.getStack();s&&s.exitAnimationComplete(),this.resumingFrom=this.currentAnimation=this.animationValues=void 0,this.notifyListeners("animationComplete")}finishAnimation(){this.currentAnimation&&(this.mixTargetDelta&&this.mixTargetDelta(ud),this.currentAnimation.stop()),this.completeAnimation()}applyTransformsToTarget(){const s=this.getLead();let{targetWithTransforms:l,target:a,layout:u,latestValues:c}=s;if(!(!l||!a||!u)){if(this!==s&&this.layout&&u&&mg(this.options.animationType,this.layout.layoutBox,u.layoutBox)){a=this.target||ne();const d=je(this.layout.layoutBox.x);a.x.min=s.target.x.min,a.x.max=a.x.min+d;const f=je(this.layout.layoutBox.y);a.y.min=s.target.y.min,a.y.max=a.y.min+f}Ue(l,a),Gn(l,c),Kr(this.projectionDeltaWithTransform,this.layoutCorrected,l,c)}}registerSharedNode(s,l){this.sharedNodes.has(s)||this.sharedNodes.set(s,new zS),this.sharedNodes.get(s).add(l);const u=l.options.initialPromotionConfig;l.promote({transition:u?u.transition:void 0,preserveFollowOpacity:u&&u.shouldPreserveFollowOpacity?u.shouldPreserveFollowOpacity(l):void 0})}isLead(){const s=this.getStack();return s?s.lead===this:!0}getLead(){var s;const{layoutId:l}=this.options;return l?((s=this.getStack())===null||s===void 0?void 0:s.lead)||this:this}getPrevLead(){var s;const{layoutId:l}=this.options;return l?(s=this.getStack())===null||s===void 0?void 0:s.prevLead:void 0}getStack(){const{layoutId:s}=this.options;if(s)return this.root.sharedNodes.get(s)}promote({needsReset:s,transition:l,preserveFollowOpacity:a}={}){const u=this.getStack();u&&u.promote(this,a),s&&(this.projectionDelta=void 0,this.needsReset=!0),l&&this.setOptions({transition:l})}relegate(){const s=this.getStack();return s?s.relegate(this):!1}resetSkewAndRotation(){const{visualElement:s}=this.options;if(!s)return;let l=!1;const{latestValues:a}=s;if((a.z||a.rotate||a.rotateX||a.rotateY||a.rotateZ||a.skewX||a.skewY)&&(l=!0),!l)return;const u={};a.z&&ol("z",s,u,this.animationValues);for(let c=0;c<il.length;c++)ol(`rotate${il[c]}`,s,u,this.animationValues),ol(`skew${il[c]}`,s,u,this.animationValues);s.render();for(const c in u)s.setStaticValue(c,u[c]),this.animationValues&&(this.animationValues[c]=u[c]);s.scheduleRender()}getProjectionStyles(s){var l,a;if(!this.instance||this.isSVG)return;if(!this.isVisible)return US;const u={visibility:""},c=this.getTransformTemplate();if(this.needsReset)return this.needsReset=!1,u.opacity="",u.pointerEvents=ho(s==null?void 0:s.pointerEvents)||"",u.transform=c?c(this.latestValues,""):"none",u;const d=this.getLead();if(!this.projectionDelta||!this.layout||!d.target){const w={};return this.options.layoutId&&(w.opacity=this.latestValues.opacity!==void 0?this.latestValues.opacity:1,w.pointerEvents=ho(s==null?void 0:s.pointerEvents)||""),this.hasProjected&&!on(this.latestValues)&&(w.transform=c?c({},""):"none",this.hasProjected=!1),w}const f=d.animationValues||d.latestValues;this.applyTransformsToTarget(),u.transform=BS(this.projectionDeltaWithTransform,this.treeScale,f),c&&(u.transform=c(f,u.transform));const{x:m,y:v}=this.projectionDelta;u.transformOrigin=`${m.origin*100}% ${v.origin*100}% 0`,d.animationValues?u.opacity=d===this?(a=(l=f.opacity)!==null&&l!==void 0?l:this.latestValues.opacity)!==null&&a!==void 0?a:1:this.preserveOpacity?this.latestValues.opacity:f.opacityExit:u.opacity=d===this?f.opacity!==void 0?f.opacity:"":f.opacityExit!==void 0?f.opacityExit:0;for(const w in Ho){if(f[w]===void 0)continue;const{correct:C,applyTo:p}=Ho[w],h=u.transform==="none"?f[w]:C(f[w],d);if(p){const g=p.length;for(let S=0;S<g;S++)u[p[S]]=h}else u[w]=h}return this.options.layoutId&&(u.pointerEvents=d===this?ho(s==null?void 0:s.pointerEvents)||"":"none"),u}clearSnapshot(){this.resumeFrom=this.snapshot=void 0}resetTree(){this.root.nodes.forEach(s=>{var l;return(l=s.currentAnimation)===null||l===void 0?void 0:l.stop()}),this.root.nodes.forEach(cd),this.root.sharedNodes.clear()}}}function HS(e){e.updateLayout()}function WS(e){var t;const n=((t=e.resumeFrom)===null||t===void 0?void 0:t.snapshot)||e.snapshot;if(e.isLead()&&e.layout&&n&&e.hasListeners("didUpdate")){const{layoutBox:r,measuredBox:i}=e.layout,{animationType:o}=e.options,s=n.source!==e.layout.source;o==="size"?$e(d=>{const f=s?n.measuredBox[d]:n.layoutBox[d],m=je(f);f.min=r[d].min,f.max=f.min+m}):mg(o,n.layoutBox,r)&&$e(d=>{const f=s?n.measuredBox[d]:n.layoutBox[d],m=je(r[d]);f.max=f.min+m,e.relativeTarget&&!e.currentAnimation&&(e.isProjectionDirty=!0,e.relativeTarget[d].max=e.relativeTarget[d].min+m)});const l=Wn();Kr(l,r,n.layoutBox);const a=Wn();s?Kr(a,e.applyTransform(i,!0),n.measuredBox):Kr(a,r,n.layoutBox);const u=!fg(l);let c=!1;if(!e.resumeFrom){const d=e.getClosestProjectingParent();if(d&&!d.resumeFrom){const{snapshot:f,layout:m}=d;if(f&&m){const v=ne();Gr(v,n.layoutBox,f.layoutBox);const w=ne();Gr(w,r,m.layoutBox),dg(v,w)||(c=!0),d.options.layoutRoot&&(e.relativeTarget=w,e.relativeTargetOrigin=v,e.relativeParent=d)}}}e.notifyListeners("didUpdate",{layout:r,snapshot:n,delta:a,layoutDelta:l,hasLayoutChanged:u,hasRelativeTargetChanged:c})}else if(e.isLead()){const{onExitComplete:r}=e.options;r&&r()}e.options.transition=void 0}function KS(e){_r&&sn.totalNodes++,e.parent&&(e.isProjecting()||(e.isProjectionDirty=e.parent.isProjectionDirty),e.isSharedProjectionDirty||(e.isSharedProjectionDirty=!!(e.isProjectionDirty||e.parent.isProjectionDirty||e.parent.isSharedProjectionDirty)),e.isTransformDirty||(e.isTransformDirty=e.parent.isTransformDirty))}function GS(e){e.isProjectionDirty=e.isSharedProjectionDirty=e.isTransformDirty=!1}function QS(e){e.clearSnapshot()}function cd(e){e.clearMeasurements()}function XS(e){e.isLayoutDirty=!1}function YS(e){const{visualElement:t}=e.options;t&&t.getProps().onBeforeLayoutMeasure&&t.notify("BeforeLayoutMeasure"),e.resetTransform()}function fd(e){e.finishAnimation(),e.targetDelta=e.relativeTarget=e.target=void 0,e.isProjectionDirty=!0}function bS(e){e.resolveTargetDelta()}function ZS(e){e.calcProjection()}function qS(e){e.resetSkewAndRotation()}function JS(e){e.removeLeadSnapshot()}function dd(e,t,n){e.translate=Q(t.translate,0,n),e.scale=Q(t.scale,1,n),e.origin=t.origin,e.originPoint=t.originPoint}function hd(e,t,n,r){e.min=Q(t.min,n.min,r),e.max=Q(t.max,n.max,r)}function e2(e,t,n,r){hd(e.x,t.x,n.x,r),hd(e.y,t.y,n.y,r)}function t2(e){return e.animationValues&&e.animationValues.opacityExit!==void 0}const n2={duration:.45,ease:[.4,0,.1,1]},pd=e=>typeof navigator<"u"&&navigator.userAgent&&navigator.userAgent.toLowerCase().includes(e),md=pd("applewebkit/")&&!pd("chrome/")?Math.round:Ie;function gd(e){e.min=md(e.min),e.max=md(e.max)}function r2(e){gd(e.x),gd(e.y)}function mg(e,t,n){return e==="position"||e==="preserve-aspect"&&!sS(ld(t),ld(n),.2)}function i2(e){var t;return e!==e.root&&((t=e.scroll)===null||t===void 0?void 0:t.wasRoot)}const o2=pg({attachResizeListener:(e,t)=>gi(e,"resize",t),measureScroll:()=>({x:document.documentElement.scrollLeft||document.body.scrollLeft,y:document.documentElement.scrollTop||document.body.scrollTop}),checkIsScrollRoot:()=>!0}),sl={current:void 0},gg=pg({measureScroll:e=>({x:e.scrollLeft,y:e.scrollTop}),defaultParent:()=>{if(!sl.current){const e=new o2({});e.mount(window),e.setOptions({layoutScroll:!0}),sl.current=e}return sl.current},resetTransform:(e,t)=>{e.style.transform=t!==void 0?t:"none"},checkIsScrollRoot:e=>window.getComputedStyle(e).position==="fixed"}),s2={pan:{Feature:ES},drag:{Feature:xS,ProjectionNode:gg,MeasureLayout:ag}};function vd(e,t,n){const{props:r}=e;e.animationState&&r.whileHover&&e.animationState.setActive("whileHover",n==="Start");const i="onHover"+n,o=r[i];o&&H.postRender(()=>o(t,ki(t)))}class l2 extends Zt{mount(){const{current:t}=this.node;t&&(this.unmount=s1(t,n=>(vd(this.node,n,"Start"),r=>vd(this.node,r,"End"))))}unmount(){}}class a2 extends Zt{constructor(){super(...arguments),this.isActive=!1}onFocus(){let t=!1;try{t=this.node.current.matches(":focus-visible")}catch{t=!0}!t||!this.node.animationState||(this.node.animationState.setActive("whileFocus",!0),this.isActive=!0)}onBlur(){!this.isActive||!this.node.animationState||(this.node.animationState.setActive("whileFocus",!1),this.isActive=!1)}mount(){this.unmount=Ti(gi(this.node.current,"focus",()=>this.onFocus()),gi(this.node.current,"blur",()=>this.onBlur()))}unmount(){}}function yd(e,t,n){const{props:r}=e;e.animationState&&r.whileTap&&e.animationState.setActive("whileTap",n==="Start");const i="onTap"+(n==="End"?"":n),o=r[i];o&&H.postRender(()=>o(t,ki(t)))}class u2 extends Zt{mount(){const{current:t}=this.node;t&&(this.unmount=c1(t,n=>(yd(this.node,n,"Start"),(r,{success:i})=>yd(this.node,r,i?"End":"Cancel")),{useGlobalTarget:this.node.props.globalTapTarget}))}unmount(){}}const ma=new WeakMap,ll=new WeakMap,c2=e=>{const t=ma.get(e.target);t&&t(e)},f2=e=>{e.forEach(c2)};function d2({root:e,...t}){const n=e||document;ll.has(n)||ll.set(n,{});const r=ll.get(n),i=JSON.stringify(t);return r[i]||(r[i]=new IntersectionObserver(f2,{root:e,...t})),r[i]}function h2(e,t,n){const r=d2(t);return ma.set(e,n),r.observe(e),()=>{ma.delete(e),r.unobserve(e)}}const p2={some:0,all:1};class m2 extends Zt{constructor(){super(...arguments),this.hasEnteredView=!1,this.isInView=!1}startObserver(){this.unmount();const{viewport:t={}}=this.node.getProps(),{root:n,margin:r,amount:i="some",once:o}=t,s={root:n?n.current:void 0,rootMargin:r,threshold:typeof i=="number"?i:p2[i]},l=a=>{const{isIntersecting:u}=a;if(this.isInView===u||(this.isInView=u,o&&!u&&this.hasEnteredView))return;u&&(this.hasEnteredView=!0),this.node.animationState&&this.node.animationState.setActive("whileInView",u);const{onViewportEnter:c,onViewportLeave:d}=this.node.getProps(),f=u?c:d;f&&f(a)};return h2(this.node.current,s,l)}mount(){this.startObserver()}update(){if(typeof IntersectionObserver>"u")return;const{props:t,prevProps:n}=this.node;["amount","margin","root"].some(g2(t,n))&&this.startObserver()}unmount(){}}function g2({viewport:e={}},{viewport:t={}}={}){return n=>e[n]!==t[n]}const v2={inView:{Feature:m2},tap:{Feature:u2},focus:{Feature:a2},hover:{Feature:l2}},y2={layout:{ProjectionNode:gg,MeasureLayout:ag}},ga={current:null},vg={current:!1};function w2(){if(vg.current=!0,!!gu)if(window.matchMedia){const e=window.matchMedia("(prefers-reduced-motion)"),t=()=>ga.current=e.matches;e.addListener(t),t()}else ga.current=!1}const S2=[...$m,we,Qt],x2=e=>S2.find(Um(e)),wd=new WeakMap;function E2(e,t,n){for(const r in t){const i=t[r],o=n[r];if(xe(i))e.addValue(r,i);else if(xe(o))e.addValue(r,pi(i,{owner:e}));else if(o!==i)if(e.hasValue(r)){const s=e.getValue(r);s.liveStyle===!0?s.jump(i):s.hasAnimated||s.set(i)}else{const s=e.getStaticValue(r);e.addValue(r,pi(s!==void 0?s:i,{owner:e}))}}for(const r in n)t[r]===void 0&&e.removeValue(r);return t}const Sd=["AnimationStart","AnimationComplete","Update","BeforeLayoutMeasure","LayoutMeasure","LayoutAnimationStart","LayoutAnimationComplete"];class C2{scrapeMotionValuesFromProps(t,n,r){return{}}constructor({parent:t,props:n,presenceContext:r,reducedMotionConfig:i,blockInitialAnimation:o,visualState:s},l={}){this.current=null,this.children=new Set,this.isVariantNode=!1,this.isControllingVariants=!1,this.shouldReduceMotion=null,this.values=new Map,this.KeyframeResolver=Wu,this.features={},this.valueSubscriptions=new Map,this.prevMotionValues={},this.events={},this.propEventSubscriptions={},this.notifyUpdate=()=>this.notify("Update",this.latestValues),this.render=()=>{this.current&&(this.triggerBuild(),this.renderInstance(this.current,this.renderState,this.props.style,this.projection))},this.renderScheduledAt=0,this.scheduleRender=()=>{const m=dt.now();this.renderScheduledAt<m&&(this.renderScheduledAt=m,H.render(this.render,!1,!0))};const{latestValues:a,renderState:u,onUpdate:c}=s;this.onUpdate=c,this.latestValues=a,this.baseTarget={...a},this.initialValues=n.initial?{...a}:{},this.renderState=u,this.parent=t,this.props=n,this.presenceContext=r,this.depth=t?t.depth+1:0,this.reducedMotionConfig=i,this.options=l,this.blockInitialAnimation=!!o,this.isControllingVariants=ys(n),this.isVariantNode=bp(n),this.isVariantNode&&(this.variantChildren=new Set),this.manuallyAnimateOnMount=!!(t&&t.current);const{willChange:d,...f}=this.scrapeMotionValuesFromProps(n,{},this);for(const m in f){const v=f[m];a[m]!==void 0&&xe(v)&&v.set(a[m],!1)}}mount(t){this.current=t,wd.set(t,this),this.projection&&!this.projection.instance&&this.projection.mount(t),this.parent&&this.isVariantNode&&!this.isControllingVariants&&(this.removeFromVariantTree=this.parent.addVariantChild(this)),this.values.forEach((n,r)=>this.bindToMotionValue(r,n)),vg.current||w2(),this.shouldReduceMotion=this.reducedMotionConfig==="never"?!1:this.reducedMotionConfig==="always"?!0:ga.current,this.parent&&this.parent.children.add(this),this.update(this.props,this.presenceContext)}unmount(){wd.delete(this.current),this.projection&&this.projection.unmount(),Gt(this.notifyUpdate),Gt(this.render),this.valueSubscriptions.forEach(t=>t()),this.valueSubscriptions.clear(),this.removeFromVariantTree&&this.removeFromVariantTree(),this.parent&&this.parent.children.delete(this);for(const t in this.events)this.events[t].clear();for(const t in this.features){const n=this.features[t];n&&(n.unmount(),n.isMounted=!1)}this.current=null}bindToMotionValue(t,n){this.valueSubscriptions.has(t)&&this.valueSubscriptions.get(t)();const r=Pn.has(t),i=n.on("change",l=>{this.latestValues[t]=l,this.props.onUpdate&&H.preRender(this.notifyUpdate),r&&this.projection&&(this.projection.isTransformDirty=!0)}),o=n.on("renderRequest",this.scheduleRender);let s;window.MotionCheckAppearSync&&(s=window.MotionCheckAppearSync(this,t,n)),this.valueSubscriptions.set(t,()=>{i(),o(),s&&s(),n.owner&&n.stop()})}sortNodePosition(t){return!this.current||!this.sortInstanceNodePosition||this.type!==t.type?0:this.sortInstanceNodePosition(this.current,t.current)}updateFeatures(){let t="animation";for(t in lr){const n=lr[t];if(!n)continue;const{isEnabled:r,Feature:i}=n;if(!this.features[t]&&i&&r(this.props)&&(this.features[t]=new i(this)),this.features[t]){const o=this.features[t];o.isMounted?o.update():(o.mount(),o.isMounted=!0)}}}triggerBuild(){this.build(this.renderState,this.latestValues,this.props)}measureViewportBox(){return this.current?this.measureInstanceViewportBox(this.current,this.props):ne()}getStaticValue(t){return this.latestValues[t]}setStaticValue(t,n){this.latestValues[t]=n}update(t,n){(t.transformTemplate||this.props.transformTemplate)&&this.scheduleRender(),this.prevProps=this.props,this.props=t,this.prevPresenceContext=this.presenceContext,this.presenceContext=n;for(let r=0;r<Sd.length;r++){const i=Sd[r];this.propEventSubscriptions[i]&&(this.propEventSubscriptions[i](),delete this.propEventSubscriptions[i]);const o="on"+i,s=t[o];s&&(this.propEventSubscriptions[i]=this.on(i,s))}this.prevMotionValues=E2(this,this.scrapeMotionValuesFromProps(t,this.prevProps,this),this.prevMotionValues),this.handleChildMotionValue&&this.handleChildMotionValue(),this.onUpdate&&this.onUpdate(this)}getProps(){return this.props}getVariant(t){return this.props.variants?this.props.variants[t]:void 0}getDefaultTransition(){return this.props.transition}getTransformPagePoint(){return this.props.transformPagePoint}getClosestVariantNode(){return this.isVariantNode?this:this.parent?this.parent.getClosestVariantNode():void 0}addVariantChild(t){const n=this.getClosestVariantNode();if(n)return n.variantChildren&&n.variantChildren.add(t),()=>n.variantChildren.delete(t)}addValue(t,n){const r=this.values.get(t);n!==r&&(r&&this.removeValue(t),this.bindToMotionValue(t,n),this.values.set(t,n),this.latestValues[t]=n.get())}removeValue(t){this.values.delete(t);const n=this.valueSubscriptions.get(t);n&&(n(),this.valueSubscriptions.delete(t)),delete this.latestValues[t],this.removeValueFromRenderState(t,this.renderState)}hasValue(t){return this.values.has(t)}getValue(t,n){if(this.props.values&&this.props.values[t])return this.props.values[t];let r=this.values.get(t);return r===void 0&&n!==void 0&&(r=pi(n===null?void 0:n,{owner:this}),this.addValue(t,r)),r}readValue(t,n){var r;let i=this.latestValues[t]!==void 0||!this.current?this.latestValues[t]:(r=this.getBaseTargetFromProps(this.props,t))!==null&&r!==void 0?r:this.readValueFromInstance(this.current,t,this.options);return i!=null&&(typeof i=="string"&&(zm(i)||Dm(i))?i=parseFloat(i):!x2(i)&&Qt.test(n)&&(i=Im(t,n)),this.setBaseTarget(t,xe(i)?i.get():i)),xe(i)?i.get():i}setBaseTarget(t,n){this.baseTarget[t]=n}getBaseTarget(t){var n;const{initial:r}=this.props;let i;if(typeof r=="string"||typeof r=="object"){const s=Pu(this.props,r,(n=this.presenceContext)===null||n===void 0?void 0:n.custom);s&&(i=s[t])}if(r&&i!==void 0)return i;const o=this.getBaseTargetFromProps(this.props,t);return o!==void 0&&!xe(o)?o:this.initialValues[t]!==void 0&&i===void 0?void 0:this.baseTarget[t]}on(t,n){return this.events[t]||(this.events[t]=new ju),this.events[t].add(n)}notify(t,...n){this.events[t]&&this.events[t].notify(...n)}}class yg extends C2{constructor(){super(...arguments),this.KeyframeResolver=Hm}sortInstanceNodePosition(t,n){return t.compareDocumentPosition(n)&2?1:-1}getBaseTargetFromProps(t,n){return t.style?t.style[n]:void 0}removeValueFromRenderState(t,{vars:n,style:r}){delete n[t],delete r[t]}handleChildMotionValue(){this.childSubscription&&(this.childSubscription(),delete this.childSubscription);const{children:t}=this.props;xe(t)&&(this.childSubscription=t.on("change",n=>{this.current&&(this.current.textContent=`${n}`)}))}}function P2(e){return window.getComputedStyle(e)}class T2 extends yg{constructor(){super(...arguments),this.type="html",this.renderInstance=om}readValueFromInstance(t,n){if(Pn.has(n)){const r=Hu(n);return r&&r.default||0}else{const r=P2(t),i=(nm(n)?r.getPropertyValue(n):r[n])||0;return typeof i=="string"?i.trim():i}}measureInstanceViewportBox(t,{transformPagePoint:n}){return sg(t,n)}build(t,n,r){Au(t,n,r.transformTemplate)}scrapeMotionValuesFromProps(t,n,r){return Lu(t,n,r)}}class k2 extends yg{constructor(){super(...arguments),this.type="svg",this.isSVGTag=!1,this.measureInstanceViewportBox=ne}getBaseTargetFromProps(t,n){return t[n]}readValueFromInstance(t,n){if(Pn.has(n)){const r=Hu(n);return r&&r.default||0}return n=sm.has(n)?n:xu(n),t.getAttribute(n)}scrapeMotionValuesFromProps(t,n,r){return um(t,n,r)}build(t,n,r){Nu(t,n,this.isSVGTag,r.transformTemplate)}renderInstance(t,n,r,i){lm(t,n,r,i)}mount(t){this.isSVGTag=Du(t.tagName),super.mount(t)}}const A2=(e,t)=>Cu(e)?new k2(t):new T2(t,{allowProjection:e!==E.Fragment}),N2=Jy({...Zw,...v2,...s2,...y2},A2),gr=py(N2);function M2(e,t){return Object.keys(t).forEach(r=>{e[r]=t[r]}),e}function D2(e){return!!e&&typeof e=="object"}function L2(e){var t=Object.prototype.toString.call(e);return t==="[object RegExp]"||t==="[object Date]"||V2(e)}var R2=typeof Symbol=="function"&&Symbol.for,_2=R2?Symbol.for("react.element"):60103;function V2(e){return e.$$typeof===_2}function O2(e){return D2(e)&&!L2(e)}function I2(e){return Array.isArray(e)?[]:{}}function vi(e,t){return t.clone!==!1&&t.isMergeableObject(e)?Yo(I2(e),e,t):e}function F2(e,t,n){return e.concat(t).map(function(r){return vi(r,n)})}function j2(e,t){if(!t.customMerge)return Yo;const n=t.customMerge(e);return typeof n=="function"?n:Yo}function z2(e){return Object.getOwnPropertySymbols?Object.getOwnPropertySymbols(e).filter(function(t){return e.propertyIsEnumerable(t)}):[]}function xd(e){return Object.keys(e).concat(z2(e))}function wg(e,t){try{return t in e}catch{return!1}}function B2(e,t){return wg(e,t)&&!(Object.hasOwnProperty.call(e,t)&&Object.propertyIsEnumerable.call(e,t))}function U2(e,t,n){const r={};return n.isMergeableObject(e)&&xd(e).forEach(function(i){r[i]=vi(e[i],n)}),xd(t).forEach(function(i){B2(e,i)||(wg(e,i)&&n.isMergeableObject(t[i])?r[i]=j2(i,n)(e[i],t[i],n):r[i]=vi(t[i],n))}),r}function Yo(e,t,n){n=n||{},n.arrayMerge=n.arrayMerge||F2,n.isMergeableObject=n.isMergeableObject||O2,n.cloneUnlessOtherwiseSpecified=vi;var r=Array.isArray(t),i=Array.isArray(e),o=r===i;return o?r?n.arrayMerge(e,t,n):U2(e,t,n):vi(t,n)}const Sg=[];class $2{constructor(t,n){Tn(this,"value",null);Tn(this,"_name",null);Tn(this,"_initial");Tn(this,"_prototype");Tn(this,"_listeners",[]);this._initial=t,this._prototype=n,this.set(t)}get exists(){return!!this.value}listen(t){return this._listeners.push(t),this}unlisten(t){return this._listeners.splice(this._listeners.indexOf(t),1),this}reset(){return this.set(this._initial),this}force(){return Object.values(this._listeners).forEach(t=>t(this.value)),this}set(t,n){this.value=this._prototype?xg(t,this._prototype):t;for(const r of Object.values(this._listeners))r(this.value,n);return this}patch(t,n){const r=n!=null&&n.deep?Yo(this.value,t):M2(this.value,t);return this.set({...r},t),this}getName(){return this._name}name(t){return this._name=t,Sg.push(this),this}persist(){const t=`stack_${this._name}`;function n(o){o?localStorage.setItem(t,JSON.stringify(o)):localStorage.removeItem(t)}const r=localStorage.getItem(t),i=r?JSON.parse(r):null;return i&&this.set(i),this.listen(o=>n(o)),i!==this.value&&n(this.value),this}toggle(){if(typeof this.value!="boolean")throw new Error("Cannot toggle non-boolean state.");const t=!this.value;return this.set(t),this}increment(){if(typeof this.value!="number")throw new Error("Cannot increment non-number state.");return this.set(this.value+1),this}decrement(){if(typeof this.value!="number")throw new Error("Cannot increment non-number state.");return this.set(this.value-1),this}mock(t){return window.invokeNative?this:(this.value=t,this)}}function xg(e,t){if(typeof e!="object")throw new Error("Cannot apply prototype to non-objects.");let n=e;if(n)if(!Array.isArray(e))n=Object.assign(Object.create(t),e);else for(let r=0;r<e.length;r++)n[r]=xg(e[r],t);return n}function ge(e=null,t=null){return new $2(e,t)}function K(e){const t=Array.isArray(e),n=t?e:[e],[r,i]=E.useState({});return E.useEffect(()=>{const o=[];for(const s of n){const l=()=>i({});s.listen(l),o.push([s,l])}return()=>{o.forEach(([s,l])=>s.unlisten(l))}},[]),t?n.map(o=>o.value):n[0].value}window.addEventListener("message",e=>{if(e.data.action==="mutate"){const n=e.data.key,r=e.data.method;let i=Sg.find(o=>o.getName()===n);r=="collect"&&i&&i.reset(),i?i[r](...e.data.params):console.error(`Mutation (${r}): Key \`${n}\` points to an invalid stack reference.`)}});const H2=(e,t)=>{const n=Object.keys(Ag),r=n.indexOf(e);return n.indexOf(t)>r?"right":"left"},W2=(e,t,n=.25)=>({initial:e?{x:(e==="left"?"-":"")+"75%"}:{},animate:{x:0},exit:e?{x:(e==="left"?"-":"")+"75%"}:{},transition:{duration:n,ease:"easeInOut"},key:t});async function J(e,t,n){const r={method:"post",headers:{"Content-Type":"application/json; charset=UTF-8"},body:JSON.stringify(t)};if((window==null?void 0:window.invokeNative)&&n!==void 0)return n;const o="lb-newsapp";return new Promise((s,l)=>{fetch(`https://${o}/${e}`,r).then(a=>a.json()).then(a=>{s(a)}).catch(a=>a)})}const go=ge(null),M=(e,t)=>{var r;let n=((r=go.value)==null?void 0:r[e])??e;if(t)for(const[i,o]of Object.entries(t))n=n.replaceAll(`{${i}}`,String(o));return n};globalThis!=null&&globalThis.formatPhoneNumber||(globalThis.formatPhoneNumber=e=>e.replace(/(\d{3})(\d{3})(\d{4})/,"($1) $2-$3"));function Qr(e,t){const n=E.useRef();E.useEffect(()=>{n.current=t},[t]),E.useEffect(()=>{const r=i=>{const{action:o,data:s}=i.data;n.current&&o===e&&n.current(s)};return window.addEventListener("message",r),()=>window.removeEventListener("message",r)},[e])}const bo=e=>{let t=new Date(e),r=new Date().getTime()-t.getTime();return Math.floor(r/(1e3*60*60*24))===0?t.toLocaleTimeString("en-US",{hour:"numeric",minute:"numeric",hour12:!1}):`${t.getDate()} ${t.toLocaleDateString("en-US",{month:"short"})}`};function Ed(e){var t=document.createElement("textarea"),n=document.getSelection();return t.textContent=e,document.body.appendChild(t),n.removeAllRanges(),t.select(),document.execCommand("copy"),n.removeAllRanges(),document.body.removeChild(t),!0}const Eg=e=>{const t=/\.(mp4|mov|avi|mkv|flv|wmv|webm|ogg|ogv|3gp)/i;return typeof e=="string"?e.match(t)!==null:void 0};function Ai(e){if(!e)return!1;var t=e.getBoundingClientRect();if(!t)return!1;const r=document.querySelector(".news-app").getBoundingClientRect();return t.bottom<r.top||t.right<r.left||t.left>r.right||t.top>r.bottom}function Nn(e){const{setPopUp:t}=globalThis;e&&t({title:M("ERROR"),description:e,buttons:[{title:M("OK")}]})}function xs({data:e,last:t}){var l,a,u;const[n,r]=E.useState(JSON.parse(localStorage.getItem("seenArticles")||"[]").includes(e.id)),[i,o]=E.useState((a=(l=e.content.find(c=>c.type==="media"))==null?void 0:l.content.find(c=>c.type==="image"))==null?void 0:a.src);let s=(u=e.content.find(c=>c.type==="text"))==null?void 0:u.content;return L("div",{id:t?"last":void 0,className:"item","data-seen":n,onClick:()=>Px(e),children:[L("div",{className:"item-content",children:[y("div",{className:"title",children:e.title}),y("div",{className:"description",children:s.substring(0,200)}),y("div",{className:"date",children:bo(e.published_at)})]}),i&&y("img",{src:i})]},e.id)}function Ni(){var t,n,r,i,o,s,l,a;const e=K(cr);return(t=e==null?void 0:e.Header)!=null&&t.image?y("img",{src:e.Header.image}):y("div",{className:"title",style:{color:(n=e==null?void 0:e.Header)!=null&&n.color?((i=(r=globalThis==null?void 0:globalThis.settings)==null?void 0:r.display)==null?void 0:i.theme)==="dark"?(o=e==null?void 0:e.Header)==null?void 0:o.color.dark:(s=e==null?void 0:e.Header)==null?void 0:s.color.light:null,fontSize:((l=e==null?void 0:e.Header)==null?void 0:l.fontSize)??null},children:((a=e==null?void 0:e.Header)==null?void 0:a.text)??"Ludendorff Weekly"})}var Cg={color:void 0,size:void 0,className:void 0,style:void 0,attr:void 0},Cd=fn.createContext&&fn.createContext(Cg),K2=["attr","size","title"];function G2(e,t){if(e==null)return{};var n=Q2(e,t),r,i;if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);for(i=0;i<o.length;i++)r=o[i],!(t.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(e,r)&&(n[r]=e[r])}return n}function Q2(e,t){if(e==null)return{};var n={};for(var r in e)if(Object.prototype.hasOwnProperty.call(e,r)){if(t.indexOf(r)>=0)continue;n[r]=e[r]}return n}function Zo(){return Zo=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},Zo.apply(this,arguments)}function Pd(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),n.push.apply(n,r)}return n}function qo(e){for(var t=1;t<arguments.length;t++){var n=arguments[t]!=null?arguments[t]:{};t%2?Pd(Object(n),!0).forEach(function(r){X2(e,r,n[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):Pd(Object(n)).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(n,r))})}return e}function X2(e,t,n){return t=Y2(t),t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function Y2(e){var t=b2(e,"string");return typeof t=="symbol"?t:t+""}function b2(e,t){if(typeof e!="object"||!e)return e;var n=e[Symbol.toPrimitive];if(n!==void 0){var r=n.call(e,t||"default");if(typeof r!="object")return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function Pg(e){return e&&e.map((t,n)=>fn.createElement(t.tag,qo({key:n},t.attr),Pg(t.child)))}function Ae(e){return t=>fn.createElement(Z2,Zo({attr:qo({},e.attr)},t),Pg(e.child))}function Z2(e){var t=n=>{var{attr:r,size:i,title:o}=e,s=G2(e,K2),l=i||n.size||"1em",a;return n.className&&(a=n.className),e.className&&(a=(a?a+" ":"")+e.className),fn.createElement("svg",Zo({stroke:"currentColor",fill:"currentColor",strokeWidth:"0"},n.attr,r,s,{className:a,style:qo(qo({color:e.color||n.color},n.style),e.style),height:l,width:l,xmlns:"http://www.w3.org/2000/svg"}),o&&fn.createElement("title",null,o),e.children)};return Cd!==void 0?fn.createElement(Cd.Consumer,null,n=>t(n)):t(Cg)}function Tg(e){return Ae({tag:"svg",attr:{viewBox:"0 0 512 512"},child:[{tag:"path",attr:{fill:"none",strokeMiterlimit:"10",strokeWidth:"32",d:"M448 256c0-106-86-192-192-192S64 150 64 256s86 192 192 192 192-86 192-192z"},child:[]},{tag:"path",attr:{fill:"none",strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"32",d:"M256 176v160m80-80H176"},child:[]}]})(e)}function q2(e){return Ae({tag:"svg",attr:{viewBox:"0 0 512 512"},child:[{tag:"path",attr:{fill:"none",strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"32",d:"M256 112v288m144-144H112"},child:[]}]})(e)}function J2(e){return Ae({tag:"svg",attr:{viewBox:"0 0 512 512"},child:[{tag:"path",attr:{d:"M391 480c-19.52 0-46.94-7.06-88-30-49.93-28-88.55-53.85-138.21-103.38C116.91 298.77 93.61 267.79 61 208.45c-36.84-67-30.56-102.12-23.54-117.13C45.82 73.38 58.16 62.65 74.11 52a176.3 176.3 0 0 1 28.64-15.2c1-.43 1.93-.84 2.76-1.21 4.95-2.23 12.45-5.6 21.95-2 6.34 2.38 12 7.25 20.86 16 18.17 17.92 43 57.83 52.16 77.43 6.15 13.21 10.22 21.93 10.23 31.71 0 11.45-5.76 20.28-12.75 29.81-1.31 1.79-2.61 3.5-3.87 5.16-7.61 10-9.28 12.89-8.18 18.05 2.23 10.37 18.86 41.24 46.19 68.51s57.31 42.85 67.72 45.07c5.38 1.15 8.33-.59 18.65-8.47 1.48-1.13 3-2.3 4.59-3.47 10.66-7.93 19.08-13.54 30.26-13.54h.06c9.73 0 18.06 4.22 31.86 11.18 18 9.08 59.11 33.59 77.14 51.78 8.77 8.84 13.66 14.48 16.05 20.81 3.6 9.53.21 17-2 22-.37.83-.78 1.74-1.21 2.75a176.49 176.49 0 0 1-15.29 28.58c-10.63 15.9-21.4 28.21-39.38 36.58A67.42 67.42 0 0 1 391 480z"},child:[]}]})(e)}function ur(e){return Ae({tag:"svg",attr:{viewBox:"0 0 512 512"},child:[{tag:"path",attr:{fill:"none",strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"48",d:"M328 112 184 256l144 144"},child:[]}]})(e)}function mn(e){return Ae({tag:"svg",attr:{viewBox:"0 0 512 512"},child:[{tag:"path",attr:{fill:"none",strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"48",d:"m184 112 144 144-144 144"},child:[]}]})(e)}function Td(e){return Ae({tag:"svg",attr:{viewBox:"0 0 512 512"},child:[{tag:"path",attr:{d:"M408 480H184a72 72 0 0 1-72-72V184a72 72 0 0 1 72-72h224a72 72 0 0 1 72 72v224a72 72 0 0 1-72 72z"},child:[]},{tag:"path",attr:{d:"M160 80h235.88A72.12 72.12 0 0 0 328 32H104a72 72 0 0 0-72 72v224a72.12 72.12 0 0 0 48 67.88V160a80 80 0 0 1 80-80z"},child:[]}]})(e)}function ex(e){return Ae({tag:"svg",attr:{viewBox:"0 0 512 512"},child:[{tag:"circle",attr:{cx:"256",cy:"256",r:"26"},child:[]},{tag:"circle",attr:{cx:"346",cy:"256",r:"26"},child:[]},{tag:"circle",attr:{cx:"166",cy:"256",r:"26"},child:[]},{tag:"path",attr:{fill:"none",strokeMiterlimit:"10",strokeWidth:"32",d:"M448 256c0-106-86-192-192-192S64 150 64 256s86 192 192 192 192-86 192-192z"},child:[]}]})(e)}function tx(e){return Ae({tag:"svg",attr:{viewBox:"0 0 512 512"},child:[{tag:"circle",attr:{cx:"256",cy:"256",r:"48"},child:[]},{tag:"circle",attr:{cx:"416",cy:"256",r:"48"},child:[]},{tag:"circle",attr:{cx:"96",cy:"256",r:"48"},child:[]}]})(e)}function nx(e){return Ae({tag:"svg",attr:{viewBox:"0 0 512 512"},child:[{tag:"path",attr:{d:"M204 240H68a36 36 0 0 1-36-36V68a36 36 0 0 1 36-36h136a36 36 0 0 1 36 36v136a36 36 0 0 1-36 36zm240 0H308a36 36 0 0 1-36-36V68a36 36 0 0 1 36-36h136a36 36 0 0 1 36 36v136a36 36 0 0 1-36 36zM204 480H68a36 36 0 0 1-36-36V308a36 36 0 0 1 36-36h136a36 36 0 0 1 36 36v136a36 36 0 0 1-36 36zm240 0H308a36 36 0 0 1-36-36V308a36 36 0 0 1 36-36h136a36 36 0 0 1 36 36v136a36 36 0 0 1-36 36z"},child:[]}]})(e)}function rx(e){return Ae({tag:"svg",attr:{viewBox:"0 0 512 512"},child:[{tag:"path",attr:{d:"M424 80H88a56.06 56.06 0 0 0-56 56v240a56.06 56.06 0 0 0 56 56h336a56.06 56.06 0 0 0 56-56V136a56.06 56.06 0 0 0-56-56zm-14.18 92.63-144 112a16 16 0 0 1-19.64 0l-144-112a16 16 0 1 1 19.64-25.26L256 251.73l134.18-104.36a16 16 0 0 1 19.64 25.26z"},child:[]}]})(e)}function ix(e){return Ae({tag:"svg",attr:{viewBox:"0 0 512 512"},child:[{tag:"path",attr:{d:"M439.91 112h-23.82a.09.09 0 0 0-.09.09V416a32 32 0 0 0 32 32 32 32 0 0 0 32-32V152.09A40.09 40.09 0 0 0 439.91 112z"},child:[]},{tag:"path",attr:{d:"M384 416V72a40 40 0 0 0-40-40H72a40 40 0 0 0-40 40v352a56 56 0 0 0 56 56h342.85a1.14 1.14 0 0 0 1.15-1.15 1.14 1.14 0 0 0-.85-1.1A64.11 64.11 0 0 1 384 416zM96 128a16 16 0 0 1 16-16h64a16 16 0 0 1 16 16v64a16 16 0 0 1-16 16h-64a16 16 0 0 1-16-16zm208 272H112.45c-8.61 0-16-6.62-16.43-15.23A16 16 0 0 1 112 368h191.55c8.61 0 16 6.62 16.43 15.23A16 16 0 0 1 304 400zm0-64H112.45c-8.61 0-16-6.62-16.43-15.23A16 16 0 0 1 112 304h191.55c8.61 0 16 6.62 16.43 15.23A16 16 0 0 1 304 336zm0-64H112.45c-8.61 0-16-6.62-16.43-15.23A16 16 0 0 1 112 240h191.55c8.61 0 16 6.62 16.43 15.23A16 16 0 0 1 304 272zm0-64h-63.55c-8.61 0-16-6.62-16.43-15.23A16 16 0 0 1 240 176h63.55c8.61 0 16 6.62 16.43 15.23A16 16 0 0 1 304 208zm0-64h-63.55c-8.61 0-16-6.62-16.43-15.23A16 16 0 0 1 240 112h63.55c8.61 0 16 6.62 16.43 15.23A16 16 0 0 1 304 144z"},child:[]}]})(e)}function ox(e){return Ae({tag:"svg",attr:{viewBox:"0 0 512 512"},child:[{tag:"path",attr:{fill:"none",strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"32",d:"M364.13 125.25 87 403l-23 45 44.99-23 277.76-277.13-22.62-22.62zm56.56-56.56-22.62 22.62 22.62 22.63 22.62-22.63a16 16 0 0 0 0-22.62h0a16 16 0 0 0-22.62 0z"},child:[]}]})(e)}function sx(e){return Ae({tag:"svg",attr:{viewBox:"0 0 512 512"},child:[{tag:"path",attr:{d:"M332.64 64.58C313.18 43.57 286 32 256 32c-30.16 0-57.43 11.5-76.8 32.38-19.58 21.11-29.12 49.8-26.88 80.78C156.76 206.28 203.27 256 256 256s99.16-49.71 103.67-110.82c2.27-30.7-7.33-59.33-27.03-80.6zM432 480H80a31 31 0 0 1-24.2-11.13c-6.5-7.77-9.12-18.38-7.18-29.11C57.06 392.94 83.4 353.61 124.8 326c36.78-24.51 83.37-38 131.2-38s94.42 13.5 131.2 38c41.4 27.6 67.74 66.93 76.18 113.75 1.94 10.73-.68 21.34-7.18 29.11A31 31 0 0 1 432 480z"},child:[]}]})(e)}function lx(e){return Ae({tag:"svg",attr:{viewBox:"0 0 512 512"},child:[{tag:"path",attr:{fill:"none",strokeLinecap:"round",strokeMiterlimit:"10",strokeWidth:"32",d:"M320 146s24.36-12-64-12a160 160 0 1 0 160 160"},child:[]},{tag:"path",attr:{fill:"none",strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"32",d:"m256 58 80 80-80 80"},child:[]}]})(e)}function kg(e){return Ae({tag:"svg",attr:{viewBox:"0 0 512 512"},child:[{tag:"path",attr:{d:"M456.69 421.39 362.6 327.3a173.81 173.81 0 0 0 34.84-104.58C397.44 126.38 319.06 48 222.72 48S48 126.38 48 222.72s78.38 174.72 174.72 174.72A173.81 173.81 0 0 0 327.3 362.6l94.09 94.09a25 25 0 0 0 35.3-35.3zM97.92 222.72a124.8 124.8 0 1 1 124.8 124.8 124.95 124.95 0 0 1-124.8-124.8z"},child:[]}]})(e)}const vo=ge(null);function ax(){const e=K(mt),t=K(vo),n=K(nt),[r,i]=E.useState([]),[o,s]=E.useState(!1),[l,a]=E.useState(!1),[u,c]=E.useState(0);E.useEffect(()=>{J("News",{action:"getCategories"}).then(f=>{f&&mt.set(f)})},[]),E.useEffect(()=>{t&&J("News",{action:"getArticles",filter:{category:t}}).then(f=>{f&&i(f)})},[t]);const d=()=>{if(!t||o||l)return;let f=document.querySelector("#last");if(!f)return;!Ai(f)&&(a(!0),J("News",{action:"getArticles",filter:{category:t,page:u+1}},null).then(v=>{v&&v.length>0?(i([...r,...v]),a(!1),v.length<10&&s(!0)):s(!0)}),c(v=>v+1))};return L(Qe,{children:[L("div",{className:"news-header",children:[t&&y(ur,{onClick:()=>{te!=null&&te.value&&nt.set(te.value),vo.reset()}}),y(Ni,{}),t&&y(mn,{className:"hidden"})]}),y(gr.div,{initial:{x:(t?"":"-")+"75%"},animate:{x:0},exit:{x:(t?"":"-")+"75%"},transition:{duration:.2,ease:"easeInOut"},className:"news-content",children:t?y("div",{className:"items",onScroll:d,children:r.map((f,m)=>{let v=m===r.length-1;return y(xs,{data:f,last:v},f.id)})}):y("section",{className:"categories",children:Object.keys(e).map((f,m)=>L("div",{className:"item",onClick:()=>{e[f]!==0&&vo.set(f)},children:[y("div",{className:"title",children:f}),L("div",{className:"amount",children:[e[f],y(mn,{})]})]},m))})},(t?"category":"categories")+n)]})}function ux(){var l,a;const{setContextMenu:e}=components,t=K(cr),n=K(Mn),[r,i]=E.useState(null),o=E.useRef(!0);E.useEffect(()=>{if(n&&o.current){o.current=!1;let u=JSON.parse(localStorage.getItem("seenArticles")||"[]");localStorage.setItem("seenArticles",JSON.stringify([...u,n.id]));return}},[n]),Qr("articleUpdated",u=>{u&&i(u)});const s=()=>{var c;let u=[t.IsAdmin&&{title:M("DELETE_ARTICLE.TITLE"),color:"red",cb:()=>{J("News",{action:"adminDeleteArticle",articleId:n.id}).then(d=>{d&&(Mn.reset(),nt.set((te==null?void 0:te.value)??"home"))})}},((t==null?void 0:t.IsAdmin)||((c=t==null?void 0:t.Permissions)==null?void 0:c.unpublish))&&{title:M("UNPUBLISH_ARTICLE"),cb:()=>{J("News",{action:"unpublishOther",articleId:n.id}).then(d=>{d&&(Mn.reset(),nt.set(te!=null&&te.value&&(te==null?void 0:te.value)!=="article"?te==null?void 0:te.value:"home"))})}}];e({buttons:u.filter(Boolean)})};return L(Qe,{children:[L("div",{className:"news-header",children:[y(ur,{className:"back",onClick:()=>{Mn.reset(),xn.reset(),nt.set((te==null?void 0:te.value)??"home"),te.reset()}}),y(Ni,{}),t!=null&&t.IsAdmin||(l=t==null?void 0:t.Permissions)!=null&&l.unpublish?y(ex,{onClick:s}):y(ur,{className:"hidden"})]}),y(vu,{children:r&&L(gr.div,{initial:{opacity:0,y:-10},animate:{opacity:1,y:0},exit:{opacity:0,y:-10},transition:{duration:.2,ease:"easeInOut"},className:"updated-pill",onClick:()=>{Mn.set(r),i(null)},children:[y(lx,{}),M("ARTICLE_UPDATED")]})}),y("div",{className:"news-content",children:L("div",{className:"article",children:[L("div",{className:"article-author",onClick:()=>xn.set(n.author),children:[L("div",{className:"user",children:[y("img",{src:Xu((a=n==null?void 0:n.author)==null?void 0:a.avatar),alt:"User"}),y("div",{className:"name",children:n==null?void 0:n.author.display_name})]}),y(mn,{})]}),L("div",{className:"article-header",children:[y("div",{className:"category",onClick:()=>{te.set("article"),vo.set(n==null?void 0:n.category),nt.set("categories")},children:n==null?void 0:n.category}),y("div",{className:"title",children:n==null?void 0:n.title}),L("div",{className:"dates",children:[(n==null?void 0:n.updated_at)!==(n==null?void 0:n.published_at)&&y("div",{className:"updated",children:M("UPDATED_AT",{date:bo(n==null?void 0:n.updated_at)})}),y("div",{className:"published",children:M("PUBLISHED_AT",{date:bo(n==null?void 0:n.published_at)})})]})]}),y("div",{className:"article-content",children:n==null?void 0:n.content.map((u,c)=>y(E.Fragment,{children:u.type==="text"?y("div",{className:"text",children:u.content}):y(cx,{data:u.content})},c))})]})})]})}const cx=({data:e})=>{var l;const t=E.useRef(null),[n]=E.useState(e.map(a=>a.src)),[r,i]=E.useState(0),o={pos:{startLeft:0,startX:0},onMouseDown:a=>{o.pos={startLeft:t.current.scrollLeft,startX:a.clientX},t.current.style.userSelect="none",document.addEventListener("mouseup",o.onMouseUp),document.addEventListener("mousemove",o.onMove)},onMove:a=>{const u=a.clientX-o.pos.startX;t.current.scrollLeft=o.pos.startLeft-u;const c=t.current.getBoundingClientRect();(c.left-5>a.clientX||a.clientX>c.right-5)&&o.onMouseUp()},onMouseUp:()=>{t.current.style.removeProperty("user-select"),document.removeEventListener("mouseup",o.onMouseUp),document.removeEventListener("mousemove",o.onMove);const a=n,u=t.current.clientWidth;let c=r;const d=t.current.scrollLeft-o.pos.startLeft;d>u/2&&c<a.length-1?c++:d<-u/2&&c>0&&c--,s(c)}},s=a=>{a<0||a>=n.length||(t.current.scrollTo({left:a*t.current.offsetWidth,behavior:"smooth"}),i(a))};return L(Qe,{children:[L("div",{className:"images",children:[y("div",{className:"image-grid",ref:t,onMouseDown:o.onMouseDown,children:n&&n.map((a,u)=>y("div",{onClick:()=>components==null?void 0:components.setFullscreenImage(a),children:Eg(a)?y("video",{src:a,autoPlay:!0,loop:!0,muted:r!==u}):y("img",{src:a})},u))}),n.length>1&&L(Qe,{children:[L("div",{className:"arrows",children:[y(ur,{className:"left","data-disabled":r===0,onClick:()=>s(r-1)}),y(mn,{className:"right","data-disabled":r===n.length-1,onClick:()=>s(r+1)})]}),y("div",{className:"amount-wrapper",children:y("div",{className:"amount",children:M("IMAGE_AMOUNT",{current:r+1,total:n.length})})})]})]}),y(vu,{children:((l=e[r])==null?void 0:l.description)&&y(gr.div,{initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},transition:{duration:.2,ease:"easeInOut"},className:"image-caption",children:e[r].description})})]})},An=ge([]),kd=ge(0);function fx(){const e=K(An),[t,n]=E.useState(!1),[r,i]=E.useState(!1),o=K(kd);return E.useEffect(()=>{J("News",{action:"getArticles"}).then(l=>{l&&An.set(l)})},[]),Qr("newArticle",l=>{l&&An.set([l,...e])}),Qr("articleUpdated",l=>{l&&An.set(e.map(a=>a.id===l.id?l:a))}),Qr("articleRemoved",l=>{l&&An.set(e.filter(a=>a.id!==l))}),L(Qe,{children:[y("div",{className:"news-header",children:y(Ni,{})}),y("div",{className:"news-content",children:y("div",{className:"items",onScroll:()=>{if(t||r)return;let l=document.querySelector("#last");if(!l)return;!Ai(l)&&(i(!0),J("News",{action:"getArticles",filter:{page:o+1}},null).then(u=>{u&&u.length>0?(An.set([...e,...u]),i(!1),u.length<10&&n(!0)):n(!0)}),kd.increment())},children:e.map((l,a)=>{let u=a===e.length-1;return y(xs,{data:l,last:u},l.id)})})})]})}function dx(){const{setPopUp:e}=globalThis,t=K(xn),[n,r]=E.useState([]),[i,o]=E.useState(!1),[s,l]=E.useState(!1),[a,u]=E.useState(0);E.useEffect(()=>{J("News",{action:"getArticles",filter:{author:t.id}}).then(d=>{d&&r(d)})},[]);const c=()=>{if(i||s)return;let d=document.querySelector("#last");if(!d)return;!Ai(d)&&(l(!0),J("News",{action:"getArticles",filter:{author:t.id,page:a+1}},null).then(m=>{m&&m.length>0?(r([...n,...m]),l(!1),m.length<10&&o(!0)):o(!0)}),u(a+1))};return L(gr.div,{initial:{opacity:0,y:100},animate:{opacity:1,y:0},exit:{opacity:0,y:100},transition:{duration:.2,ease:"easeInOut"},className:"news-author-popup",onScroll:c,children:[L("div",{className:"author-header",children:[y("div",{className:"close",onClick:()=>xn.reset()}),L("div",{className:"user",children:[y("img",{src:Xu(t==null?void 0:t.avatar),alt:"User"}),y("div",{className:"name",children:t==null?void 0:t.display_name})]})]}),L("div",{className:"author-content",children:[(t==null?void 0:t.email)&&L("div",{className:"item",children:[y("div",{className:"label",children:M("EMAIL")}),y("div",{className:"value",children:L("div",{className:"item",children:[y("div",{className:"title",children:t.email}),L("div",{className:"buttons",children:[(globalThis==null?void 0:globalThis.setApp)&&y(rx,{onClick:()=>{globalThis==null||globalThis.setApp({name:"Mail",data:{view:"newMail",recipient:t.email}})}}),y(Td,{onClick:()=>{Ed(t.email),e({title:M("COPIED"),description:M("EMAIL_COPIED"),buttons:[{title:M("OK")}]})}})]})]})})]}),(t==null?void 0:t.phone_number)&&L("div",{className:"item",children:[y("div",{className:"label",children:M("PHONE_NUMBER")}),y("div",{className:"value",children:L("div",{className:"item",children:[y("div",{className:"title blue",children:globalThis.formatPhoneNumber(t.phone_number)}),L("div",{className:"buttons",children:[(globalThis==null?void 0:globalThis.createCall)&&y(J2,{onClick:()=>globalThis.createCall({number:t.phone_number})}),y(Td,{onClick:()=>{Ed(t.phone_number),e({title:M("COPIED"),description:M("NUMBER_COPIED"),buttons:[{title:M("OK")}]})}})]})]})})]}),L("div",{className:"item",children:[y("div",{className:"label",children:M("ARTICLES")}),y("div",{className:"value",children:n.map((d,f)=>{let m=f===n.length-1;return y("div",{className:"item-article",children:y(xs,{data:d,last:m})},d.id)})})]})]})]})}function hx(){const e=K(nt),t=K(b),n=[{icon:y(ix,{}),title:M("FOOTER.HOME"),value:"home"},{icon:y(nx,{}),title:M("FOOTER.CATEGORIES"),value:"categories"},{icon:y(kg,{}),title:M("FOOTER.SEARCH"),value:"search"},{icon:y(sx,{}),title:M("FOOTER.PROFILE"),value:"profile"}];return y("div",{className:"news-footer",children:n.filter(r=>r.value!=="profile"||t).map((r,i)=>L("div",{className:"item","data-active":e===r.value,onClick:()=>nt.set(r.value),children:[r==null?void 0:r.icon,r==null?void 0:r.title]},i))})}const px=({title:e,selected:t,options:n})=>{const[r,i]=E.useState(!1),o=E.useRef(null),[s,l]=E.useState(t),a=()=>{i(!r)},u=()=>{i(!1)};return E.useEffect(()=>{const c=d=>{o.current&&!o.current.contains(d.target)&&u()};return document.addEventListener("click",c),()=>{document.removeEventListener("click",c)}},[]),E.useEffect(()=>{l(t)},[t]),L("div",{className:`dropdown-container ${r?"dropdown-open":""}`,ref:o,children:[y("div",{className:"dropdown-toggle click-dropdown",onClick:a,children:s??e}),r&&y("div",{className:"dropdown-menu dropdown-active",onMouseLeave:u,children:y("ul",{children:n==null?void 0:n.filter(c=>c.title).map((c,d)=>y("li",{onClick:()=>{l(c.title),c.cb(),u()},children:y("a",{href:"#",children:c.title})},d))})})]})},tt=ge("home");function mx(){const e={home:y(gx,{}),newArticle:y(vx,{}),editProfile:y(yx,{}),arcticles:y(Ad,{type:"all"}),drafts:y(Ad,{type:"drafts"})},t=K(tt);return E.useEffect(()=>{J("News",{action:"getProfile"}).then(n=>{n!=null&&n.profile&&b.set({...n.profile,articleCount:n==null?void 0:n.articleCount,draftCount:n==null?void 0:n.draftCount})})},[]),L(Qe,{children:[L("div",{className:"news-header",children:[t!=="home"&&y(ur,{onClick:()=>{tt.reset(),Xr.reset()}}),y(Ni,{}),t!=="home"&&y(ur,{className:"hidden"})]}),y("div",{className:"news-content",style:{paddingBottom:"5rem"},children:y(gr.div,{initial:{x:(t==="home"?"-":"")+"75%"},animate:{x:0},exit:{x:(t==="home"?"-":"")+"75%"},transition:{duration:.2,ease:"easeInOut"},className:"profile-body",children:e[t]},t)})]})}const gx=()=>{var n;const e=K(b),t=K(cr);return L(Qe,{children:[((n=t==null?void 0:t.Permissions)==null?void 0:n.create)&&y("section",{children:L("div",{className:"item",onClick:()=>tt.set("newArticle"),children:[y("div",{className:"title",children:M("NEW_ARTICLE")}),y(Tg,{})]})}),L("section",{children:[L("div",{className:"item",onClick:()=>tt.set("editProfile"),children:[y("div",{className:"title",children:M("PUBLIC_PROFILE")}),y(mn,{})]}),L("div",{className:"item",onClick:()=>{(e==null?void 0:e.articleCount)!==0&&tt.set("arcticles")},children:[y("div",{className:"title",children:M("ARTICLES")}),L("div",{className:"amount",children:[e==null?void 0:e.articleCount,y(mn,{})]})]}),L("div",{className:"item",onClick:()=>{e.draftCount!==0&&tt.set("drafts")},children:[y("div",{className:"title",children:M("DRAFTS")}),L("div",{className:"amount",children:[e.draftCount,y(mn,{})]})]})]})]})},Xr=ge(null),vx=()=>{var d;const e=K(Xr),t=K(cr),[n,r]=E.useState(e??{}),[i,o]=E.useState((e==null?void 0:e.content)??[]),s=K(mt),{setPopUp:l,setContextMenu:a,setGallery:u}=components,c=f=>{if(!n.category||!n.title||!i.length)return Nn(M("REQUIRED_FIELDS"));if(!i.some(m=>m.type==="text"))return Nn(M("REQUIRED_TEXT_FIELD"));if(i.some(m=>m.type==="media"&&m.content.length===0))return Nn(M("EMPTY_MEDIA_FIELD"));e?J("News",{action:"updateArticle",data:{articleId:e==null?void 0:e.id,title:n.title,category:n.category,content:i,publish:!f}}).then(m=>{if(!(m!=null&&m.success))return Nn(M("FAILED_PUBLISH",{reason:m==null?void 0:m.reason}));e.published!==!f&&b.patch({articleCount:b.value.articleCount+(f?-1:1),draftCount:b.value.draftCount+(f?1:-1)}),Xr.reset(),tt.reset()}):J("News",{action:"createArticle",data:{title:n.title,category:n.category,content:i,publish:!f}}).then(m=>{if(!(m!=null&&m.success))return Nn(M("FAILED_PUBLISH",{reason:m==null?void 0:m.reason}));tt.reset(),b.patch({articleCount:b.value.articleCount+(f?0:1),draftCount:b.value.draftCount+(f?1:0)})})};return L(Qe,{children:[L("div",{className:"item form",children:[y("div",{className:"label",children:M("CATEGORY")}),y(px,{title:M("SELECT_CATEGORY"),selected:n.category,options:[((d=t.Permissions)==null?void 0:d.categories)&&{title:M("ADD_CATEGORY.TITLE"),cb:()=>{var m;if(!((m=t.Permissions)!=null&&m.categories))return;let f="";l({title:M("ADD_CATEGORY.TITLE"),description:M("ADD_CATEGORY.DESCRIPTION"),input:{type:"text",placeholder:M("ADD_CATEGORY.PLACEHOLDER"),onChange:v=>{f=v}},buttons:[{title:M("CANCEL")},{title:M("ADD_CATEGORY.ADD"),cb:()=>{if(!(!f||f.length<2)){if(mt[f])return r(v=>({...v,category:f}));mt.set({...mt==null?void 0:mt.value,[f]:0}),r(v=>({...v,category:f}))}}}]})}},...Object.keys(s).map(f=>({title:f,selected:n.category===f,cb:()=>r(m=>({...m,category:f}))}))]})]}),L("div",{className:"item form",children:[y("div",{className:"label",children:M("TITLE")}),y("input",{type:"text",placeholder:M("TITLE"),value:n.title,onChange:f=>r(m=>({...m,title:f.target.value}))})]}),i.map((f,m)=>{var v,w;return L("div",{className:"item form",children:[L("div",{className:"label",children:[f.type==="text"?M("TEXT"):M("MEDIA"),y(tx,{onClick:()=>{let C=[m!==0&&{title:M("MOVE_UP"),cb:()=>o(p=>{const h=[...p],g=h[m-1];return h[m-1]=h[m],h[m]=g,h})},m!==i.length-1&&{title:M("MOVE_DOWN"),cb:()=>o(p=>{const h=[...p],g=h[m+1];return h[m+1]=h[m],h[m]=g,h})},{title:M("DELETE"),color:"red",cb:()=>o(p=>p.filter((h,g)=>g!==m))}];a({buttons:C.filter(Boolean)})}})]}),f.type==="media"?L("div",{className:"images",children:[(v=f.content)==null?void 0:v.map((C,p)=>L("div",{className:"attachment",children:[Eg(C.src)?y("video",{src:C.src}):y("img",{src:C.src}),y(ox,{className:"edit",onClick:()=>{a({buttons:[{title:M("SET_DESCRIPTION"),cb:()=>{let h="";l({title:M("EDIT_IMAGE"),description:M("SET_DESCRIPTION_INFO"),input:{type:"text",placeholder:M("DESCRIPTION"),defaultValue:C.description,onChange:g=>{h=g}},buttons:[{title:M("CANCEL")},{title:M("SAVE"),cb:()=>{o(g=>{const S=[...g];return S[m].content[p].description=h,S})}}]})}},{title:M("DELETE"),color:"red",cb:()=>{o(h=>{const g=[...h];return g[m].content=g[m].content.filter((S,x)=>x!==p),g})}}]})}})]},p)),((w=f.content)==null?void 0:w.length)<6&&y("div",{className:"attachment",onClick:()=>{u({multiSelect:!0,includeImages:!0,includeVideos:!0,onSelect:C=>{o(p=>{C!=null&&C.length||(C=[C]);let h=C.slice(0,6-f.content.length).map(g=>({src:g.src,description:"",type:g.isVideo?"video":"image"}));return p.map((g,S)=>(S===m&&(g.content=[...g.content,...h]),g))})}})},children:y(q2,{})})]}):y("textarea",{value:f.content,onChange:C=>{o(p=>{const h=[...p];return h[m].content=C.target.value,h})}})]},m)}),y("section",{children:L("div",{className:"item",onClick:()=>{a({buttons:[{title:M("TEXT"),cb:()=>{o([...i,{type:"text",content:""}])}},{title:M("MEDIA"),cb:()=>{o([...i,{type:"media",content:[]}])}}]})},children:[y("div",{className:"title",children:M("ADD_FIELD")}),y(Tg,{})]})}),y("div",{className:"button",onClick:()=>{let f=[!(e!=null&&e.published)&&{title:M("PUBLISH"),cb:()=>c(!1)},(e==null?void 0:e.published)&&{title:M("SAVE"),cb:()=>c(!1)},{title:e!=null&&e.published?"Unpublish":"Save Draft",cb:()=>c(!0)},e&&{title:"Delete",color:"red",cb:()=>{l({title:M("DELETE_ARTICLE.TITLE"),description:M("DELETE_ARTICLE.DESCRIPTION"),buttons:[{title:M("CANCEL")},{title:M("DELETE"),color:"red",cb:()=>{J("News",{action:"deleteArticle",articleId:e.id}).then(m=>{if(!m)return console.log("Failed to delete article");tt.reset(),Xr.reset(),b.patch({articleCount:e!=null&&e.published?b.value.articleCount-1:b.value.articleCount,draftCount:e!=null&&e.published?b.value.draftCount:b.value.draftCount-1})})}}]})}}];a({buttons:f.filter(Boolean)})},children:M(e?"MANAGE":"UPLOAD")})]})},yx=()=>{const{setPopUp:e,setContextMenu:t,setGallery:n}=components,[r,i]=E.useState(b==null?void 0:b.value),o=()=>{if(!r.display_name||(r==null?void 0:r.display_name.length)<2)return Nn(M("SET_NAME"));J("News",{action:"updateProfile",profile:r}).then(s=>{if(!s)return console.log("Failed to save profile");b.set(r),tt.reset()})};return L(Qe,{children:[L("div",{className:"item profile",children:[y("img",{src:Xu(r.avatar)}),y("div",{className:"title",onClick:()=>{let s=[{title:M("CHANGE_AVATAR"),cb:()=>{n({onSelect:l=>i(a=>({...a,avatar:l.src}))})}},r.avatar&&{title:M("REMOVE_AVATAR"),color:"red",cb:()=>i(l=>({...l,avatar:null}))}];t({buttons:s.filter(Boolean)})},children:M("EDIT_AVATAR")})]}),L("div",{className:"item form",children:[y("div",{className:"label",children:M("NAME")}),y("input",{type:"text",value:r.display_name,placeholder:M("NAME"),onChange:s=>i(l=>({...l,display_name:s.target.value}))})]}),L("div",{className:"item form",children:[y("div",{className:"label",children:M("EMAIL")}),y("input",{type:"text",defaultValue:r.email,placeholder:"<EMAIL>",onChange:s=>i(l=>({...l,email:s.target.value}))})]}),L("div",{className:"item form",children:[y("div",{className:"label",children:M("PHONE_NUMBER")}),y("input",{type:"text",defaultValue:r.phone_number,placeholder:globalThis==null?void 0:globalThis.formatPhoneNumber("1234567890"),onChange:s=>i(l=>({...l,phone_number:s.target.value}))})]}),y("div",{className:"button",onClick:o,children:M("SAVE")})]})},Ad=({type:e})=>{const[t,n]=E.useState([]),[r,i]=E.useState(!1),[o,s]=E.useState(!1),[l,a]=E.useState(0);return E.useEffect(()=>{J("News",{action:"getArticles",filter:{own:!0,drafts:e==="drafts"}}).then(c=>{c&&n(c)})},[]),y("div",{className:"items no-padding",onScroll:()=>{if(r||o)return;let c=document.querySelector("#last");if(!c)return;!Ai(c)&&(s(!0),J("News",{action:"getArticles",filter:{own:!0,drafts:e==="drafts",page:l+1}},null).then(f=>{f&&f.length>0?(n([...t,...f]),s(!1),f.length<10&&i(!0)):i(!0)}),a(l+1))},children:t.map((c,d)=>{let f=d===t.length-1;return y(wx,{data:c,published:e!=="drafts",last:f},c.id)})})};function wx({data:e,published:t,last:n}){var o,s,l,a;const[r]=E.useState((l=(s=(o=e==null?void 0:e.content)==null?void 0:o.find(u=>u.type==="media"))==null?void 0:s.content.find(u=>u.type==="image"))==null?void 0:l.src);let i=(a=e.content.find(u=>u.type==="text"))==null?void 0:a.content;return L("div",{id:n?"last":void 0,className:"item",onClick:()=>{Xr.set({...e,published:t}),tt.set("newArticle")},children:[L("div",{className:"item-content",children:[y("div",{className:"title",children:e.title}),y("div",{className:"description",children:i.substring(0,200)}),y("div",{className:"date",children:bo(e.published_at)})]}),r&&y("img",{src:r})]},e.id)}const Ji=ge([]),Nd=ge(""),Md=ge(0);function Sx(){const e=K(Ji),t=K(Nd),[n,r]=E.useState(""),[i,o]=E.useState(!1),[s,l]=E.useState(!1),a=K(Md);E.useEffect(()=>{if(!t||t.length===0){Ji.reset();return}J("News",{action:"getArticles",filter:{search:t}}).then(c=>{c&&Ji.set(c)})},[t]),E.useEffect(()=>{const c=setTimeout(()=>Nd.set(n),500);return()=>clearTimeout(c)},[n]);const u=()=>{if(i||s)return;let c=document.querySelector("#last");if(!c)return;!Ai(c)&&(l(!0),J("News",{action:"getArticles",filter:{search:t,page:a+1}},null).then(f=>{f&&f.length>0?(Ji.set([...e,...f]),l(!1),f.length<10&&o(!0)):o(!0)}),Md.increment())};return L(Qe,{children:[y("div",{className:"news-header",children:y(Ni,{})}),L("div",{className:"news-content",children:[L("div",{className:"search",children:[y(kg,{}),y("input",{placeholder:M("SEARCH_PLACEHOLDER"),value:n,onChange:c=>r(c.target.value)})]}),y("div",{className:"items no-padding",onScroll:u,children:e.map((c,d)=>{let f=d===e.length-1;return y(xs,{data:c,last:f},c.id)})})]})]})}const xx=!(window!=null&&window.invokeNative);function Ex({children:e}){const[t,n]=E.useState("00:00");return E.useEffect(()=>{const r=setInterval(()=>{const i=new Date;n(`${i.getHours().toString().padStart(2,"0")}:${i.getMinutes().toString().padStart(2,"0")}`)},1e3);return()=>clearInterval(r)},[]),L("div",{className:"frame","data-device":"tablet","data-theme":"light",children:[xx&&L(Qe,{children:[y("div",{className:"notch"}),y("div",{className:"time",children:t}),y("div",{className:"indicator"})]}),y("div",{className:"content",children:e})]})}const va=!(window!=null&&window.invokeNative),cr=ge(null),nt=ge("home"),Dd=ge(null),b=ge(null).mock({display_name:"John Doe",email:"<EMAIL>",id:"1"}),mt=ge({Shootings:0,Robberies:0,Economy:0,Politics:0,Life:0,Sports:0,Entertainment:0,Technology:0,Science:0,Health:0,Travel:0,Weather:0,Opinion:0}),Mn=ge(null),xn=ge(null),te=ge(null),Ag={home:y(fx,{}),categories:y(ax,{}),search:y(Sx,{}),profile:y(mx,{}),article:y(ux,{})},Cx=()=>{var o,s;const e=K(go),t=K(xn),n=K(nt),r=K(Dd),i=E.useRef(null);return E.useEffect(()=>{if(va){document.getElementsByTagName("html")[0].style.visibility="visible",document.getElementsByTagName("body")[0].style.visibility="visible",go.set({});return}J("News",{action:"getConfig"}).then(l=>{cr.set({...l,Locales:null}),go.set(l.Locales)}),J("News",{action:"getCategories"}).then(l=>{if(!l)return;let a=Object.keys(l).sort((u,c)=>u.localeCompare(c)).reduce((u,c)=>(u[c]=l[c],u),{});mt.set(a)}),J("News",{action:"getProfile"}).then(l=>{l!=null&&l.profile&&b.set({...l.profile,articleCount:l.articleCount,draftCount:l.draftCount})})},[]),E.useEffect(()=>{Dd.set(n)},[n]),Qr("updatePermissions",l=>{if(l)if(cr.patch({Permissions:l}),l.create){if(b.value)return;J("News",{action:"getProfile"}).then(a=>{a!=null&&a.profile&&b.set({...a.profile,articleCount:a.articleCount,draftCount:a.draftCount})})}else n==="profile"&&nt.set("home"),b.reset()}),y(Tx,{children:e&&L("div",{className:"news-app",ref:i,style:{height:va?"100%":"100vh"},"data-theme":(s=(o=globalThis==null?void 0:globalThis.settings)==null?void 0:o.display)==null?void 0:s.theme,children:[y(gr.div,{...W2(H2(r,n),n),className:"news-body",children:Ag[n]}),y(vu,{children:t&&y(dx,{})}),y(hx,{})]})})},Xu=e=>e||`./avatar-placeholder-${globalThis!=null&&globalThis.settings?settings.display.theme:"light"}.svg`,Px=e=>{e&&(xn.value&&xn.reset(),Mn.set(e),nt.set("article"))},Tx=({children:e})=>va?y("div",{className:"frame-wrapper",children:y(Ex,{children:e})}):e;const Ld=!(window!=null&&window.invokeNative),kx=al.createRoot(document.getElementById("root"));if(window.name===""||Ld){const e=()=>{kx.render(y(Cx,{}))};Ld?e():window.addEventListener("message",t=>{t.data==="componentsLoaded"&&e()})}
