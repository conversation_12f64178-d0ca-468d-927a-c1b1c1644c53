import{r as u,a as c,d as f}from"./index-a04bc7c5.js";function d(e){const[n,i]=u.useState(e.value),[a,t]=u.useState(r(n,e.min,e.max));return c(f,{type:"range",className:"sliderInput",min:e.min,max:e.max,defaultValue:n,step:e.step,id:e.id,onChange:s=>{let l=parseFloat(s.target.value);i(l),e.onChange(l),t(r(l,e.min,e.max))},style:{background:`linear-gradient(to right, #0a84ff 0%, #0a84ff ${a}%, #636366 ${a}%, #636366 100%)`}})}function r(e,n,i,a){var t=(e-n)*100/(i-n);return t>100?a?(t=(a-e)*100/(a-i),t<0&&(t=0)):t=100:t<0&&(t=0),t}export{d as S};
