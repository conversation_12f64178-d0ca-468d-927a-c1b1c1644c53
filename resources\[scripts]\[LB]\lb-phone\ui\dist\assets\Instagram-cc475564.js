import{J as Q,u as T,r as m,cp as rt,s as E,aW as At,q as g,aX as Te,a0 as J,C as k,L as c,j as i,a as e,a6 as Se,bD as St,O as Pe,ca as D,cq as dt,c7 as mt,P as oe,d as ie,cr as qe,b1 as pt,F,m as pe,b as Ne,cs as Ye,ct as ut,a4 as X,a5 as re,cu as It,T as Et,n as Tt,ab as ke,N as Rt,G as me,bS as be,U as Ct,A as Fe,K as ue,cv as vt,t as te,y as ft,I as Ve,x as Be,V as _e,ak as Ee,an as Ot,ao as Lt,aF as Mt,S as wt,cw as Gt,v as bt,cx as kt,o as ct,aR as _t,aU as Dt,bN as yt,cf as Ut,bV as Ft,cg as Vt,ag as xt,cy as Ht,ae as Wt,aa as $t,b0 as qt}from"./index-a04bc7c5.js";import{S as Yt}from"./Switch-1ce279b8.js";import{T as ht}from"./Textarea-63971279.js";import{formatNumber as Bt}from"./Tiktok-826a5ded.js";import"./Slider-ce3bd25e.js";const ne=Q({host:"",participants:[],viewers:0});function zt(){var Oe,Le,Me,we;const s=T(Ne);T(L);const t=T($),n=T(ne),[l,v]=m.useState(!1),[u,a]=m.useState(!1),[f,o]=m.useState(null),[P,I]=m.useState(""),d=m.useRef(null),r=m.useRef(null),h=m.useRef(null),[N,O]=m.useState(null),[R,Y]=m.useState(null),[S,w]=m.useState(null),[b,p]=m.useState([]),[y,V]=m.useState([]),[U,H]=m.useState([]);m.useEffect(()=>{d.current.scrollTop=d.current.scrollHeight},[U]),m.useEffect(()=>{const A=s.rtc?new rt({config:s.rtc}):new rt;return A.on("open",C=>O({peer:A,id:C})),A.on("call",C=>{C.on("close",()=>{V(_=>_.filter(z=>z.connectionId!==C.connectionId))}),V(_=>[..._,C])}),E("info","Created peer"),()=>{try{for(let C of y)C.close()}catch{E("error","Failed to close calls")}try{A.destroy()}catch{E("error","Failed to destroy peer")}}},[]),m.useEffect(()=>{if(!S||h!=null&&h.current||!N)return;E("info","Creating game render");let A=new At(S);h.current=A,A.resizeByAspect(3/4);const C=window.innerWidth;C>1280&&A.setQuality(1280/C);let _;if(R||l){E("info","Host is true, creating call"),l?g("Instagram",{action:"setLive",username:t.username,verified:t.verified,id:N.id}):R&&g("Instagram",{action:"joinLive",username:R,streamId:N.id}).then(B=>{if(Y(null),!B)return E("error","Failed to join live");a(!0)});const z=S.captureStream(24);_=B=>{E("info","Answering call",B),B.answer(z)},N.peer.on("call",_)}return()=>{_&&N.peer.off("call",_),h.current&&h.current.destroy(),h.current=null}},[S,N]);const j=A=>{A.forEach(C=>{if(C.username===t.username)return E("info","Skipping self");E("info","Adding participant",C);const _=document.createElement("canvas"),z=_.captureStream(0),B=N.peer.call(C.id,z);V(Ce=>[...Ce,B]),B.on("close",_.remove),B.on("error",_.remove),B.on("stream",Ce=>{g("Instagram",{action:"addCall",id:B.connectionId,username:C.username}),p(De=>[...De,{stream:Ce,username:C.username}])})})},ae=A=>{A.forEach(C=>{E("info","Removing participant",C),C.username===t.username&&a(!1),p(_=>_.filter(z=>z.username!==C.username))})};m.useEffect(()=>{const A=n.participants;if(!A||!N)return E("warning","No participants");const C=A.filter(z=>!b.find(B=>B.username===z.username));C.length&&j(C),E("info","Added",C);const _=b.filter(z=>!A.find(B=>B.username===z.username));_.length&&ae(_),E("info","Removed",_)},[n.participants,N]),m.useEffect(()=>{const A=t.username===(n==null?void 0:n.host);return v(A),Te.ShouldClose.set(!1),A&&p(C=>[...C,{username:n.host}]),()=>{Te.ShouldClose.set(!0)}},[]);const Ie=()=>{var A;k.PopUp.set({title:l?c("APPS.INSTAGRAM.END_LIVE_POPUP.TITLE"):c("APPS.INSTAGRAM.LEAVE_LIVE_POPUP.TITLE"),description:c("APPS.INSTAGRAM.END_LIVE_POPUP.DESCRIPTION").format({action:(A=l?c("APPS.INSTAGRAM.END_LIVE_POPUP.END"):c("APPS.INSTAGRAM.END_LIVE_POPUP.LEAVE"))==null?void 0:A.toLowerCase()}),buttons:[{title:c("APPS.INSTAGRAM.END_LIVE_POPUP.CANCEL")},{title:l?c("APPS.INSTAGRAM.END_LIVE_POPUP.END"):c("APPS.INSTAGRAM.END_LIVE_POPUP.LEAVE"),color:"red",cb:()=>{se(l)}}]})},ge=()=>{g("Instagram",{action:"sendLiveMessage",data:{user:{username:t.username,avatar:t.avatar,verified:t.verified},live:n.host,content:P}}),I("")},se=(A,C)=>{if(l)g("Instagram",{action:"endLive"},!0).then(()=>L.set("feed"));else if(u&&g("Instagram",{action:"endLive"}).then(()=>L.set("feed")),g("Instagram",{action:"stopViewing",username:n.host}),A)Te.ShouldClose.set(!0),ne.patch({ended:!0});else{if(C===!1)return;L.set("feed")}};return J("instagram:addMessage",A=>{A.live===n.host&&H(C=>[...C,A])}),J("instagram:updateViewers",A=>{A.username===(n==null?void 0:n.host)&&ne.patch({viewers:A.viewers})}),J("instagram:setLive",A=>{ne.set(A)}),J("instagram:liveEnded",A=>{A===(n==null?void 0:n.host)&&se(!0)}),J("instagram:stopLive",()=>L.set("feed")),J("instagram:endCall",A=>{const C=y.find(_=>_.connectionId===A);C&&(C.close(),V(_=>_.filter(z=>z.connectionId!==A)))}),J("instagram:invitedLive",A=>{A===(n==null?void 0:n.host)&&k.PopUp.set({title:c("APPS.INSTAGRAM.INVITE_LIVE_POPUP.TITLE"),description:c("APPS.INSTAGRAM.INVITE_LIVE_POPUP.DESCRIPTION").format({username:A}),buttons:[{title:c("APPS.INSTAGRAM.INVITE_LIVE_POPUP.NO")},{title:c("APPS.INSTAGRAM.INVITE_LIVE_POPUP.YES"),cb:()=>{Y(A),p(C=>[...C,{username:t.username}])}}]})}),J("instagram:joinedLive",A=>{A.host===(n==null?void 0:n.host)&&ne.patch({participants:[...n.participants,A]})}),J("instagram:leftLive",A=>{A.host===(n==null?void 0:n.host)&&(E("info","left live",A),ne.patch({participants:n.participants.filter(C=>C.username!==A.participant)}))}),i("div",{className:"instagram-live-container",children:[e("div",{className:"instagram-streams","data-streams":b.length.toString(),children:b.map((A,C)=>A.username===t.username?i("div",{className:"instagram-stream",children:[e("canvas",{ref:_=>{E("info","setting canvas ref",_),w(_)}}),!l&&e(Se,{onClick:()=>{k.PopUp.set({title:c("APPS.INSTAGRAM.LEAVE_LIVE_POPUP.TITLE"),description:c("APPS.INSTAGRAM.LEAVE_LIVE_POPUP.DESCRIPTION"),buttons:[{title:c("APPS.INSTAGRAM.REMOVE_LIVE_POPUP.CANCEL")},{title:c("APPS.INSTAGRAM.LEAVE_LIVE_POPUP.LEAVE"),color:"red",cb:()=>{g("Instagram",{action:"endLive"}),a(!1)}}]})}})]}):e(Kt,{host:l,stream:A.stream,username:A.username},`stream-${C}`))}),n.ended&&i("div",{className:"instagram-stream-ended",children:[e(St,{onClick:()=>L.set("feed")}),e("div",{className:"title",children:c("APPS.INSTAGRAM.LIVE_ENDED")})]}),e(Pe,{children:f&&e(Jt,{username:n.host,type:f,host:f==="participants"?l||u:l,endLive:se,close:()=>o(null)})}),i("div",{className:"instagram-live",children:[i("div",{className:"live-header",children:[i("div",{className:"profile",onClick:()=>{b.length>1?o("participants"):!l&&!u&&(se(),x(n.host))},children:[e("div",{className:"profile-picture",children:e(D,{className:"avatar",avatar:(Le=(Oe=n==null?void 0:n.participants)==null?void 0:Oe[0])==null?void 0:Le.avatar})}),i("div",{className:"name",children:[e(jt,{usernames:n.participants.map(A=>A.username)}),((we=(Me=n==null?void 0:n.participants)==null?void 0:Me[0])==null?void 0:we.verified)&&e("span",{className:"verified",children:e("img",{src:"./assets/img/icons/instagram/Verified.svg"})}),e(dt,{})]})]}),i("div",{className:"stats",children:[e("div",{className:"live",children:c("APPS.INSTAGRAM.LIVE")}),i("div",{className:"viewers",onClick:()=>o("viewers"),children:[e(mt,{}),n.viewers]}),e(Se,{className:"x",onClick:()=>{l||u?Ie():se(!1)}})]})]}),i("div",{className:"comment-section",children:[e("div",{className:"comment-feed",ref:d,children:U.map((A,C)=>{const _=A.user.avatar??`./assets/img/avatar-placeholder-${oe.Settings.value.display.theme}.svg`;return i("div",{className:"comment",children:[e(D,{avatar:_}),i("div",{className:"info",children:[i("div",{className:"name",children:[A.user.username,A.user.verified&&e("span",{className:"verified",children:e("img",{src:"./assets/img/icons/instagram/Verified.svg"})})]}),e("div",{className:"comment-text",children:A.content})]})]},C)})}),i("div",{className:"comment-input",children:[e(ie,{type:"text",ref:r,placeholder:c("APPS.INSTAGRAM.COMMENT_PLACEHOLDER"),onChange:A=>I(A.target.value),onKeyDown:A=>{A.key==="Enter"&&P.replace(/ /g,"").length>0&&(ge(),r.current.value="")}}),e(qe,{onClick:()=>{P.replace(/ /g,"").length>0&&(ge(),r.current.value="")}}),(l||u)&&e(pt,{onClick:()=>g("Instagram",{action:"flipCamera"})})]})]})]})]})}const Kt=s=>{const t=m.useRef(null);return m.useEffect(()=>{if(!(t!=null&&t.current))return E("warning","No video ref");t.current.srcObject=s.stream,t.current.play().catch(()=>E("error","Failed to play video stream"))},[s==null?void 0:s.stream]),i("div",{className:"instagram-stream",children:[s.host&&e(Se,{onClick:()=>{k.PopUp.set({title:c("APPS.INSTAGRAM.REMOVE_LIVE_POPUP.TITLE").format({username:s.username}),description:c("APPS.INSTAGRAM.REMOVE_LIVE_POPUP.DESCRIPTION").format({username:s.username}),buttons:[{title:c("APPS.INSTAGRAM.REMOVE_LIVE_POPUP.CANCEL")},{title:c("APPS.INSTAGRAM.REMOVE_LIVE_POPUP.REMOVE"),color:"red",cb:()=>{g("Instagram",{action:"removeLive",username:s.username}).then(()=>{})}}]})}}),e("video",{autoPlay:!0,ref:t})]})},jt=s=>{let t=s.usernames.map((n,l)=>n).join(", ");return t.length>9&&(t=t.substring(0,9)+"..",t[t.length-3]===","&&(t=t.substring(0,t.length-3)+"..")),e(F,{children:t})},Jt=s=>{const[t,n]=m.useState([]);return m.useEffect(()=>{var l;if(s.type==="participants"){n((l=ne.value)==null?void 0:l.participants);return}g("Instagram",{action:"getLiveViewers",username:s.username}).then(v=>{v&&n(v)})},[]),i(pe.div,{className:"story-modal","data-type":"live",initial:{y:600},animate:{y:0},exit:{y:600},transition:{duration:.35,ease:"easeInOut"},children:[i("div",{className:"story-modal-header",children:[e("div",{className:"close-border",onClick:()=>s.close()}),e("div",{className:"title",children:s.type==="viewers"?c("APPS.INSTAGRAM.WHOS_WATCHING"):c("APPS.INSTAGRAM.IN_THIS_ROOM")})]}),e("div",{className:"story-modal-content",children:e("div",{className:"viewers",children:t.map((l,v)=>e(Xt,{type:s.type,user:l,host:s.host,endLive:s.endLive},v))})})]})},Xt=({user:s,host:t,type:n,endLive:l})=>{var f;const v=(f=T(ne))==null?void 0:f.participants,[u,a]=m.useState(!1);return i("div",{className:"item",children:[i("div",{className:"user",onClick:()=>{t||(l(!1,!1),x(s.username))},children:[e("div",{className:"profile-picture",children:e(D,{className:"avatar",avatar:s.avatar})}),i("div",{className:"user-details",children:[i("div",{className:"username",children:[s.username,s.verified&&e("span",{className:"verified",children:e("img",{src:"./assets/img/icons/instagram/Verified.svg"})})]}),e("div",{className:"name",children:s.name})]})]}),t&&n==="viewers"&&(v.find(o=>o.username===s.username)?e("div",{className:"remove",onClick:o=>{o.stopPropagation(),k.PopUp.set({title:c("APPS.INSTAGRAM.REMOVE_LIVE_POPUP.TITLE").format({username:s.username}),description:c("APPS.INSTAGRAM.REMOVE_LIVE_POPUP.DESCRIPTION").format({username:s.username}),buttons:[{title:c("APPS.INSTAGRAM.REMOVE_LIVE_POPUP.CANCEL")},{title:c("APPS.INSTAGRAM.REMOVE_LIVE_POPUP.REMOVE"),color:"red",cb:()=>{g("Instagram",{action:"removeLive",username:s.username}).then(()=>{})}}]})},children:c("APPS.INSTAGRAM.REMOVE")}):e("div",{className:"invite",onClick:o=>{o.stopPropagation(),!u&&g("Instagram",{action:"inviteLive",username:s.username},()=>{a(!0)})},children:u?c("APPS.INSTAGRAM.INVITED"):c("APPS.INSTAGRAM.INVITE")}))]})};function gt(s){const t=T(oe.Settings);return e(F,{children:s.liked?e(Ye,{onClick:s.onClick,color:"#ed4956"}):e(ut,{onClick:s.onClick,color:t.display.theme==="dark"?"#ffffff":"#262626"})})}function Pt(s){const t=T($),[n,l]=m.useState(s.data),[v,u]=m.useState(!1),a=m.useRef(null),[f,o]=m.useState(0),[P,I]=m.useState(!1),d=T(Ct),r=T(Fe),h={pos:{startLeft:0,startX:0},onMouseDown:S=>{me("Instagram")&&(h.pos={startLeft:a.current.scrollLeft,startX:S.clientX},a.current.style.userSelect="none",document.addEventListener("mouseup",h.onMouseUp),document.addEventListener("mousemove",h.onMove))},onMove:S=>{const w=(S.clientX-h.pos.startX)/be();a.current.scrollLeft=h.pos.startLeft-w;const b=a.current.getBoundingClientRect();(b.left*be()-5>S.clientX||S.clientX>b.right*be()-5)&&h.onMouseUp()},onMouseUp:()=>{a.current.style.removeProperty("user-select"),document.removeEventListener("mouseup",h.onMouseUp),document.removeEventListener("mousemove",h.onMove);const S=JSON.parse(n.media),w=a.current.clientWidth;let b=f;const p=a.current.scrollLeft-h.pos.startLeft;p>w/2&&b<S.length-1?b++:p<-w/2&&b>0&&b--,N(b)}},N=S=>{a.current.scrollTo({left:S*a.current.offsetWidth,behavior:"smooth"}),o(S)};J("instagram:updatePostData",S=>{if(!S||S.postId!==n.id)return E("warning","instagram:updatePostData","Post ID's do not match");l(w=>({...w,[S.data]:S.increment?w[S.data]+1:w[S.data]-1}))});const O=S=>{if(!v)return u(!0);if(S.detail===2){if(!n.id)return;n.liked||g("Instagram",{action:"toggleLike",data:{postId:n.id,toggle:!n.liked,isComment:!1}},!n.liked).then(w=>{l(b=>({...b,liked:w})),I(!0),setTimeout(()=>I(!1),1e3)})}},R=()=>{k.ContextMenu.set({buttons:[{title:c("APPS.INSTAGRAM.SHARE_TO_STORY.TITLE"),cb:()=>{var S;g("Instagram",{action:"addToStory",media:(S=JSON.parse(n.media))==null?void 0:S[f],metadata:{post:{id:n.id,username:n.username}}},Math.random().toString(36).substring(7)).then(w=>{if(!w)return E("error","Failed to add story");le.set([{username:t.username,avatar:t.avatar,verified:t.verified,seen:0,id:w},...le.value.filter(b=>b.username!==t.username)]),k.PopUp.set({title:c("APPS.INSTAGRAM.SHARE_TO_STORY.SUCCESS"),description:c("APPS.INSTAGRAM.SHARE_TO_STORY.DESCRIPTION"),buttons:[{title:c("APPS.INSTAGRAM.OK")}]})})}}]})},Y=()=>{x(n.username),n!=null&&n.viewPost&&fe.reset()};return i("div",{className:"post",id:s.last?"last":"","data-id":n.id,"data-active":s.active,children:[i("div",{className:"post-header",children:[e(D,{avatar:n.avatar,className:"profile-picture",onClick:Y}),i("div",{className:"post-info",children:[i("div",{className:"name",onClick:Y,children:[n.username,n.verified&&e("span",{className:"verified",children:e("img",{src:"./assets/img/icons/instagram/Verified.svg"})})]}),n.location&&e("div",{className:"location",children:n.location})]})]}),i("div",{className:"post-body",children:[P&&e("div",{className:"popup-heart","data-visible":P,children:e(Ye,{})}),e("div",{className:"image-container",ref:a,onMouseDown:h.onMouseDown,children:JSON.parse(n.media).map((S,w)=>{var b;return e("div",{className:"image",onClick:O,children:X(S)?e(Qt,{attachment:S,active:s.active&&!(d!=null&&d.visible)&&((b=r==null?void 0:r.active)==null?void 0:b.name)==="Instagram"}):e(re,{src:S,blur:!v&&(n==null?void 0:n.username)!==(t==null?void 0:t.username)})},w)})}),i("div",{className:"actions",children:[i("div",{className:"like-comment",children:[e(gt,{liked:n.liked,onClick:()=>{n.id&&g("Instagram",{action:"toggleLike",data:{postId:n.id,toggle:!n.liked,isComment:!1}},!n.liked).then(S=>{l(w=>({...w,liked:S}))})}}),e(It,{onClick:()=>$e({id:n.id,username:n.username,avatar:n.avatar,verified:n.verified,caption:n.caption,timestamp:n.timestamp})}),(n.username===t.username||t.isAdmin)&&e(Et,{onClick:()=>{k.PopUp.set({title:c("APPS.INSTAGRAM.DELETE_POST_POPUP.TITLE"),description:c("APPS.INSTAGRAM.DELETE_POST_POPUP.DESCRIPTION"),buttons:[{title:c("APPS.INSTAGRAM.DELETE_POST_POPUP.CANCEL")},{title:c("APPS.INSTAGRAM.DELETE_POST_POPUP.PROCEED"),color:"red",cb:()=>{g("Instagram",{action:"deletePost",id:n.id},!0).then(S=>{if(!S)return E("error","Instagram","Failed to delete post");L.set("feed")})}}]})}})]}),JSON.parse(n.media).length>1&&e("div",{className:"scroll-dots",children:JSON.parse(n.media).map((S,w)=>e("div",{className:`dot ${f==w?"active":""}`,onClick:()=>N(w)},w))}),e("div",{className:"like-comment",children:e(Tt,{onClick:R})})]}),i("div",{className:"liked-by",onClick:()=>{he.set({title:c("APPS.INSTAGRAM.LIKED_BY"),postId:n.id})},children:[n.like_count," ",c("APPS.INSTAGRAM.LIKES")]}),i("div",{className:"caption",children:[e("span",{className:"user",onClick:S=>{S.stopPropagation(),x(n.username)},children:n.username}),ke(n.caption)]}),e("div",{className:"comments",onClick:S=>{S.stopPropagation(),$e({id:n.id,username:n.username,avatar:n.avatar,verified:n.verified,caption:n.caption,timestamp:n.timestamp})},children:n.comment_count>0?c("APPS.INSTAGRAM.VIEW_ALL_COMMENTS").format({count:n.comment_count}):c("APPS.INSTAGRAM.FIRST_COMMENT")}),e("div",{className:"date",children:Rt(n.timestamp)})]})]})}const Qt=s=>{var u;const[t,n]=m.useState(s.active),l=T(oe.Settings),v=m.useRef(null);return m.useEffect(()=>{var a,f,o,P;n(s.active),!(!v.current||((a=l==null?void 0:l.sound)==null?void 0:a.volume)===void 0)&&s.active&&(v.current.volume=((f=l==null?void 0:l.sound)==null?void 0:f.volume)!==void 0?l.sound.volume:.5,v.current.currentTime=0,s.active?(o=v.current)==null||o.play().catch(I=>{}):(P=v.current)==null||P.pause().catch(I=>{}))},[s.active]),m.useEffect(()=>{var a,f;!v.current||((a=l==null?void 0:l.sound)==null?void 0:a.volume)===void 0||s.active&&(v.current.volume=((f=l==null?void 0:l.sound)==null?void 0:f.volume)!==void 0?l.sound.volume:.5)},[(u=l==null?void 0:l.sound)==null?void 0:u.volume]),e("video",{ref:v,src:s.attachment,autoPlay:!0,loop:!0,muted:!s.active,onLoadedMetadata:a=>{var f;s.active&&(a.currentTarget.volume=((f=l==null?void 0:l.sound)==null?void 0:f.volume)!==void 0?l.sound.volume:.5)}})},q={account:{username:"lb",name:"LB",avatar:"https://docs.lbscripts.com/images/icons/icon.png",bio:`Official LB account
https://lbphone.com`,verified:!0},accounts:{lb:{username:"lb",name:"LB",avatar:"https://docs.lbscripts.com/images/icons/icon.png",bio:`Official LB account
https://lbphone.com`,verified:!0,following:2,followers:13,posts:2,followingList:["loaf","breze"],followerList:["loaf","breze"]},breze:{username:"breze",name:"Breze",bio:`Hey, its breze 
https://lbphone.com`,verified:!0,following:2,followers:1,posts:2,followingList:["lb","loaf"],followerList:["lb","loaf"]},loaf:{username:"loaf",name:"Loaf Scripts",bio:`Loaf Scritps official account 
https://lbphone.com`,verified:!0,following:2,followers:1,posts:2,followingList:["lb","breze"],followerList:["lb","breze"]}},posts:[{id:"1",username:"breze",name:"Breze",verified:!0,caption:"sunsets are the best",media:'["https://r2.fivemanage.com/images/GRBUI6C4orbD.webp", "https://r2.fivemanage.com/images/rafgjPwSYnn2.png"]',location:"Sandy Shores",like_count:2,comment_count:0,timestamp:Date.now()-1e3*60*60*24*1},{id:"2",username:"lb",name:"LB",verified:!0,caption:"dump",media:'["https://r2.fivemanage.com/images/upTPndukc2PD.webp", "https://r2.fivemanage.com/images/X4pfDFiSFvhi.webp"]',location:"Los Santos",like_count:8,comment_count:2,timestamp:Date.now()-1e3*60*60*24*2}],stories:[{id:"1",username:"lb",avatar:"https://docs.lbscripts.com/images/icons/icon.png",verified:!0,seen:0,media:[{id:"2",image:"https://r2.fivemanage.com/images/oRKcdeTA8oBy.png",timestamp:Date.now()-1e3*60*60*4,seen:!1,views:2,viewers:[{username:"Breze"},{username:"Loaf"}]}]},{id:"2",username:"breze",verified:!0,seen:0,media:[{id:"1",metadata:{post:{id:132,src:"https://townsquare.media/site/812/files/2024/03/attachment-future-metro-boomin-we-dont-trust-you-photo.jpg?w=1080&q=75",username:"tordinationen"}},timestamp:Date.now()-1e3*60*60*2,seen:!1},{id:"2",image:"https://r2.fivemanage.com/images/ZsmnyJOA2rc5.png",timestamp:Date.now()-1e3*60*60*3,seen:!1},{id:"3",image:"https://r2.fivemanage.com/video/REZeHruJivNL.mp4",timestamp:Date.now()-1e3*60*60*4,seen:!1}]},{id:"3",username:"loaf",verified:!0,seen:1,media:[{id:"1",image:"https://r2.fivemanage.com/images/X4pfDFiSFvhi.webp",timestamp:Date.now()-1e3*60*60*3,seen:!0},{id:"2",image:"https://r2.fivemanage.com/images/upTPndukc2PD.webp",timestamp:Date.now()-1e3*60*60*4,seen:!0}]}],comments:[{postId:"2",user:{username:"breze",verified:!0},comment:{id:"1",content:"nice",timestamp:Date.now()-1e3*60*60*24,likes:0}},{postId:"2",user:{username:"loaf",verified:!0},comment:{id:"2",content:"haha, nice picture",timestamp:Date.now()-1e3*60*60*22,likes:1,liked:!0}}],messages:[{id:"1",username:"breze",name:"Breze",verified:!0,content:"hey",messages:[{id:"1",sender:"breze",content:"hey",timestamp:Date.now()-1e3*60*60*4}],timestamp:Date.now()-1e3*60*60*4},{id:"2",username:"loaf",name:"Loaf Scripts",verified:!1,content:"yoo, your last post was fire",messages:[{id:"3",sender:"loaf",content:"yoo, your last post was fire",timestamp:Date.now()-1e3*60*60*8}],timestamp:Date.now()-1e3*60*60*8}],notifications:[{type:"like_photo",username:"loaf",name:"Loaf Scripts",postId:"1",photo:"https://r2.fivemanage.com/images/upTPndukc2PD.webp",timestamp:Date.now()-1e3*60*60*2},{type:"follow",username:"loaf",name:"Loaf Scripts",isFollowing:!0,timestamp:Date.now()-1e3*60*60*24*3}]};function Zt(){const s=T(L),t=T($),n=T(le),[l,v]=m.useState([]),[u,a]=m.useState([]),[f,o]=m.useState(null);m.useEffect(()=>{me("Instagram")&&g("Instagram",{action:"getPosts",filters:{following:!0},page:0},q.posts).then(r=>{if(!r)return E("error","Failed to fetch posts");v(r)})},[]),m.useEffect(()=>{me("Instagram")&&s==="feed"&&(g("Instagram",{action:"getLives"}).then(r=>{r&&a(r)}),g("Instagram",{action:"getStories"},q.stories).then(r=>{if(!r)return E("warning","Failed to fetch stories");le.set([...r.sort((h,N)=>{if(h.seen===N.seen)return 0;if(h.seen===1)return 1;if(N.seen===1)return-1})])}))},[s]),new IntersectionObserver(P,{rootMargin:"20px",threshold:.5});function P(r){r.forEach(h=>{if(h.isIntersecting){let O=h.target.getAttribute("data-id");o(O)}})}const{handleScroll:I}=ue({fetchData:r=>g("Instagram",{action:"getPosts",filters:{following:!0},page:r}),onDataFetched:r=>v([...l,...r]),perPage:25});m.useEffect(()=>{const r=document.querySelector(".stories");return r==null||r.addEventListener("wheel",h=>{h.preventDefault(),r.scrollLeft+=h.deltaY}),()=>{r==null||r.removeEventListener("wheel",h=>{h.preventDefault(),r.scrollLeft+=h.deltaY})}},[]),J("instagram:updateLives",r=>{r&&a(r)});const d=()=>{k.PopUp.set({title:c("APPS.INSTAGRAM.GO_LIVE_POPUP.TITLE"),description:c("APPS.INSTAGRAM.GO_LIVE_POPUP.DESCRIPTION"),buttons:[{title:c("APPS.INSTAGRAM.GO_LIVE_POPUP.CANCEL")},{title:c("APPS.INSTAGRAM.GO_LIVE_POPUP.PROCEED"),color:"red",cb:()=>{g("Instagram",{action:"goLive",user:{username:t.username,avatar:t==null?void 0:t.avatar,verified:t.verified}},!0).then(r=>{if(!r)return E("error","Failed to go live");ne.set({host:t.username,participants:[{username:t.username,name:t.username,avatar:t==null?void 0:t.avatar,verified:t.verified}],viewers:0}),L.set("live")})}}]})};return i(F,{children:[i("div",{className:"instagram-header feed-header",children:[i("div",{className:"name",children:[t==null?void 0:t.username,(t==null?void 0:t.verified)&&e("span",{className:"verified",children:e("img",{src:"./assets/img/icons/instagram/Verified.svg"})})]}),e(qe,{onClick:()=>L.set("dmList")})]}),i("div",{className:"feed",onScroll:I,children:[e("div",{className:"stories-wrapper",children:i("div",{className:"stories",children:[i("div",{className:"story self",onClick:()=>{k.ContextMenu.set({buttons:[{title:c("APPS.INSTAGRAM.GO_LIVE"),cb:()=>d()},{title:c("APPS.INSTAGRAM.CREATE_STORY"),cb:()=>{var r,h,N;k.Gallery.set({includeVideos:!0,allowExternal:(N=(h=(r=Ne)==null?void 0:r.value)==null?void 0:h.AllowExternal)==null?void 0:N.InstaPic,onSelect:O=>{let R=Array.isArray(O)?O[0].src:O.src;g("Instagram",{action:"addToStory",media:R},Math.random().toString(36).substring(7)).then(Y=>{if(!Y)return E("error","Failed to add story");le.set([{username:t.username,avatar:t.avatar,verified:t.verified,seen:0,id:Y},...n.filter(S=>S.username!==t.username)])})}})}}]})},children:[i("div",{className:"profile-picture",children:[e(D,{className:"avatar",avatar:t==null?void 0:t.avatar}),e("div",{className:"plus",children:e("i",{className:"far fa-plus"})})]}),e("div",{className:"name",children:c("APPS.INSTAGRAM.YOUR_STORY")})]}),Object.keys(u).map(r=>{var h;return i("div",{className:"story",onClick:()=>{var N;g("Instagram",{action:"viewLive",username:((N=u[r])==null?void 0:N.participant)??r}).then(O=>{var S,w,b,p;if(!O)return E("error","Failed to view live, perhaps it ended?");let R=(S=u[r])!=null&&S.participant?u[(w=u[r])==null?void 0:w.participant]:u[r],Y=(R==null?void 0:R.participants)??[];Y.unshift({username:((b=u[r])==null?void 0:b.participant)??r,name:R.name,avatar:R.avatar,verified:R.verified,id:R.id}),ne.set({host:((p=u[r])==null?void 0:p.participant)??r,participants:Y,viewers:O}),L.set("live")})},children:[i("div",{className:"profile-picture",children:[e(D,{className:"avatar",avatar:(h=u[r])==null?void 0:h.avatar}),e("img",{className:"circle live",src:"./assets/img/icons/instagram/LiveCircle.svg"})]}),e("div",{className:"name",children:r})]},r)}),n==null?void 0:n.map(r=>i("div",{className:"story",onClick:h=>{let[N,O]=vt(h);Ue(r,null,{x:N,y:O})},children:[i("div",{className:"profile-picture",children:[e(D,{className:"avatar",avatar:r.avatar}),e("img",{className:"circle","data-seen":r.seen===1,src:"./assets/img/icons/instagram/StoryCircle.svg"})]}),e("div",{className:"name",children:r.username})]},r.username))]})}),(l==null?void 0:l.length)===0&&e("div",{className:"no-posts",children:c("APPS.INSTAGRAM.EMPTY_FEED")}),l==null?void 0:l.map(r=>e(Pt,{data:r,active:f===r.id},r.id))]})]})}function lt(){const s=T(oe.Settings),[t,n]=m.useState(""),[l,v]=m.useState(""),[u,a]=m.useState(""),[f,o]=m.useState("login"),P=()=>{g("Instagram",{action:"logIn",username:l,password:u},{success:!!q.accounts[l],account:q.accounts[l]}).then(d=>{if(d.success&&d.account)g("isAdmin",null,!1).then(r=>{te.APPS.INSTAGRAM.account.set(d.account),$.set({...d.account,isAdmin:r}),L.set("feed"),de.set(!0)});else{if(d.error)return k.PopUp.set({title:c("APPS.INSTAGRAM.ERROR"),description:c(`APPS.INSTAGRAM.${d.error}`),buttons:[{title:c("APPS.INSTAGRAM.OK")}]});E("error","Instagram LogIn Error",d.error)}})},I=()=>{let d=l==null?void 0:l.toLowerCase().replace(/\s/g,""),r=d.match(Ne.value.UsernameFilter);if(!r||(r==null?void 0:r[0])!==d||d.length<3||d.length>15){E("warning","Username did not match regex"),k.PopUp.set({title:c("APPS.INSTAGRAM.ERROR"),description:c("APPS.INSTAGRAM.USERNAME_NOT_ALLOWED"),buttons:[{title:c("APPS.INSTAGRAM.OK")}]});return}g("Instagram",{action:"createAccount",name:t,username:d,password:u},{success:!q.accounts[d]}).then(h=>{if(h.success)g("isAdmin").then(N=>{let O={name:t,username:d,posts:0,followers:0,following:0};te.APPS.INSTAGRAM.account.set(O),$.set({...O,isAdmin:N}),L.set("feed"),de.set(!0)});else{if(h.error)return k.PopUp.set({title:c("APPS.INSTAGRAM.ERROR"),description:c(`APPS.INSTAGRAM.${h.error}`),buttons:[{title:c("APPS.INSTAGRAM.OK")}]});E("error","couldn't create account",h)}})};return m.useEffect(()=>{n(""),v(""),a("")},[f]),e("div",{className:"log-in",children:i(F,{children:[e("div",{className:"wrapper",children:f==="login"?i(F,{children:[e("img",{src:"./assets/img/icons/instagram/logo.png","data-theme":s.display.theme}),i("div",{className:"form",children:[e(ie,{placeholder:c("APPS.INSTAGRAM.USERNAME_PLACEHOLDER"),type:"text",className:"username",onChange:d=>v(d.target.value.replace(/\s/g,"")),maxLength:15},1),e(ie,{placeholder:c("APPS.INSTAGRAM.PASSWORD_PLACEHOLDER"),type:"password",onChange:d=>a(d.target.value),maxLength:50},3),e("div",{className:"button",onClick:P,children:c("APPS.INSTAGRAM.LOG_IN")})]})]}):i(F,{children:[e("img",{src:"./assets/img/icons/instagram/logo.png","data-theme":s.display.theme}),i("div",{className:"form",children:[e(ie,{placeholder:c("APPS.INSTAGRAM.NAME_PLACEHOLDER"),type:"text",onChange:d=>n(d.target.value),maxLength:20},2),e(ie,{placeholder:c("APPS.INSTAGRAM.USERNAME_PLACEHOLDER"),type:"text",className:"username",onChange:d=>v(d.target.value.replace(/\s/g,"")),maxLength:15},4),e(ie,{placeholder:c("APPS.INSTAGRAM.PASSWORD_PLACEHOLDER"),type:"password",onChange:d=>a(d.target.value),maxLength:50}),e("div",{className:"button",onClick:I,children:c("APPS.INSTAGRAM.CREATE_ACCOUNT")})]})]})}),e("div",{className:"footer",children:f==="login"?i(F,{children:[c("APPS.INSTAGRAM.NOT_HAVE_ACCOUNT")," ",e("span",{onClick:()=>o("createAccount"),children:c("APPS.INSTAGRAM.CREATE_ACCOUNT")})]}):i(F,{children:[c("APPS.INSTAGRAM.HAVE_ACCOUNT")," ",e("span",{onClick:()=>o("login"),children:c("APPS.INSTAGRAM.LOG_IN")})]})})]})})}const He=Q(!1);function ea(){var u,a,f,o,P;const s=T(Fe),[t,n]=m.useState(null),l=T(He);m.useEffect(()=>{me("Instagram")&&g("Instagram",{action:"getNotifications"},q.notifications).then(I=>{if(!I)return E("warning","Failed to fetch notifications, or no results?");n(I)})},[s==null?void 0:s.active]);const{handleScroll:v}=ue({fetchData:I=>g("Instagram",{action:"getNotifications",page:I}),onDataFetched:I=>{I.length>0&&n(d=>({notifications:[...d.notifications,...I[0].notifications]}))},perPage:25});return i("div",{className:"notifications-container",children:[e(Pe,{children:l&&e(ta,{})}),e("div",{className:"notifications-header",children:c("APPS.INSTAGRAM.ACTIVITY")}),i("div",{className:"notifications-body",onScroll:v,children:[((u=t==null?void 0:t.requests)==null?void 0:u.total)>0&&i("div",{className:"follow-requests",onClick:()=>He.set(!0),children:[i("div",{className:"item-data",children:[e("div",{className:"profile-pictures","data-multiple":t.requests.total>1,children:t.requests.recent.map((I,d)=>e(D,{avatar:I.avatar},d))}),i("div",{className:"item-body",children:[e("div",{className:"title",children:c("APPS.INSTAGRAM.FOLLOW_REQUESTS")}),e("div",{className:"subtitle",children:t.requests.total>1?c("APPS.INSTAGRAM.ANDOTHERS").format({username:(a=t.requests.recent[0])==null?void 0:a.username,amount:((f=t.requests)==null?void 0:f.total)-1}):(o=t.requests.recent[0])==null?void 0:o.username})]})]}),i("div",{className:"actions",children:[e("div",{className:"blue-dot"}),e(ft,{})]})]}),(P=t==null?void 0:t.notifications)==null?void 0:P.map((I,d)=>{let r=d===t.notifications.length-1?"last":"";return e(aa,{notification:I,last:r},d)})]})]})}const ta=()=>{const[s,t]=m.useState(""),[n,l]=m.useState([]),v=[{class:"follow",label:c("APPS.INSTAGRAM.CONFIRM"),action:!0},{class:"following",label:c("APPS.INSTAGRAM.DELETE"),action:!1}];m.useEffect(()=>{g("Instagram",{action:"getFollowRequests",page:0},[]).then(a=>{if(!a)return E("warning","Failed to fetch follow requests");E("info","Fetched follow requests",a),l(a)})},[]);const{handleScroll:u}=ue({fetchData:a=>g("Instagram",{action:"getFollowRequests",page:a}),onDataFetched:a=>l(a),perPage:15});return i(pe.div,{initial:{opacity:.5,x:150},animate:{opacity:1,x:0},exit:{opacity:.5,x:150},transition:{duration:.2,ease:"easeInOut"},className:"requests-container",children:[i("div",{className:"requests-header",children:[e("div",{className:"icon",children:e(Ve,{onClick:()=>He.set(!1)})}),e("div",{className:"title",children:c("APPS.INSTAGRAM.FOLLOW_REQUESTS")}),e("span",{})]}),i("div",{className:"requests-body",children:[e(Be,{placeholder:c("SEARCH"),onChange:a=>t(a.target.value)}),e("div",{className:"items",onScroll:u,children:n.filter(a=>{var f,o;return((f=a.username)==null?void 0:f.toLowerCase().includes(s==null?void 0:s.toLowerCase()))||((o=a.name)==null?void 0:o.toLowerCase().includes(s==null?void 0:s.toLowerCase()))}).map((a,f)=>i("div",{className:"item",children:[i("div",{className:"user",onClick:()=>x(a.username),children:[e(D,{className:"avatar",avatar:a.avatar}),i("div",{className:"user-info",children:[i("div",{className:"username",children:[a.username,a.verified&&e("span",{className:"verified",children:e("img",{src:"./assets/img/icons/instagram/Verified.svg"})})]}),e("div",{className:"name",children:a.name})]})]}),e("div",{className:"buttons",children:v.map((o,P)=>e("div",{className:_e("button",o.class),onClick:I=>{I.stopPropagation(),g("Instagram",{action:"handleFollowRequest",username:a.username,accept:o.action},!0).then(d=>{if(!d)return E("warning","Failed to handle follow request");l(r=>r.filter(h=>h.username!==a.username))})},children:o.label},P))})]},f))})]})]})},aa=({notification:s,last:t})=>{const n=T($),[l,v]=m.useState(s.isFollowing);switch(s.type){case"like_photo":return i("div",{className:"notification-item",id:t,onClick:()=>ye(s.postId),children:[i("div",{className:"notification-body",children:[e(D,{className:"avatar",avatar:s.avatar,onClick:a=>{a.stopPropagation(),x(s.username)}}),i("div",{className:"content",children:[e("span",{children:s.username})," ",c("APPS.INSTAGRAM.LIKED_PHOTO"),e("span",{className:"date",children:Ee(s.timestamp)})]})]}),X(s.photo)?e("video",{className:"post-preview",src:s.photo,muted:!0,controls:!1}):e(re,{className:"post-preview",src:s.photo})]});case"like_comment":return e("div",{className:"notification-item",id:t,onClick:()=>ye(s.postId,!0),children:i("div",{className:"notification-body",children:[e(D,{className:"avatar",avatar:s.avatar,onClick:a=>{a.stopPropagation(),x(s.username)}}),i("div",{className:"content",children:[e("span",{children:s.username})," ",c("APPS.INSTAGRAM.LIKED_COMMENT").format({comment:s.comment}),e("span",{className:"date",children:Ee(s.timestamp)})]})]})});case"comment":return i("div",{className:"notification-item",id:t,onClick:()=>ye(s.postId,!0),children:[i("div",{className:"notification-body",children:[e(D,{className:"avatar",avatar:s.avatar,onClick:a=>{a.stopPropagation(),x(s.username)}}),i("div",{className:"content",children:[e("span",{children:s.username})," ",c("APPS.INSTAGRAM.COMMENTED").format({comment:s.comment}),e("span",{className:"date",children:Ee(s.timestamp)})]})]}),X(s.photo)?e("video",{className:"post-preview",src:s.photo,muted:!0,controls:!1}):e("img",{className:"post-preview",src:s.photo})]});case"follow":return i("div",{className:"notification-item",id:t,onClick:()=>x(s.username),children:[i("div",{className:"notification-body",children:[e(D,{className:"avatar",avatar:s.avatar,onClick:a=>{a.stopPropagation(),x(s.username)}}),i("div",{className:"content",children:[e("span",{children:s.username})," ",c("APPS.INSTAGRAM.FOLLOWED"),e("span",{className:"date",children:Ee(s.timestamp)})]})]}),e("div",{className:_e("button",l?"following":"follow"),onClick:a=>{a.stopPropagation(),n.username!==s.username&&(l?k.PopUp.set({title:c("APPS.INSTAGRAM.UNFOLLOW_POPUP.TITLE"),description:c("APPS.INSTAGRAM.UNFOLLOW_POPUP.DESCRIPTION").format({name:s.username}),buttons:[{title:c("APPS.INSTAGRAM.UNFOLLOW_POPUP.CANCEL")},{title:c("APPS.INSTAGRAM.UNFOLLOW_POPUP.PROCEED"),color:"red",cb:()=>{g("Instagram",{action:"toggleFollow",data:{username:s.username,following:!l}},!0).then(()=>{v(f=>!f)})}}]}):g("Instagram",{action:"toggleFollow",data:{username:s.username,following:!l}},!0).then(()=>{v(f=>!f)}))},children:l?c("APPS.INSTAGRAM.FOLLOWING"):c("APPS.INSTAGRAM.FOLLOW")})]})}};function sa(){var O,R,Y,S,w,b;const s=T($),t=T(ce);T(L);const n=T(Z),l=m.useRef(null),v=m.useRef(null),[u,a]=m.useState(!1),[f,o]=m.useState(!1),[P,I]=m.useState(null);m.useEffect(()=>{me("Instagram")&&t.username&&(a(s.username===t.username),g("Instagram",{action:"getPosts",filters:{profile:!0,username:t.username},page:0}).then(p=>{p&&I(p)}))},[t==null?void 0:t.username]);const{handleScroll:d}=ue({fetchData:p=>g("Instagram",{action:"getPosts",filters:{profile:!0,username:t.username},page:p}),onDataFetched:p=>I([...P,...p]),perPage:15}),r=()=>{if(!t.name)return E("warning","Name is required, not saving");g("Instagram",{action:"updateProfile",data:{name:t.name,bio:t.bio,avatar:t.avatar,private:t.private}},!0).then(p=>{if(!p)return E("warning","Failed to update profile");te.APPS.INSTAGRAM.account.set({...s,name:t.name,bio:t.bio,avatar:t.avatar,private:t.private}),o(!1),E("info","Successfully updated profile")})};J("instagram:updateProfileData",p=>{p.username===t.username&&ce.set({...t,[p.data]:p.increment?t[p.data]+1:t[p.data]-1})});const h=m.useRef(!0);m.useEffect(()=>{if(h.current){h.current=!1;return}g("Instagram",{action:"toggleFollow",data:{username:t.username,following:t.isFollowing}},!0)},[t.isFollowing]);const N=()=>{g("AccountSwitcher",{action:"getAccounts",app:"Instagram"}).then(p=>{var U;if(!p)return E("info","No accounts found");let y=s.isAdmin,V=(U=p==null?void 0:p.filter(H=>H!==t.username))==null?void 0:U.map(H=>({title:H,cb:()=>{g("AccountSwitcher",{action:"switch",app:"Instagram",account:H}).then(j=>{g("Instagram",{action:"isLoggedIn"}).then(ae=>{ae?(te.APPS.INSTAGRAM.account.set(ae),$.set({...ae,isAdmin:y}),L.set("feed"),de.set(!0)):te.APPS.INSTAGRAM.account.set(null)})})}}));k.ContextMenu.set({buttons:[{title:s.username},...V,{title:c("APPS.INSTAGRAM.ADD_ACCOUNT"),cb:()=>de.set(!1)}]})})};return m.useEffect(()=>{if(!(l!=null&&l.current)||!(v!=null&&v.current))return;const p=33,y=29.5,U=(l.current.offsetHeight-p)/16,H=Math.max(y-U,0);v.current.style.maxHeight=`${H}rem`},[v==null?void 0:v.current,t.bio]),e(F,{children:t&&i("div",{className:"profile",children:[e(Pe,{children:f&&i(pe.div,{className:"edit-profile",initial:{y:750,opacity:.75},animate:{y:0,opacity:1},exit:{y:750,opacity:.75},transition:{duration:.3,ease:"easeInOut"},children:[i("div",{className:"edit-profile-header",children:[e("div",{className:"cancel",onClick:()=>o(!1),children:c("CANCEL")}),e("div",{className:"title",children:c("APPS.INSTAGRAM.EDIT_PROFILE")}),e("div",{className:"save",onClick:()=>r(),children:c("APPS.INSTAGRAM.DONE")})]}),i("div",{className:"edit-profile-body",children:[i("div",{className:"profile-picture",onClick:()=>{var p,y,V;k.Gallery.set({allowExternal:(V=(y=(p=Ne)==null?void 0:p.value)==null?void 0:y.AllowExternal)==null?void 0:V.InstaPic,onSelect:U=>{let H=Array.isArray(U)?U[0].src:U.src;ce.set({...t,avatar:H}),$.set({...s,avatar:H})}})},children:[e(D,{avatar:t.avatar}),e("span",{children:c("APPS.INSTAGRAM.CHANGE_PICTURE")})]}),i("div",{className:"profile-items",children:[i("div",{className:"item",children:[e("div",{className:"title",children:c("APPS.INSTAGRAM.NAME")}),e(ie,{placeholder:c("APPS.INSTAGRAM.NAME_PLACEHOLDER"),onChange:p=>{ce.set({...t,name:p.target.value})},maxLength:30,defaultValue:t.name})]}),i("div",{className:"item",children:[e("div",{className:"title",children:c("APPS.INSTAGRAM.BIO")}),e(ht,{placeholder:c("APPS.INSTAGRAM.BIO"),onChange:p=>{ce.set({...t,bio:p.target.value})},maxLength:100,defaultValue:t.bio??""})]}),i("div",{className:"item",children:[e("div",{className:"title",children:c("APPS.INSTAGRAM.PRIVATE")}),e("div",{className:"switch-wrapper",children:e(Yt,{checked:t.private,onChange:()=>ce.set({...t,private:!t.private})})})]})]}),i("div",{className:"buttons",children:[e("div",{className:"button blue",onClick:()=>{k.PopUp.set({title:c("APPS.INSTAGRAM.SIGN_OUT_POPUP.TITLE"),description:c("APPS.INSTAGRAM.SIGN_OUT_POPUP.DESCRIPTION"),buttons:[{title:c("APPS.INSTAGRAM.SIGN_OUT_POPUP.CANCEL")},{title:c("APPS.INSTAGRAM.SIGN_OUT_POPUP.PROCEED"),color:"red",cb:()=>{g("Instagram",{action:"signOut"},!0).then(p=>{if(!p)return E("error","Failed to sign out");de.set(null),$.set(null),te.APPS.INSTAGRAM.account.set(null)})}}]})},children:c("APPS.INSTAGRAM.SIGN_OUT")}),((Y=(R=(O=Ne)==null?void 0:O.value)==null?void 0:R.ChangePassword)==null?void 0:Y.InstaPic)&&e("div",{className:"button red",onClick:()=>Ot("Instagram",()=>{}),children:c("APPS.INSTAGRAM.CHANGE_PASSWORD")}),((b=(w=(S=Ne)==null?void 0:S.value)==null?void 0:w.DeleteAccount)==null?void 0:b.InstaPic)&&e("div",{className:"button red",onClick:()=>{Lt("Instagram",()=>{de.set(null),$.set(null),te.APPS.INSTAGRAM.account.set(null)})},children:c("APPS.INSTAGRAM.DELETE_ACCOUNT")})]})]})]})}),u?e("div",{className:"instagram-header",children:i("div",{className:"name",style:{cursor:"pointer"},onClick:()=>N(),children:[t.username??"Unknown",t.verified&&e("span",{className:"verified",children:e("img",{src:"./assets/img/icons/instagram/Verified.svg"})}),e(dt,{})]})}):i("div",{className:"profile-header",children:[e("i",{className:"fas fa-chevron-left",onClick:()=>{n!=null&&n.cb?n.cb():L.set("feed")}}),i("div",{className:"name",children:[t.username??"Unknown",t.verified&&e("span",{className:"verified",children:e("img",{src:"./assets/img/icons/instagram/Verified.svg"})})]}),e("span",{})]}),i("div",{className:"profile-body",children:[i("div",{className:"profile-wrapper",children:[i("div",{className:"top",children:[i("div",{className:"profile-picture",children:[e(D,{avatar:t.avatar,className:"avatar"}),t.isLive&&(!t.private||t.isFollowing||!u)?e("img",{onClick:()=>{g("Instagram",{action:"getLives"}).then(p=>{var V;let y=t.username;g("Instagram",{action:"viewLive",username:((V=p[y])==null?void 0:V.participant)??y}).then(U=>{var ae,Ie,ge,se;if(!U)return E("error","Failed to view live, perhaps it ended?");let H=(ae=p[y])!=null&&ae.participant?p[(Ie=p[y])==null?void 0:Ie.participant]:p[y],j=(H==null?void 0:H.participants)??[];j.unshift({username:((ge=p[y])==null?void 0:ge.participant)??y,name:H.name,avatar:H.avatar,verified:H.verified,id:H.id}),ne.set({host:((se=p[y])==null?void 0:se.participant)??y,participants:j,viewers:U}),L.set("live")})})},className:"circle live",src:"./assets/img/icons/instagram/LiveCircle.svg"}):null,t.hasStory&&!t.isLive&&(!t.private||t.isFollowing||u)?e("img",{className:"circle","data-seen":t.seenStory===1,src:"./assets/img/icons/instagram/StoryCircle.svg",onClick:p=>{let[y,V]=vt(p);Ue({username:t.username,avatar:t.avatar,verified:t.verified},null,{x:y,y:V})}}):null]}),i("div",{className:"stats",children:[i("div",{children:[e("div",{className:"value",children:t.posts}),e("div",{className:"label",children:t.posts===1?c("APPS.INSTAGRAM.POST"):c("APPS.INSTAGRAM.POSTS")})]}),i("div",{"data-disabled":!t.isFollowing&&t.private&&!u,onClick:()=>{!t.isFollowing&&t.private&&!u||he.set({title:c("APPS.INSTAGRAM.FOLLOWERS"),username:t.username})},children:[e("div",{className:"value",children:t.followers}),e("div",{className:"label",children:t.followers===1?c("APPS.INSTAGRAM.FOLLOWER"):c("APPS.INSTAGRAM.FOLLOWERS")})]}),i("div",{"data-disabled":!t.isFollowing&&t.private&&!u,onClick:()=>{!t.isFollowing&&t.private&&!u||he.set({title:c("APPS.INSTAGRAM.FOLLOWING"),username:t.username})},children:[e("div",{className:"value",children:t.following}),e("div",{className:"label",children:c("APPS.INSTAGRAM.FOLLOWING")})]})]})]}),e("div",{className:"name",children:t.name}),e("div",{className:"description",ref:l,children:ke(t.bio)??""}),e("div",{className:"buttons",children:u?u&&e("div",{className:"button edit",onClick:()=>o(!0),children:c("APPS.INSTAGRAM.EDIT_PROFILE")}):i(F,{children:[e("div",{className:_e("button",t.requested||t.isFollowing?"following":"follow"),onClick:()=>{t.isFollowing?k.PopUp.set({title:c("APPS.INSTAGRAM.UNFOLLOW_POPUP.TITLE"),description:c("APPS.INSTAGRAM.UNFOLLOW_POPUP.DESCRIPTION").format({name:t.username}),buttons:[{title:c("APPS.INSTAGRAM.UNFOLLOW_POPUP.CANCEL")},{title:c("APPS.INSTAGRAM.UNFOLLOW_POPUP.PROCEED"),color:"red",cb:()=>{ce.set({...t,isFollowing:!1})}}]}):t.private?g("Instagram",{action:"toggleFollow",data:{username:t.username,following:!0}},!0).then(p=>{if(!p)return E("error","Failed to follow");ce.set({...t,requested:!t.requested})}):ce.set({...t,isFollowing:!0})},children:t.isFollowing?c("APPS.INSTAGRAM.FOLLOWING"):t.requested?c("APPS.INSTAGRAM.REQUESTED"):c("APPS.INSTAGRAM.FOLLOW")}),!t.private||t.isFollowing?e("div",{className:"button following",onClick:()=>{Ae.set({username:t.username,name:t.name,verified:t.verified,avatar:t.avatar}),L.set("dm")},children:c("APPS.INSTAGRAM.MESSAGE")}):null]})})]}),t.private&&!t.isFollowing&&!u?i("div",{className:"private-account",children:[e("div",{className:"icon",children:e(Mt,{})}),e("div",{className:"title",children:c("APPS.INSTAGRAM.PRIVATE_ACCOUNT")}),e("div",{className:"description",children:c("APPS.INSTAGRAM.PRIVATE_ACCOUNT_DESCRIPTION")})]}):e("div",{className:"posts",ref:v,onScroll:d,children:P&&P.map((p,y)=>{let V=JSON.parse(p.media)[0];return e("div",{onClick:()=>fe.set(p),children:X(V)?e("video",{src:V,muted:!0,autoPlay:!0,loop:!0}):e(re,{src:V,blur:t.username!==s.username})},y)})})]})]})})}function na(){const[s,t]=m.useState(""),[n,l]=m.useState(""),[v,u]=m.useState([]),[a,f]=m.useState([]);m.useEffect(()=>{me("Instagram")&&g("Instagram",{action:"getPosts",page:0},q.posts).then(P=>{if(!P)return E("warning","No posts received in getPosts");f(P)})},[]),m.useEffect(()=>{const P=setTimeout(()=>t(n),500);return()=>clearTimeout(P)},[n]),m.useEffect(()=>{s.length>0&&g("Instagram",{action:"search",query:s},Object.keys(q.accounts).map(P=>q.accounts[P])).then(P=>{P&&u(P)})},[s]);const{handleScroll:o}=ue({fetchData:P=>g("Instagram",{action:"getPosts",page:P}),onDataFetched:P=>f([...a,...P]),perPage:15});return i("div",{className:"search-container",children:[e("div",{className:"search-header",children:e(Be,{placeholder:c("SEARCH"),onChange:P=>l(P.target.value)})}),e("div",{className:"search-body",onScroll:o,children:s.length>0?e("div",{className:"search-results",children:v.map(P=>i("div",{className:"item",onClick:()=>x(P.username),children:[e("div",{className:"profile-picture",children:e(D,{avatar:P.avatar})}),i("div",{className:"user",children:[i("div",{className:"username",children:[P.username,P.verified&&e("span",{className:"verified",children:e("img",{src:"./assets/img/icons/instagram/Verified.svg"})})]}),e("div",{className:"name",children:P.name})]})]}))}):e("div",{className:"feed-grid",children:a.map((P,I)=>{let r=Math.floor(I/3)%2==0,h=!1;h=I%3==(r?1:0),a[I+1]||(h=!1);let N=JSON.parse(P.media)[0];return e("div",{className:_e("grid-post",h&&"big"),onClick:()=>{Z.set({cb:()=>{L.set("search"),Z.set(null)}}),fe.set(P)},children:X(N)?e("video",{src:N,autoPlay:!0,loop:!0,muted:!0}):e(re,{src:N,blur:!0})},I)})})})]})}function ia(){var f,o,P,I,d,r;const s=T($),t=T(ve),[n,l]=m.useState(""),v=m.useRef(null),u=()=>{n.length!==0&&g("Instagram",{action:"postComment",data:{postId:t.post.id,comment:n}},Math.random().toString(36).substring(7)).then(h=>{if(!h)return E("error","Failed to post comment, no ");l(""),v.current.value="",ve.set({...t,post:t.post,comments:[{user:{username:s.username,avatar:s.avatar,verified:s.verified},comment:{id:h,content:n,timestamp:new Date().getTime(),likes:0,liked:!1}},...t.comments]})})},{handleScroll:a}=ue({fetchData:h=>g("Instagram",{action:"getComments",postId:t.post.id,page:h}),onDataFetched:h=>ve.set({...ve.value,comments:[...ve.value.comments,...h]}),perPage:25});return i(pe.div,{...wt("right","active",.2),className:"comments-container",children:[i("div",{className:"comments-header",children:[e(Ve,{onClick:()=>ve.reset()}),e("div",{className:"title",children:c("APPS.INSTAGRAM.COMMENTS")}),e("span",{})]}),i("div",{className:"comments-body",onScroll:a,children:[i("div",{className:"post-info",children:[e("div",{className:"profile-picture",children:e(D,{avatar:(f=t==null?void 0:t.post)==null?void 0:f.avatar})}),i("div",{className:"post-data",children:[i("div",{className:"caption",children:[i("span",{className:"username",children:[(o=t==null?void 0:t.post)==null?void 0:o.username,((P=t==null?void 0:t.post)==null?void 0:P.verified)&&e("span",{className:"verified",children:e("img",{src:"./assets/img/icons/instagram/Verified.svg"})})]}),ke((I=t==null?void 0:t.post)==null?void 0:I.caption)]}),e("div",{className:"actions",children:e("div",{className:"date",children:Nt((d=t==null?void 0:t.post)==null?void 0:d.timestamp)})})]})]}),(r=t==null?void 0:t.comments)==null?void 0:r.map((h,N)=>e(ra,{data:h},N))]}),i("div",{className:"add-comment",children:[e("div",{className:"profile-picture",children:e(D,{avatar:s.avatar})}),i("div",{className:"input-container",children:[e(ie,{type:"text",ref:v,placeholder:c("APPS.INSTAGRAM.ADD_COMMENT_AS").format({name:s.username}),onKeyPress:h=>{h.key==="Enter"&&u()},onChange:h=>l(h.target.value)}),e("div",{className:"send",onClick:u,children:c("APPS.INSTAGRAM.POST")})]})]})]})}const ra=({data:s})=>{var a;const[t,n]=m.useState(s.comment.liked),[l,v]=m.useState(s.comment.likes);J("instagram:updateCommentLikes",f=>{!f||f.commentId!==s.comment.id||v(o=>o+(f.increment?1:-1))});const u=()=>{x(s.user.username),ve.reset()};return i("div",{className:"comment",children:[i("div",{className:"user",children:[e("div",{className:"profile-picture",onClick:u,children:e(D,{avatar:s.user.avatar})}),i("div",{className:"comment-data",children:[i("div",{className:"caption",children:[i("span",{className:"username",onClick:u,children:[s.user.username,s.user.verified&&e("span",{className:"verified",children:e("img",{src:"./assets/img/icons/instagram/Verified.svg"})})]}),s.comment.content]}),i("div",{className:"actions",children:[e("div",{className:"date",children:Nt(s.comment.timestamp)}),i("div",{className:"likes",children:[l," ",(a=c("APPS.INSTAGRAM.LIKES"))==null?void 0:a.toLowerCase()]})]})]})]}),e(gt,{liked:t,onClick:()=>{s.comment.id&&g("Instagram",{action:"toggleLike",data:{postId:s.comment.id,toggle:!t,isComment:!0}},!t).then(f=>n(f))}})]})},Nt=s=>{const t=new Date(s),l=new Date().getTime()-t.getTime(),v=Math.floor(l/(1e3*60*60*24)),u=Math.floor(l/(1e3*60*60)),a=Math.floor(l/(1e3*60));let f="";return v<1?u<1?f=`${a}m`:f=`${u}h`:v<30?f=`${v}d`:f=`${t.getDate()}/${t.getMonth()+1}/${t.getFullYear()}`,f};function ca(){T(L);const[s,t]=m.useState(0),[n,l]=m.useState([]),[v,u]=m.useState(""),[a,f]=m.useState(null);return m.useEffect(()=>{var o,P,I;s===0&&k.Gallery.set({allowExternal:(I=(P=(o=Ne)==null?void 0:o.value)==null?void 0:P.AllowExternal)==null?void 0:I.InstaPic,multiSelect:!0,includeVideos:!0,onCancel:()=>L.set("feed"),onSelect:d=>{Array.isArray(d)?d.length>0&&l(d.map(r=>r.src)):d&&l([d.src]),t(1)}})},[s]),e("div",{className:"newpost-container","data-camera":!1,children:s===1&&i(F,{children:[i("div",{className:"newpost-header",children:[e(Ve,{onClick:()=>L.set("feed")}),e("div",{className:"title",children:c("APPS.INSTAGRAM.NEW_POST")}),e("div",{className:"next",onClick:()=>{g("Instagram",{action:"newPost",data:{caption:v,location:a,images:n}},!0).then(o=>{if(!o)return E("warning","Failed to post");L.set("feed")})},children:c("APPS.INSTAGRAM.POST")})]}),e("div",{className:"newpost-body",children:i("div",{className:"newpost-content",children:[i("div",{className:"caption",children:[e("div",{className:"attachments",style:{width:`calc(4rem + ${n.length*20}px)`},onClick:()=>t(0),children:n.slice(0,5).map((o,P)=>e("div",{className:"attachment",style:{left:`${P*20}px`,top:`${P*5}px`,zIndex:P+1},children:X(o)?e(la,{src:o}):e(re,{src:o,alt:"image"})},P))}),e("div",{className:"text-area",children:e(ht,{onChange:o=>u(o.target.value),placeholder:c("APPS.INSTAGRAM.CAPTION_PLACEHOLDER")})})]}),e("div",{className:"item",onClick:()=>{k.PopUp.set({title:c("APPS.INSTAGRAM.CHOOSE_LOCATION.TITLE"),description:c("APPS.INSTAGRAM.CHOOSE_LOCATION.DESCRIPTION"),input:{type:"text",maxCharacters:30,onChange:o=>f(o)},buttons:[{title:c("APPS.INSTAGRAM.CHOOSE_LOCATION.CANCEL")},{title:c("APPS.INSTAGRAM.CHOOSE_LOCATION.PROCEED")}]})},children:a?i(F,{children:[i("div",{className:"label",children:[e(Gt,{}),a]}),e(Se,{onClick:o=>{o.stopPropagation(),f(null)}})]}):i(F,{children:[e("div",{className:"label",children:c("APPS.INSTAGRAM.LOCATION")}),e(ft,{})]})})]})})]})})}const la=({src:s})=>{const[t,n]=m.useState(null);return i(F,{children:[e("video",{src:s,crossOrigin:"anonymous",controls:!1,onMouseOver:v=>{var u;return(u=v.currentTarget)==null?void 0:u.play()},onMouseOut:v=>{var u;return(u=v.currentTarget)==null?void 0:u.pause()},onLoadedMetadata:v=>n(v.currentTarget.duration)}),t&&e("div",{className:"video-duration",children:(v=>{const u=Math.floor(v/60),a=bt(v-u*60,0);return`${u}:${a<10?"0"+a:a}`})(t)})]})};function oa(){const s=T(fe);return e(F,{children:e(pe.div,{...kt,className:"viewpost-container",children:i(F,{children:[i("div",{className:"viewpost-header",children:[e(Ve,{onClick:()=>{fe.reset()}}),e("div",{className:"name",children:(s==null?void 0:s.username)??"Unknown"}),e("span",{})]}),e("div",{className:"profile-body",children:e(Pt,{data:{...s,viewPost:!0}})})]})})})}function da(){T(oe.Settings);const s=T($),t=T(Ae),n=T(Z),[l,v]=m.useState([]),u=m.useRef(null),a=m.useRef(0),[f,o]=m.useState({content:"",attachments:[]});m.useEffect(()=>{var d;v([]),g("Instagram",{action:"getMessages",page:0,username:t.username},((d=q.messages.find(r=>r.username===t.username))==null?void 0:d.messages)??[]).then(r=>{r&&v(r.reverse())})},[]),J("instagram:newMessage",d=>{if(t.username!==d.sender||d.sender===s.username)return;v([...l,d]);let r=document.querySelector(".dm-body-container");r.scrollTop=r.scrollHeight});const P=()=>{f.content.length===0&&f.attachments.length===0||g("Instagram",{action:"sendMessage",username:t.username,message:{content:f.content,attachments:f.attachments.length>0?f.attachments:null}},!0).then(d=>{if(!d)return;u.current.value="",v([...l,{sender:s.username,recipient:t.username,content:f.content,attachments:f.attachments,timestamp:Date.now()}]),o({content:"",attachments:[]});let r=document.querySelector(".dm-body-container");r.scrollTop=r.scrollHeight})},{handleScroll:I}=ue({fetchData:d=>g("Instagram",{action:"getMessages",username:t.username,page:d}),onDataFetched:d=>{let r=document.querySelector(".dm-body-container");a.current=r.scrollHeight,v([...d.reverse(),...l])},isReversed:!0,perPage:25});return m.useEffect(()=>{let d=document.querySelector(".dm-body-container");const r=d.scrollHeight;d.scrollTop+=r-a.current,d.scroll},[l]),i("div",{className:"dm-container",children:[i("div",{className:"dm-header",children:[e("i",{className:"far fa-chevron-left",onClick:()=>{n&&n.cb?(n.cb(),Z.reset()):L.set("dmList")}}),i("div",{className:"profile",onClick:d=>{d.stopPropagation(),Z.set({cb:()=>{Ae.set(t),L.set("dm")}}),x(t.username)},children:[e("div",{className:"avatar",children:e(D,{avatar:t.avatar})}),i("div",{className:"username",children:[t.name??t.username,t.verified&&e("span",{className:"verified",children:e("img",{src:"./assets/img/icons/instagram/Verified.svg"})})]})]})]}),e("div",{className:"dm-body-container",onScroll:I,children:e("div",{className:"dm-body",children:l.map((d,r)=>{var Y;let h,N=d.sender===s.username?"self":"other",O=((Y=l[r+1])==null?void 0:Y.sender)===s.username?"self":"other",R=We(d.content);return l[r+1]?h=Math.abs(d.timestamp-l[r+1].timestamp)/36e5:O=void 0,i("div",{className:`message ${N}`,children:[N=="other"?i("div",{className:"message-with-pfp",children:[e("div",{"data-story-reply":R?"true":"false",className:_e("profile-picture",N!==O?"show":"hide"),onClick:S=>{S.stopPropagation(),Z.set({cb:()=>{Ae.set(t),L.set("dm")}}),x(t.username)},children:e(D,{avatar:t.avatar})}),e("div",{className:"message-content",children:R?i("div",{className:"story-reply",children:[e("div",{className:"title",children:c("APPS.INSTAGRAM.REPLIED_TO_YOUR_STORY")}),X(R.src)?e("video",{src:R.src,controls:!1,loop:!0,autoPlay:!0,muted:!0}):e(re,{src:R.src}),e("div",{className:"content",children:R.content})]}):i(F,{children:[d.content&&e("div",{className:"content",children:ke(d.content)}),d.attachments&&e("div",{className:"attatchments",children:d.attachments.map((S,w)=>X(S)?e("video",{src:S,controls:!1,loop:!0,autoPlay:!0,muted:!0,onClick:b=>k.FullscreenImage.set(S)},w):e(re,{src:S,blur:!0,onClick:()=>k.FullscreenImage.set(S)},w))})]})})]}):e(F,{children:R?i("div",{className:"story-reply",children:[X(R.src)?e("video",{src:R.src,controls:!1,loop:!0,autoPlay:!0,muted:!0}):e(re,{src:R.src}),e("div",{className:"content",children:R.content})]}):i(F,{children:[d.content&&e("div",{className:"content",children:ke(d.content)}),d.attachments&&e("div",{className:"attatchments",children:d.attachments.map((S,w)=>X(S)?e("video",{src:S,controls:!1,loop:!0,muted:!0,autoPlay:!0,onClick:b=>{k.FullscreenImage.set(S)}},w):e(re,{src:S,onClick:()=>{k.FullscreenImage.set(S)}},w))})]})}),l[r+1]&&h>6?e("div",{className:"date",children:ct(d.timestamp)}):N!==O&&e("div",{className:"date",children:ct(d.timestamp)})]},r)})})}),e("div",{className:"attachments",children:f.attachments.map((d,r)=>i("div",{className:"attachment",children:[X(d)?e("video",{src:d,muted:!0,controls:!1,loop:!0,autoPlay:!0}):e(re,{src:d}),e(Se,{onClick:()=>{o({...f,attachments:f.attachments.filter((h,N)=>N!==r)})}})]},r))}),i("div",{className:"dm-footer",children:[e(ie,{type:"text",ref:u,onChange:d=>{o({...f,content:d.target.value})},onKeyDown:d=>{d.key==="Enter"&&P()},placeholder:c("APPS.INSTAGRAM.MESSAGE_PLACEHOLDER")}),f.content.length>0?e("span",{onClick:()=>P(),className:"send",children:c("APPS.INSTAGRAM.SEND")}):e(_t,{onClick:()=>{var d,r,h;f.attachments.length<3&&k.Gallery.set({includeVideos:!0,allowExternal:(h=(r=(d=Ne)==null?void 0:d.value)==null?void 0:r.AllowExternal)==null?void 0:h.InstaPic,onSelect:N=>o({...f,attachments:[...f.attachments,Array.isArray(N)?N[0].src:N.src]})})}})]})]})}const We=s=>{if(!(s.includes("<!REPLIED_STORY-DATA=")&&s.includes("!>")))return null;const n=s.match(/src="([^"]+)"/),l=s.match(/content:"([^"]+)"/);return!n||!l?null:{src:n[1],content:l[1]}},ot=Q(0);function ma(){const s=T($),[t,n]=m.useState(!1),[l,v]=m.useState(""),[u,a]=m.useState(""),[f,o]=m.useState([]),[P,I]=m.useState([]);m.useEffect(()=>{me("Instagram")&&g("Instagram",{action:"getRecentMessages",page:0},q.messages).then(r=>{r&&I(r)})},[]);const{handleScroll:d}=ue({fetchData:r=>g("Instagram",{action:"getRecentMessages",page:r}),onDataFetched:r=>I([...P,...r]),perPage:25});return m.useEffect(()=>{const r=setTimeout(()=>v(u),500);return()=>clearTimeout(r)},[u]),m.useEffect(()=>{l.length>0?t?g("Instagram",{action:"search",query:l},q.messages.filter(r=>r.username!==s.username)).then(r=>{r&&o(r)}):o(P.filter(r=>{var h;return r.username.includes(l)||((h=r==null?void 0:r.name)==null?void 0:h.includes(l))&&r.username!==s.username})):o([])},[l]),m.useEffect(()=>{v(""),o([])},[t]),e("div",{className:"dm-list",children:t?i("div",{className:"slide up",children:[i("div",{className:"dm-list-header new",children:[e("i",{className:"far fa-chevron-left",onClick:()=>{n(!1),o([])}}),e("div",{className:"title",children:c("APPS.INSTAGRAM.NEW_MESSAGE")}),e("span",{})]}),i("div",{className:"dm-list-body ",children:[i("div",{className:"input",children:[e("span",{children:c("APPS.INSTAGRAM.TO")}),e(ie,{type:"text",onChange:r=>a(r.target.value),placeholder:c("SEARCH")})]}),f.length>0&&e("div",{className:"items",children:f.map((r,h)=>i("div",{className:"item",onClick:()=>{n(!1),Ae.set(r),L.set("dm")},children:[e("div",{className:"avatar",onClick:N=>{N.stopPropagation(),Z.set({cb:()=>{L.set("dmList"),Z.reset()}}),x(r.username)},children:e(D,{avatar:r.avatar})}),i("div",{className:"info",children:[i("div",{className:"username",children:[r.username,r.verified&&e("span",{className:"verified",children:e("img",{src:"./assets/img/icons/instagram/Verified.svg"})})]}),e("div",{className:"last-message",children:r.name})]})]},h))})]})]}):i(F,{children:[i("div",{className:"dm-list-header",children:[i("div",{className:"top",children:[e("i",{className:"far fa-chevron-left",onClick:()=>L.set("feed")}),e("i",{className:"far fa-edit",onClick:()=>n(!0)})]}),e(Be,{placeholder:c("SEARCH"),onChange:r=>a(r.target.value)})]}),e("div",{className:"dm-list-body",onScroll:d,children:e("div",{className:"items",children:l.length>0&&!t?f.map((r,h)=>{var N,O;if(((N=r.content)==null?void 0:N.length)===0&&(r.content=c("APPS.INSTAGRAM.SENT_A_PHOTO")),(O=r.content)!=null&&O.includes("<!REPLIED_STORY-DATA=")){let R=We(r.content);r.content=c("APPS.INSTAGRAM.REPLIED_TO_YOUR_STORY_CONTENT").format({content:R.content})}return i("div",{className:"item",onClick:()=>{r.unread&&ot.set(ot.value-1),n(!1),Ae.set(r),L.set("dm")},children:[e("div",{className:"avatar",onClick:R=>{R.stopPropagation(),Z.set({cb:()=>{L.set("dmList"),Z.reset()}}),x(r.username)},children:e(D,{avatar:r.avatar})}),i("div",{className:"info",children:[i("div",{className:"username",children:[r.name,r.verified&&e("span",{className:"verified",children:e("img",{src:"./assets/img/icons/instagram/Verified.svg"})})]}),i("div",{className:"last-message",children:[r.content.length>40?r.content.substring(0,40)+"...":r.content," • ",Ee(r.timestamp)]})]})]},h)}):P.map((r,h)=>{var N;if(r.content.length===0&&(r.content=c("APPS.INSTAGRAM.SENT_A_PHOTO")),(N=r.content)!=null&&N.includes("<!REPLIED_STORY-DATA=")){let O=We(r.content);r.content=c("APPS.INSTAGRAM.REPLIED_TO_YOUR_STORY_CONTENT").format({content:O.content})}return i("div",{className:"item",onClick:()=>{n(!1),Ae.set(r),L.set("dm")},children:[e("div",{className:"avatar",onClick:O=>{O.stopPropagation(),Z.set({cb:()=>{L.set("dmList"),Z.reset()}}),x(r.username)},children:e(D,{avatar:r.avatar})}),i("div",{className:"info",children:[i("div",{className:"username",children:[r.name,r.verified&&e("span",{className:"verified",children:e("img",{src:"./assets/img/icons/instagram/Verified.svg"})})]}),r.content.length>40?r.content.substring(0,40)+"...":r.content," • ",Ee(r.timestamp)]})]},h)})})})]})})}function ua(){var ae,Ie,ge,se,Oe,Le,Me,we,A,C,_,z,B,Ce,De,ze,Ke,je,Je,Xe,Qe,Ze,et,tt,at,st,nt,it;const s=T(oe.Settings),t=T(Te.Visible),n=T(Fe),l=T($),v=T(le),u=T(Re),[a,f]=m.useState(null),[o,P]=m.useState(0),[I,d]=m.useState(""),[r,h]=m.useState(!1),[N,O]=m.useState({x:0,y:0}),R=m.useRef(null),[Y,S]=m.useState(0),[w,b]=m.useState(null);m.useEffect(()=>{if(a){for(let M=0;M<a.media.length;M++)if(!a.media[M].seen)return P(M)}},[]),m.useEffect(()=>{u&&(f(u),O(u.origin))},[u]),m.useEffect(()=>{var G,W,K;const M=(W=(G=a==null?void 0:a.media)==null?void 0:G[o])==null?void 0:W.metadata;M!=null&&M.post&&Dt((K=a==null?void 0:a.media[o])==null?void 0:K.image).then(ee=>{if(!ee)return E("warning","Failed to get average color");b(`
                    linear-gradient(0deg,
                        ${ee.darkmuted??ee.muted} 0%,
                        ${ee.darkvibrant??ee.dominant} 100%
                    )
                `)})},[o,a]),m.useEffect(()=>{var M,G;if(!(!R.current||((M=s==null?void 0:s.sound)==null?void 0:M.volume)===void 0)){if(!t||!me("Instagram")){R.current.pause();return}(G=R==null?void 0:R.current)!=null&&G.paused&&R.current.play(),R.current.volume=s.sound.volume}},[(ae=s==null?void 0:s.sound)==null?void 0:ae.volume,t,n==null?void 0:n.active]);const p=M=>{if(r)return h(!1);const G=M.target.getBoundingClientRect(),W=M.clientX-G.left*be();let K=a.media[o];if(K.seen||g("Instagram",{action:"viewedStory",id:K.id}),W<G.width*be()/2)if(o>0)P(o-1);else{let ee=v.findIndex(xe=>xe.username===a.username),Ge=v[ee-1];Ge&&Ue(Ge)}else if(o<a.media.length-1)P(o+1);else{let ee=v.findIndex(xe=>xe.username===a.username),Ge=v[ee+1];Ge?(V(a.username),Ue(Ge),P(0)):U()}},y=()=>{var G,W;if(!I||I==="")return;let M=`<!REPLIED_STORY-DATA='{src="${(W=(G=a==null?void 0:a.media)==null?void 0:G[o])==null?void 0:W.image}", content:"${I}"}'!>`;g("Instagram",{action:"sendMessage",username:a.username,message:{content:M}},!0).then(K=>{if(!K)return E("error","Failed to send message");Ae.set({username:a.username,name:a.name,verified:a.verified,avatar:a.avatar}),L.set("dm"),U()})},V=M=>{var G;a.media.length===o+1&&le.set((G=le.value)==null?void 0:G.map(W=>(W.username===M&&(W.seen=1),W)))},U=M=>{M!==!1&&V(a.username),Re.set(null)},H=()=>{var M,G;(G=(M=a==null?void 0:a.media[o])==null?void 0:M.metadata)!=null&&G.post&&k.ContextMenu.set({buttons:[{title:c("APPS.INSTAGRAM.VIEW_POST"),cb:()=>{var W,K,ee;if(ye((ee=(K=(W=a==null?void 0:a.media[o])==null?void 0:W.metadata)==null?void 0:K.post)==null?void 0:ee.id),a!=null&&a.media[o].seen)return U();g("Instagram",{action:"viewedStory",id:a==null?void 0:a.media[o].id},!0).then(()=>U())}}]})},j=(ge=(Ie=a==null?void 0:a.media)==null?void 0:Ie[o])==null?void 0:ge.metadata;return i(pe.div,{className:"instagram-story",initial:{scale:0,transformOrigin:u!=null&&u.origin?`${((se=u==null?void 0:u.origin)==null?void 0:se.x)??0}px ${((Oe=u==null?void 0:u.origin)==null?void 0:Oe.y)??0}px`:"50% 50%"},animate:{scale:1,transformOrigin:u!=null&&u.origin?`${((Le=u==null?void 0:u.origin)==null?void 0:Le.x)??0}px ${((Me=u==null?void 0:u.origin)==null?void 0:Me.y)??0}px`:"50% 50%"},exit:{scale:0,transformOrigin:N?`${(N==null?void 0:N.x)??0}px ${(N==null?void 0:N.y)??0}px`:"50% 50%"},transition:{duration:.25,ease:"easeInOut"},children:[j!=null&&j.post?e("div",{className:"background-image",onClick:p,style:{background:w??"rgb(20,20,20)"}}):X((A=(we=a==null?void 0:a.media)==null?void 0:we[o])==null?void 0:A.image)?e("video",{ref:R,className:"background-image",onClick:p,src:(_=(C=a==null?void 0:a.media)==null?void 0:C[o])==null?void 0:_.image,autoPlay:!0,loop:!0,onLoadedMetadata:()=>R.current.volume=s.sound.volume,onTimeUpdate:()=>S(R.current.currentTime/R.current.duration)}):e("div",{className:"background-image",onClick:p,style:{backgroundImage:`url(${(B=(z=a==null?void 0:a.media)==null?void 0:z[o])==null?void 0:B.image})`}}),e(Pe,{children:r&&e(va,{activeImage:o,close:()=>h(!1)})}),i("div",{className:"story-wrapper",children:[i("div",{className:"story-header",children:[e("div",{className:"story-steps",children:(Ce=a==null?void 0:a.media)==null?void 0:Ce.map((M,G)=>{var K;let W=X((K=a==null?void 0:a.media[G])==null?void 0:K.image);return e("div",{style:W&&o===G?{background:`linear-gradient(to right, rgba(255,255,255,0.8) ${Y*100}%, rgba(255,255,255,0.3) ${Y*100}%)`}:void 0,className:o===G?"active":"",onClick:()=>P(G)},G)})}),i("div",{className:"profile-details",children:[i("div",{className:"profile",onClick:()=>{var M,G;(G=(M=a==null?void 0:a.media)==null?void 0:M[o])!=null&&G.seen||g("Instagram",{action:"viewedStory",id:a==null?void 0:a.media[o].id},!0).then(()=>U()),U(),x(a==null?void 0:a.username)},children:[e("div",{className:"profile-picture",children:e(D,{className:"avatar",avatar:a==null?void 0:a.avatar})}),i("div",{className:"name",children:[a==null?void 0:a.username,!1]}),e("div",{className:"time",children:Ee((ze=(De=a==null?void 0:a.media)==null?void 0:De[o])==null?void 0:ze.timestamp)})]}),e("div",{className:"stats",children:e(Se,{onClick:()=>{var M,G,W,K;if((G=(M=a==null?void 0:a.media)==null?void 0:M[o])!=null&&G.seen)return U();g("Instagram",{action:"viewedStory",id:(K=(W=a==null?void 0:a.media)==null?void 0:W[o])==null?void 0:K.id},!0).then(()=>U())}})})]})]}),(j==null?void 0:j.post)&&e("div",{className:"story-post",children:i("div",{className:"post-wrapper",onClick:H,children:[X((je=(Ke=a==null?void 0:a.media)==null?void 0:Ke[o])==null?void 0:je.image)?e("video",{src:(Xe=(Je=a==null?void 0:a.media)==null?void 0:Je[o])==null?void 0:Xe.image,muted:!0}):e(re,{src:(Ze=(Qe=a==null?void 0:a.media)==null?void 0:Qe[o])==null?void 0:Ze.image}),i("div",{className:"post-username",children:["@",(et=j==null?void 0:j.post)==null?void 0:et.username]})]})}),(a==null?void 0:a.username)===l.username?i("div",{className:"story-footer","data-stats":!0,children:[i("div",{className:"seen-by",onClick:()=>h(!0),children:[((at=(tt=a==null?void 0:a.media)==null?void 0:tt[o])==null?void 0:at.viewers)&&e("div",{className:"images",children:(it=(nt=(st=a==null?void 0:a.media)==null?void 0:st[o])==null?void 0:nt.viewers)==null?void 0:it.map((M,G)=>e(D,{avatar:M.avatar},G))}),c("APPS.INSTAGRAM.SEEN_BY").format({count:Bt(a==null?void 0:a.media[o].views)??0})]}),e(Se,{onClick:()=>{k.PopUp.set({title:c("APPS.INSTAGRAM.DELETE_STORY.TITLE"),description:c("APPS.INSTAGRAM.DELETE_STORY.DESCRIPTION"),buttons:[{title:c("APPS.INSTAGRAM.DELETE_STORY.CANCEL")},{title:c("APPS.INSTAGRAM.DELETE_STORY.PROCEED"),color:"red",cb:()=>{g("Instagram",{action:"removeFromStory",id:a==null?void 0:a.media[o].id},!0).then(M=>{var G;if(!M)return E("error","Failed to remove image from story");E("info","Removed image from story"),Re.set(null),a.media.length===1&&le.set((G=le.value)==null?void 0:G.filter(W=>W.username!==a.username))})}}]})}})]}):i("div",{className:"story-footer",children:[e("div",{className:"input",children:e(ie,{type:"text",placeholder:c("APPS.INSTAGRAM.SEND_A_MESSAGE"),onChange:M=>d(M.target.value),onKeyDown:M=>{M.key==="Enter"&&y()}})}),e(qe,{onClick:()=>y()})]})]})]})}const va=s=>{var u;const t=T(Re),[n,l]=m.useState([]);m.useEffect(()=>{var a,f;(s==null?void 0:s.activeImage)!==void 0&&g("Instagram",{action:"getViewers",id:t==null?void 0:t.media[s.activeImage].id,page:0},(f=(a=q.stories.find(o=>o.username===t.username))==null?void 0:a.media.find(o=>o.id===t.media[s.activeImage].id))==null?void 0:f.viewers).then(o=>{if(!o)return E("warning","Failed to fetch viewers data");l(o)})},[]);const{handleScroll:v}=ue({fetchData:a=>g("Instagram",{action:"getViewers",id:t==null?void 0:t.media[s.activeImage].id,page:a}),onDataFetched:a=>l(f=>[...f,...a]),perPage:15});return i(pe.div,{className:"story-modal",initial:{y:600},animate:{y:0},exit:{y:600},transition:{duration:.35,ease:"easeInOut"},children:[i("div",{className:"story-modal-header",children:[i("div",{className:"views",children:[e(mt,{})," ",e("div",{className:"views",children:((u=t==null?void 0:t.media[s==null?void 0:s.activeImage])==null?void 0:u.views)??0})]}),e(Se,{className:"close",onClick:s==null?void 0:s.close})]}),i("div",{className:"story-modal-content",children:[e("div",{className:"title",children:c("APPS.INSTAGRAM.STORY_VIEWERS")}),e("div",{className:"viewers",onScroll:v,children:n.map((a,f)=>e("div",{className:"item",children:i("div",{className:"user",onClick:()=>{x(a.username),Re.set(null)},children:[e("div",{className:"profile-picture",children:e(D,{className:"avatar",avatar:a.avatar})}),i("div",{className:"user-details",children:[i("div",{className:"username",children:[a.username,a.verified&&e("span",{className:"verified",children:e("img",{src:"./assets/img/icons/instagram/Verified.svg"})})]}),e("div",{className:"name",children:a.name})]})]})},f))})]})]})};function fa(){const s=T(L),t=T($);return e("div",{className:"instagram-footer",children:i(F,{children:[[{value:"feed",activeIcon:e(yt,{}),icon:e(Ut,{})},{value:"search",activeIcon:e(Ft,{}),icon:e(Vt,{})},{value:"newPost",activeIcon:e(xt,{}),icon:e(Ht,{})},{value:"notifications",activeIcon:e(Ye,{}),icon:e(ut,{})}].map((l,v)=>e("div",{onClick:()=>{fe.value&&fe.reset(),L.set(l.value)},children:s===l.value?l.activeIcon:l.icon},v)),e(D,{avatar:t.avatar,className:"profile",onClick:()=>x(t.username)})]})})}function ha(){const s=T(he),[t,n]=m.useState([]);let l={[c("APPS.INSTAGRAM.FOLLOWING")]:"getFollowing",[c("APPS.INSTAGRAM.FOLLOWERS")]:"getFollowers",[c("APPS.INSTAGRAM.LIKED_BY")]:"getLikes"};const v=(a,f)=>{var o,P;if(a!==c("APPS.INSTAGRAM.LIKED_BY")){let I=a===c("APPS.INSTAGRAM.FOLLOWING")?(o=q.accounts[f])==null?void 0:o.followingList:((P=q.accounts[f])==null?void 0:P.followerList)??[];return I==null?void 0:I.map(d=>q.accounts[d])}};m.useEffect(()=>{me("Instagram")&&g("Instagram",{action:l[s.title],data:{postId:s.postId,username:s.username,page:0}},v(s.title,s.username)).then(a=>{if(!a)return E("info","No data returned from instagram event");n(a)})},[]);const{handleScroll:u}=ue({fetchData:a=>g("Instagram",{action:l[s.title],data:{postId:s.postId,username:s.username,page:a}}),onDataFetched:a=>n([...t,...a]),perPage:20});return e(F,{children:s&&t&&i(pe.div,{...Wt,className:"userpanel-container",children:[i("div",{className:"userpanel-header",children:[e("i",{className:"far fa-chevron-left",onClick:()=>he.set(null)}),e("div",{className:"userpanel-title",children:s.title}),e("span",{})]}),e("div",{className:"userpanel-body",onScroll:u,children:e("div",{className:"items",children:t.map((a,f)=>e(ga,{data:a,allData:s},f))})})]})})}const ga=s=>{const t=T($);let n=s.data;const[l,v]=m.useState(n.isFollowing===1);return i("div",{className:"item",onClick:()=>{he.set(s.allData),x(n.username)},children:[i("div",{className:"user",children:[e("div",{className:"profile-picture",children:e(D,{avatar:n.avatar})}),i("div",{className:"user-details",children:[i("div",{className:"username",children:[n.username,n.verified&&e("span",{className:"verified",children:e("img",{src:"./assets/img/icons/instagram/Verified.svg"})})]}),e("div",{className:"name",children:n.name})]})]}),t.username!==n.username&&e("div",{className:"action",onClick:u=>{u.stopPropagation(),n.username!==t.username&&(l?k.PopUp.set({title:c("APPS.INSTAGRAM.UNFOLLOW_POPUP.TITLE"),description:c("APPS.INSTAGRAM.UNFOLLOW_POPUP.DESCRIPTION").format({name:n.username}),buttons:[{title:c("APPS.INSTAGRAM.UNFOLLOW_POPUP.CANCEL")},{title:c("APPS.INSTAGRAM.UNFOLLOW_POPUP.PROCEED"),color:"red",cb:()=>{g("Instagram",{action:"toggleFollow",data:{username:n.username,following:!l}},!0).then(()=>{v(a=>!a)})}}]}):g("Instagram",{action:"toggleFollow",data:{username:n.username,following:!l}},!0).then(()=>{v(a=>!a)}))},children:l?e("div",{className:"button following",children:c("APPS.INSTAGRAM.FOLLOWING")}):e("div",{className:"button follow",children:c("APPS.INSTAGRAM.FOLLOW")})})]})},L=Q("feed"),$=Q(null),de=Q(!1),le=Q([]),Re=Q(null),ce=Q(null),ve=Q(null),fe=Q(null),Ae=Q(null),Z=Q(null),he=Q(null);function Ia(){var h;const s=T(oe.Settings),t=(h=T(Fe))==null?void 0:h.active,n=T(oe.Styles.TextColor),l=T(L),v=T($),u=T(de),a=T(fe),f=T(Re),o=T(ve),P=T(he),[I,d]=m.useState(!0);m.useEffect(()=>{var N,O;t&&((N=t==null?void 0:t.data)==null?void 0:N.view)==="profile"&&x((O=t==null?void 0:t.data)==null?void 0:O.username)},[t]),m.useEffect(()=>{g("isAdmin",null,!1).then(N=>{if(te.APPS.INSTAGRAM.account.value){$.set({...te.APPS.INSTAGRAM.account.value,isAdmin:N}),de.set(!0),d(!1);return}else if(te.APPS.INSTAGRAM.account.value===!1)return d(!1);g("Instagram",{action:"isLoggedIn"},q.account).then(O=>{O?(te.APPS.INSTAGRAM.account.set(O),$.set({...O,isAdmin:N}),de.set(!0),d(!1)):(d(!1),te.APPS.INSTAGRAM.account.set(null))})})},[]),m.useEffect(()=>{if(me("Instagram"))return setTimeout(()=>{l==="live"||l==="story"?(oe.Styles.TextColor.set("#fafafaed"),l=="live"&&k.IndicatorVisible.set(!1)):(oe.Styles.TextColor.set(s.display.theme==="dark"?"#fafafaed":"#000000"),k.IndicatorVisible.set(!0)),l==="dm"?Te.ReceiveAppNotifications.set(!1):Te.ReceiveAppNotifications.set(!0)},250),()=>{Te.ReceiveAppNotifications.set(!0)}},[l]);const r={feed:e(Zt,{}),search:e(na,{}),notifications:e(ea,{}),profile:e(sa,{}),live:e(zt,{}),logIn:e(lt,{}),newPost:e(ca,{}),dmList:e(ma,{}),dm:e(da,{})};return J("instagram:logout",N=>{if(!N)return E("warning","No username provided to logout");if(!u)return E("info","User is not logged in, cannot log out");if(N!==(v==null?void 0:v.username))return E("warning","Username provided does not match current logged in username, not logging out");$.set(null),de.set(!1),L.set("logIn")}),e("div",{className:"instagram-container",children:I||!$t()?e("div",{className:"loading",children:e(qt,{size:40,lineWeight:5,speed:2,color:n})}):e("div",{className:"instagram-wrapper",children:u?i(F,{children:[e(Pe,{children:f&&e(ua,{})}),e(Pe,{children:P&&e(ha,{})}),e(Pe,{children:o&&e(ia,{})}),e(Pe,{children:a&&e(oa,{})}),r==null?void 0:r[l],l!=="live"&&l!=="logIn"&&l!=="newPost"&&l!=="comments"&&!f&&e(fa,{})]}):e(lt,{})})})}const ye=(s,t)=>{s&&g("Instagram",{action:"getPost",id:s},q.posts.find(n=>n.id===s)).then(n=>{if(!n)return E("warning","Failed to fetch post data");if(t)return $e(n);fe.set(n)})},x=s=>{var t;s&&g("Instagram",{action:"getProfile",username:s},(t=q.accounts)==null?void 0:t[s]).then(n=>{if(!n)return E("warning","Failed to fetch profile data");ce.set(n),L.set("profile"),he.value&&he.reset()})},$e=s=>{s.id&&g("Instagram",{action:"getComments",postId:s.id,page:0},q.comments.filter(t=>t.postId===s.id)).then(t=>{t&&ve.set({post:s,comments:t})})},Ue=(s,t,n)=>{var l;!s&&!(s!=null&&s.username)||((t==null?void 0:t.length)==0&&(t=null),g("Instagram",{action:"getStory",username:s.username},(l=q.stories.find(v=>v.username===s.username))==null?void 0:l.media).then(v=>{if(!v||v.length===0)return E("warning","Failed to fetch story data");Re.set({username:s.username,avatar:s.avatar,verified:s.verified,media:v,queue:t,origin:n})}))};export{$ as Account,ve as Comments,L as CurrentView,Ae as Dm,Z as History,de as LoggedIn,fe as Post,ce as Profile,le as Stories,Re as Story,he as UserPanel,Ia as default,$e as fetchAndSetComments,ye as fetchAndSetPost,x as fetchAndSetProfile,Ue as fetchAndSetStory};
