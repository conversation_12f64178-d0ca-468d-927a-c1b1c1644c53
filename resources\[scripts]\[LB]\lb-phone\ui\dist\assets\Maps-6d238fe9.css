:root{--phone-color-primary: rgb(255, 255, 255);--phone-color-opacity: rgba(242, 242, 242, .4);--phone-color-opacity2: rgb(30, 30, 30, .5);--phone-color-highlight: rgb(250, 250, 250);--phone-color-highlight2: rgb(240, 240, 240);--phone-color-highlight3: rgb(220, 220, 220);--phone-highlight-opacity15: rgba(145, 145, 145, .15);--phone-highlight-opacity35: rgba(145, 145, 145, .35);--phone-highlight-opacity45: rgba(145, 145, 145, .45);--phone-highlight-opacity55: rgba(145, 145, 145, .55);--phone-color-input: rgba(241, 241, 241, .656);--phone-text-primary: rgb(0, 0, 0);--phone-text-secondary: rgb(142, 142, 147);--phone-color-hover: rgb(240, 240, 240);--phone-color-border: rgba(200, 200, 200, .4);--phone-color-grey: #8e8e93;--phone-color-blue: #0a84ff;--phone-color-green: #32d74b;--phone-color-green-secondary: #092911;--phone-color-red: #ff3b30;--phone-color-orange: rgb(255, 157, 10);--phone-color-yellow: #cca250;--phone-color-pink: #ff3b30;--instagram-primary: #ffffff;--instagram-blue: rgb(0, 149, 246);--instagram-red: rgb(237, 73, 86);--instagram-primary-text: rgb(38, 38, 38);--instagram-secondary-text: rgb(142, 142, 142);--instagram-stroke: rgb(219, 219, 219);--instagram-border: rgb(219, 219, 219);--instagram-highlight: rgb(239, 239, 239);--tinder-color-pink: #ff4573;--tinder-color-orange: #ff5f65;--tinder-color-mix: #f5547c;--twitter-primary: #f5f8fa;--twitter-secondary: #14171a;--twitter-background-highlight: rgb(239, 243, 244);--twitter-primary-text: #14171a;--twitter-secondary-text: #657786;--twitter-alt-text: #657786;--twitter-border: #bdc5cd75;--twitter-border-secondary: #1d9bf0;--twitter-highlight: #1d9bf0;--twitter-hover: rgba(15, 20, 25, .1);--twitter-action: #14171a;--twitter-blue: #1d9bf0;--tiktok-primary: #ffffff;--tiktok-secondary: #000000;--tiktok-text-primary: #000000;--tiktok-text-secondary: #86878b;--tiktok-color-border: #d0d1d3;--tiktok-color-pink: #fe2c55;--tiktok-color-aqua: #00f2ea;--tiktok-color-yellow: #f8cd14;--tiktok-color-blue: #479fc5;--tiktok-color-unread: rgba(254, 44, 86, .2);--crypto-color-primary: rgb(255, 255, 255);--browser-primary: rgb(245, 245, 245);--browser-secondary: rgba(153, 153, 153, .15);--browser-gradient: linear-gradient(230deg, #f4d6ff, #c5f1ff);--browser-footer: rgba(255, 255, 255, .75);--browser-border: rgba(102, 102, 102, .75);--browser-text-secondary: #696969;--controlcentre-opacity: rgba(255, 255, 255, .15);--controlcentre-opacity2: rgba(255, 255, 255, .2);--controlcentre-active: rgba(255, 255, 255, .5);--notification-primary: rgba(215, 215, 215, .5);--notification-secondary: rgba(215, 215, 215, .1);--lockscreeneditor-background: rgba(255, 255, 255, .75);--lockscreeneditor-secondary: #d9d9d9;--app-bg: #ececec;--app-bg2: #ffffff;--app-secondary: #ffffff;--app-secondary2: #ececec;--app-highlight: #cccccc;--app-highlight2: #999999;--app-highlight3: #ffffff;--app-border: #666666;--app-slider: #cccccc;--app-slider-active: #333333;--app-button: #ffffff;--components-bg: #eeeeee;--components-secondary: #ffffff;--components-highlight: #cccccc}[data-theme=dark]{--phone-color-primary: #000000;--phone-color-opacity: rgb(30, 30, 30, .5);--phone-color-opacity2: rgba(242, 242, 242, .4);--phone-color-highlight: rgb(15, 15, 15);--phone-color-highlight2: rgb(20, 20, 20);--phone-color-highlight3: rgb(25, 25, 25);--phone-highlight-opacity35: rgba(145, 145, 145, .35);--phone-highlight-opacity45: rgba(50, 50, 50, .6);--phone-highlight-opacity55: rgb(60, 60, 60, .8);--phone-color-input: rgba(60, 60, 67, .6);--phone-text-primary: #f2f2f7;--phone-text-secondary: #6f6f6f;--phone-color-grey: #636366;--phone-color-hover: rgb(30, 30, 30);--phone-color-border: rgba(150, 150, 150, .2);--phone-color-blue: #076bcf;--instagram-primary: #000000;--instagram-blue: rgb(0, 149, 246);--instagram-red: rgb(237, 73, 86);--instagram-primary-text: rgb(250, 250, 250);--instagram-secondary-text: rgb(142, 142, 142);--instagram-stroke: rgb(219, 219, 219);--instagram-border: rgb(54, 54, 54);--instagram-highlight: rgb(38, 38, 38);--twitter-primary: #000000;--twitter-secondary: #f5f8fa;--twitter-background-highlight: rgb(20, 20, 20);--twitter-primary-text: #f5f8fa;--twitter-secondary-text: #aab8c2;--twitter-alt-text: #657786;--twitter-border: #38444d;--twitter-border-secondary: #38444d;--twitter-hover: rgba(150, 150, 150, .1);--twitter-highlight: #dcdcdc;--twitter-action: #1d9bf0;--twitter-blue: #1d9bf0;--crypto-color-primary: rgb(24, 26, 32);--tiktok-text-primary: #f2f2f7;--tiktok-text-secondary: #6f6f6f;--tiktok-color-border: #96969633;--browser-primary: rgb(15, 15, 15);--browser-secondary: rgba(153, 153, 153, .15);--browser-gradient: linear-gradient(230deg, #453b48, #2f393d);--browser-footer: rgba(51, 51, 51, .75);--browser-border: rgba(102, 102, 102, .75);--browser-text-secondary: #999999;--controlcentre-opacity: rgba(0, 0, 0, .15);--controlcentre-opacity2: rgba(0, 0, 0, .2);--controlcentre-active: rgba(0, 0, 0, .5);--notification-primary: rgba(0, 0, 0, .1);--notification-secondary: rgba(0, 0, 0, .12);--lockscreeneditor-background: rgba(0, 0, 0, .8);--lockscreeneditor-secondary: #333333;--app-bg: #000000;--app-bg2: #000000;--app-secondary: #141414;--app-secondary2: #141414;--app-highlight: #cccccc;--app-highlight2: #696969;--app-highlight3: #212121;--app-border: #cccccc;--app-slider: #999999;--app-slider-active: #ffffff;--app-button: #333333;--components-bg: #000000;--components-secondary: #141414;--components-highlight: #696969}.maps-container{height:100%;width:100%;display:flex;align-items:center;justify-content:center;position:relative;overflow:hidden;border-radius:45px}.maps-container .map-wrapper{position:absolute;height:115%;width:115%;display:flex;align-items:center;justify-content:center;z-index:9}.maps-container .map-wrapper .map{height:100%;width:100%}.maps-container .map-wrapper .map .leaflet-pane .leaflet-marker-pane img{outline:0!important;border:0!important;-webkit-tap-highlight-color:transparent!important}.maps-container .map-wrapper .map .leaflet-popup,.maps-container .map-wrapper .map .leaflet-popup-content-wrapper{background-color:var(--phone-color-primary);border-radius:15px;color:var(--phone-text-primary)}.maps-container .map-wrapper .map .leaflet-tile-container img.leaflet-tile{mix-blend-mode:normal}.maps-container .map-wrapper .map .location-popup{display:flex;flex-direction:column;align-items:center;background-color:var(--phone-color-primary)}.maps-container .map-wrapper .map .location-popup .name{font-size:16px;font-weight:600;color:var(--phone-text-primary)}.maps-container .map-wrapper .map .location-popup .description{font-size:14px;font-weight:400;font-style:italic;color:var(--phone-text-secondary)}.maps-container .map-wrapper .map .location-popup .button{margin-top:1rem;width:5rem;height:2rem;background-color:var(--phone-color-blue);color:#f7f7f6;font-size:15px;font-weight:500;border-radius:10px;display:flex;align-items:center;justify-content:center;cursor:pointer}.maps-container .options{position:absolute;z-index:400;top:10rem;right:2.5rem;display:flex;flex-direction:column;align-items:center}.maps-container .options svg{padding:.75rem;background-color:#ffffffbf;-webkit-backdrop-filter:blur(1rem);backdrop-filter:blur(1rem);font-size:20px;color:#6c6c6c;cursor:pointer;transition:all .2s ease-in-out}.maps-container .options svg:first-child{border-bottom:1px solid #6c6c6c;border-top-left-radius:15px;border-top-right-radius:15px}.maps-container .options svg:last-child{border-bottom-left-radius:15px;border-bottom-right-radius:15px}.maps-container .options svg:hover{filter:brightness(.85)}.maps-container .bottom-search{position:absolute;bottom:0;z-index:10;background-color:#ffffffbf;-webkit-backdrop-filter:blur(1rem);backdrop-filter:blur(1rem);width:88%;border-radius:15px;padding:.75rem 1.5rem 3.5rem;display:flex;flex-direction:column;align-items:center}.maps-container .bottom-search .dot{height:.3rem;width:4rem;border-radius:5px;cursor:pointer;background-color:#00000080}.maps-container .bottom-search .expanded{height:100%;width:100%;display:flex;flex-direction:column;align-items:center;margin-top:1rem}.maps-container .bottom-search .expanded .locations{width:85%;border-radius:7px;background-color:#ffffffbf;-webkit-backdrop-filter:blur(1rem);backdrop-filter:blur(1rem);padding:1rem 2rem;display:flex;display:grid;grid-template-columns:repeat(auto-fit,minmax(60px,1fr));gap:.5rem 1.5rem;max-height:17rem;overflow:auto}.maps-container .bottom-search .expanded .locations::-webkit-scrollbar{display:none}.maps-container .bottom-search .expanded .locations .location{display:flex;flex-direction:column;align-items:center;gap:.15rem}.maps-container .bottom-search .expanded .locations .location svg{font-size:33px;color:var(--phone-color-blue);border-radius:50%;background-color:#91919140;padding:.8rem;cursor:pointer;transition:all .2s ease-in-out}.maps-container .bottom-search .expanded .locations .location svg:hover{filter:brightness(.7)}.maps-container .bottom-search .expanded .locations .location .name{margin-top:.6rem;overflow-wrap:break-word;width:100%;text-align:center;font-size:18px;font-weight:500}.maps-container .bottom-search .expanded .locations .location .action{font-size:16px;font-weight:400;color:#8e8e93}
