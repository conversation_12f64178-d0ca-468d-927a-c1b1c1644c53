:root{--phone-color-primary: rgb(255, 255, 255);--phone-color-opacity: rgba(242, 242, 242, .4);--phone-color-opacity2: rgb(30, 30, 30, .5);--phone-color-highlight: rgb(250, 250, 250);--phone-color-highlight2: rgb(240, 240, 240);--phone-color-highlight3: rgb(220, 220, 220);--phone-highlight-opacity15: rgba(145, 145, 145, .15);--phone-highlight-opacity35: rgba(145, 145, 145, .35);--phone-highlight-opacity45: rgba(145, 145, 145, .45);--phone-highlight-opacity55: rgba(145, 145, 145, .55);--phone-color-input: rgba(241, 241, 241, .656);--phone-text-primary: rgb(0, 0, 0);--phone-text-secondary: rgb(142, 142, 147);--phone-color-hover: rgb(240, 240, 240);--phone-color-border: rgba(200, 200, 200, .4);--phone-color-grey: #8e8e93;--phone-color-blue: #0a84ff;--phone-color-green: #32d74b;--phone-color-green-secondary: #092911;--phone-color-red: #ff3b30;--phone-color-orange: rgb(255, 157, 10);--phone-color-yellow: #cca250;--phone-color-pink: #ff3b30;--instagram-primary: #ffffff;--instagram-blue: rgb(0, 149, 246);--instagram-red: rgb(237, 73, 86);--instagram-primary-text: rgb(38, 38, 38);--instagram-secondary-text: rgb(142, 142, 142);--instagram-stroke: rgb(219, 219, 219);--instagram-border: rgb(219, 219, 219);--instagram-highlight: rgb(239, 239, 239);--tinder-color-pink: #ff4573;--tinder-color-orange: #ff5f65;--tinder-color-mix: #f5547c;--twitter-primary: #f5f8fa;--twitter-secondary: #14171a;--twitter-background-highlight: rgb(239, 243, 244);--twitter-primary-text: #14171a;--twitter-secondary-text: #657786;--twitter-alt-text: #657786;--twitter-border: #bdc5cd75;--twitter-border-secondary: #1d9bf0;--twitter-highlight: #1d9bf0;--twitter-hover: rgba(15, 20, 25, .1);--twitter-action: #14171a;--twitter-blue: #1d9bf0;--tiktok-primary: #ffffff;--tiktok-secondary: #000000;--tiktok-text-primary: #000000;--tiktok-text-secondary: #86878b;--tiktok-color-border: #d0d1d3;--tiktok-color-pink: #fe2c55;--tiktok-color-aqua: #00f2ea;--tiktok-color-yellow: #f8cd14;--tiktok-color-blue: #479fc5;--tiktok-color-unread: rgba(254, 44, 86, .2);--crypto-color-primary: rgb(255, 255, 255);--browser-primary: rgb(245, 245, 245);--browser-secondary: rgba(153, 153, 153, .15);--browser-gradient: linear-gradient(230deg, #f4d6ff, #c5f1ff);--browser-footer: rgba(255, 255, 255, .75);--browser-border: rgba(102, 102, 102, .75);--browser-text-secondary: #696969;--controlcentre-opacity: rgba(255, 255, 255, .15);--controlcentre-opacity2: rgba(255, 255, 255, .2);--controlcentre-active: rgba(255, 255, 255, .5);--notification-primary: rgba(215, 215, 215, .5);--notification-secondary: rgba(215, 215, 215, .1);--lockscreeneditor-background: rgba(255, 255, 255, .75);--lockscreeneditor-secondary: #d9d9d9;--app-bg: #ececec;--app-bg2: #ffffff;--app-secondary: #ffffff;--app-secondary2: #ececec;--app-highlight: #cccccc;--app-highlight2: #999999;--app-highlight3: #ffffff;--app-border: #666666;--app-slider: #cccccc;--app-slider-active: #333333;--app-button: #ffffff;--components-bg: #eeeeee;--components-secondary: #ffffff;--components-highlight: #cccccc}[data-theme=dark]{--phone-color-primary: #000000;--phone-color-opacity: rgb(30, 30, 30, .5);--phone-color-opacity2: rgba(242, 242, 242, .4);--phone-color-highlight: rgb(15, 15, 15);--phone-color-highlight2: rgb(20, 20, 20);--phone-color-highlight3: rgb(25, 25, 25);--phone-highlight-opacity35: rgba(145, 145, 145, .35);--phone-highlight-opacity45: rgba(50, 50, 50, .6);--phone-highlight-opacity55: rgb(60, 60, 60, .8);--phone-color-input: rgba(60, 60, 67, .6);--phone-text-primary: #f2f2f7;--phone-text-secondary: #6f6f6f;--phone-color-grey: #636366;--phone-color-hover: rgb(30, 30, 30);--phone-color-border: rgba(150, 150, 150, .2);--phone-color-blue: #076bcf;--instagram-primary: #000000;--instagram-blue: rgb(0, 149, 246);--instagram-red: rgb(237, 73, 86);--instagram-primary-text: rgb(250, 250, 250);--instagram-secondary-text: rgb(142, 142, 142);--instagram-stroke: rgb(219, 219, 219);--instagram-border: rgb(54, 54, 54);--instagram-highlight: rgb(38, 38, 38);--twitter-primary: #000000;--twitter-secondary: #f5f8fa;--twitter-background-highlight: rgb(20, 20, 20);--twitter-primary-text: #f5f8fa;--twitter-secondary-text: #aab8c2;--twitter-alt-text: #657786;--twitter-border: #38444d;--twitter-border-secondary: #38444d;--twitter-hover: rgba(150, 150, 150, .1);--twitter-highlight: #dcdcdc;--twitter-action: #1d9bf0;--twitter-blue: #1d9bf0;--crypto-color-primary: rgb(24, 26, 32);--tiktok-text-primary: #f2f2f7;--tiktok-text-secondary: #6f6f6f;--tiktok-color-border: #96969633;--browser-primary: rgb(15, 15, 15);--browser-secondary: rgba(153, 153, 153, .15);--browser-gradient: linear-gradient(230deg, #453b48, #2f393d);--browser-footer: rgba(51, 51, 51, .75);--browser-border: rgba(102, 102, 102, .75);--browser-text-secondary: #999999;--controlcentre-opacity: rgba(0, 0, 0, .15);--controlcentre-opacity2: rgba(0, 0, 0, .2);--controlcentre-active: rgba(0, 0, 0, .5);--notification-primary: rgba(0, 0, 0, .1);--notification-secondary: rgba(0, 0, 0, .12);--lockscreeneditor-background: rgba(0, 0, 0, .8);--lockscreeneditor-secondary: #333333;--app-bg: #000000;--app-bg2: #000000;--app-secondary: #141414;--app-secondary2: #141414;--app-highlight: #cccccc;--app-highlight2: #696969;--app-highlight3: #212121;--app-border: #cccccc;--app-slider: #999999;--app-slider-active: #ffffff;--app-button: #333333;--components-bg: #000000;--components-secondary: #141414;--components-highlight: #696969}.voicememo-container{position:relative;height:100%;display:flex;flex-direction:column;align-items:center;background-color:var(--app-bg)}.voicememo-container::-webkit-scrollbar{display:none}.voicememo-container .voicememo-wrapper{position:relative;height:100%;width:100%;display:flex;flex-direction:column;align-items:center;gap:.5rem}.voicememo-container .voicememo-wrapper .title{font-size:28px;font-weight:600;font-style:bold;margin-right:auto;margin-left:2rem;margin-top:4.5rem;color:var(--phone-text-primary)}.voicememo-container .voicememo-wrapper .searchbox{width:80%;background-color:var(--app-secondary);padding:.5rem 1rem;border-radius:12px}.voicememo-container .voicememo-wrapper .voicememo-items{display:flex;flex-direction:column;align-items:center;gap:.8rem;width:100%;max-height:36rem;margin-top:1rem;overflow-y:auto;overflow-x:hidden}.voicememo-container .voicememo-wrapper .voicememo-items::-webkit-scrollbar{display:none}.voicememo-container .voicememo-wrapper .voicememo-items .voicememo-item{width:88%;display:flex;flex-direction:column;box-sizing:border-box;gap:.35rem;padding:.8rem 1rem;border-radius:12px;background-color:var(--app-secondary);cursor:pointer;transition:background-color .2s ease-in-out}.voicememo-container .voicememo-wrapper .voicememo-items .voicememo-item[data-expanded=false]:hover{background-color:#96969626}.voicememo-container .voicememo-wrapper .voicememo-items .voicememo-item>input{background:transparent;border:none;font-size:16px;font-weight:500;color:#f2f2f7;width:25ch}.voicememo-container .voicememo-wrapper .voicememo-items .voicememo-item>input:focus{outline:none}.voicememo-container .voicememo-wrapper .voicememo-items .voicememo-item .voicememo-title{font-size:17px;color:var(--phone-text-primary)}.voicememo-container .voicememo-wrapper .voicememo-items .voicememo-item .subtitle{color:var(--phone-text-secondary);font-weight:400;font-size:13px;margin-top:-.1rem}.voicememo-container .voicememo-wrapper .voicememo-items .voicememo-item .voicememo-info{width:100%;display:flex;justify-content:space-between;color:#6f6f6f;font-weight:400;font-size:14px}.voicememo-container .voicememo-wrapper .voicememo-items .voicememo-item .voicememo-info .voicememo-actions{width:100%;flex-direction:column;align-items:center;justify-content:center}.voicememo-container .voicememo-wrapper .voicememo-items .voicememo-item .voicememo-info .voicememo-actions .voicememo-duration-slider{display:flex;flex-direction:column;align-items:center;justify-content:center;gap:.5rem;width:100%;margin-top:.5rem}.voicememo-container .voicememo-wrapper .voicememo-items .voicememo-item .voicememo-info .voicememo-actions .voicememo-duration-slider input{width:100%;-webkit-appearance:none;-moz-appearance:none;appearance:none;background:transparent;outline:none;-webkit-transition:.2s;transition:opacity .2s;border-radius:20px;position:relative}.voicememo-container .voicememo-wrapper .voicememo-items .voicememo-item .voicememo-info .voicememo-actions .voicememo-duration-slider input::-webkit-slider-runnable-track{width:100%;height:.4rem;cursor:pointer}.voicememo-container .voicememo-wrapper .voicememo-items .voicememo-item .voicememo-info .voicememo-actions .voicememo-duration-slider input::-webkit-slider-thumb{-webkit-appearance:none;height:.75rem;width:.75rem;margin-top:-.15rem;background-color:var(--app-slider-active);border-radius:50%;cursor:pointer}.voicememo-container .voicememo-wrapper .voicememo-items .voicememo-item .voicememo-info .voicememo-actions .voicememo-duration-slider input:focus{outline:none}.voicememo-container .voicememo-wrapper .voicememo-items .voicememo-item .voicememo-info .voicememo-actions .voicememo-duration-slider .duration{width:100%;display:flex;align-items:center;justify-content:space-between;color:#6f6f6f;font-size:12px;font-weight:500}.voicememo-container .voicememo-wrapper .voicememo-items .voicememo-item .voicememo-info .voicememo-item-footer{display:flex;align-items:center;justify-content:space-between;margin-top:.75rem;width:100%}.voicememo-container .voicememo-wrapper .voicememo-items .voicememo-item .voicememo-info .voicememo-item-footer>div{flex:1}.voicememo-container .voicememo-wrapper .voicememo-items .voicememo-item .voicememo-info .voicememo-item-footer .controls{display:flex;align-items:center;justify-content:center;gap:1rem}.voicememo-container .voicememo-wrapper .voicememo-items .voicememo-item .voicememo-info .voicememo-item-footer .controls>span{display:flex;align-items:center;justify-content:center}.voicememo-container .voicememo-wrapper .voicememo-items .voicememo-item .voicememo-info .voicememo-item-footer .controls svg{color:var(--phone-text-primary);font-size:25px;cursor:pointer;transition:all .2s ease-in-out}.voicememo-container .voicememo-wrapper .voicememo-items .voicememo-item .voicememo-info .voicememo-item-footer .controls svg.disabled{cursor:not-allowed;opacity:.3}.voicememo-container .voicememo-wrapper .voicememo-items .voicememo-item .voicememo-info .voicememo-item-footer .controls svg:hover:not(.voicememo-container .voicememo-wrapper .voicememo-items .voicememo-item .voicememo-info .voicememo-item-footer .controls svg.disabled){filter:brightness(.6)}.voicememo-container .voicememo-wrapper .voicememo-items .voicememo-item .voicememo-info .voicememo-item-footer .controls svg:nth-child(2){font-size:40px}.voicememo-container .voicememo-wrapper .voicememo-items .voicememo-item .voicememo-info .voicememo-item-footer .delete,.voicememo-container .voicememo-wrapper .voicememo-items .voicememo-item .voicememo-info .voicememo-item-footer .share{font-size:26px;color:var(--phone-color-blue);cursor:pointer}.voicememo-container .voicememo-wrapper .voicememo-items .voicememo-item .voicememo-info .voicememo-item-footer .delete.delete,.voicememo-container .voicememo-wrapper .voicememo-items .voicememo-item .voicememo-info .voicememo-item-footer .share.delete{display:flex;justify-content:flex-end}.voicememo-container .voicememo-footer{width:100%;height:10rem;background-color:var(--app-secondary);display:flex;justify-content:center;box-sizing:border-box;padding-top:1rem}.voicememo-container .voicememo-footer .button-record{display:flex;align-items:center;justify-content:center;width:4.25rem;height:4.25rem;aspect-ratio:1/1;border-radius:50%;border:3px solid var(--app-border);cursor:pointer}.voicememo-container .voicememo-footer .button-record .button-inner{height:93%;width:93%;border-radius:50%;background-color:#f54140;transition:all .2s ease-in-out}.voicememo-container .voicememo-footer .button-record .button-inner[data-recording=true]{border-radius:10%;height:50%;width:50%}.voicememo-container .voicememo-footer .button-record .button-inner:hover:not([data-recording=true]){height:87%;width:87%;opacity:.7}
