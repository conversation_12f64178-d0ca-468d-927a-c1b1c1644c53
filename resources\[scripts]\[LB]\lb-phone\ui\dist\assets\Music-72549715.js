import{u as m,r as C,G as Y,q as p,j as n,m as _,a as s,I as H,C as O,L as d,a5 as v,y as b,A as B,bT as x,bU as g,F as R,ax as Z,Q as W,R as w,bN as K,bV as y,aU as ee,d as V,bw as se,bx as te,bW as ie,P as j,aD as ne,b4 as le,s as X,bX as ae,bY as ce,bD as re,bZ as oe,z as de,x as ue,J as E,O as z,$ as he,a$ as me,b_ as Se,S as Q,b$ as ve,c0 as ge}from"./index-a04bc7c5.js";function Ce(){const t=m(B),e=m(L),o=m(I),[r,a]=C.useState([]);C.useEffect(()=>{Y("Music")&&p("Music",{action:"getPlaylists"}).then(i=>{i&&a(i.map(c=>({...c,Songs:c.Songs.map(h=>{var S;let u=o.Songs.find(f=>f.Path===h);return{...u,Cover:((S=o.Albums[u==null?void 0:u.Album])==null?void 0:S.Cover)??(u==null?void 0:u.Cover),Path:h}})})))})},[t==null?void 0:t.active]);const l=(i,c)=>{!i||!c||p("Music",{action:"addSong",id:i,song:c},!0).then(h=>{h&&L.set(null)})};return n(_.div,{className:"add-playlist-container",initial:{opacity:0,y:"100%"},animate:{opacity:1,y:0},exit:{opacity:1,y:"100%"},children:[s("div",{className:"music-header",children:s("div",{className:"left",children:s(H,{onClick:()=>L.set(null)})})}),s("div",{className:"music-body",children:s("div",{className:"library",children:r.map((i,c)=>{var h;return n("div",{className:"playlist",onClick:()=>{i.Songs.includes(e.Path)?O.PopUp.set({title:d("APPS.MUSIC.SONG_EXISTS_POPUP.TITLE"),description:d("APPS.MUSIC.SONG_EXISTS_POPUP.DESCRIPTION"),buttons:[{title:d("APPS.MUSIC.SONG_EXISTS_POPUP.CANCEL")}]}):l(i.Id,e.Path)},children:[n("div",{className:"playlist-details",children:[i.Cover?s(v,{src:i.Cover}):i.Songs.length>0?s("div",{className:"playlist-cover","data-grid":i.Songs.filter(u=>u.Cover).length>=4,children:i.Songs.filter(u=>u.Cover).length>=4?i.Songs.filter((u,S)=>S<4).map((u,S)=>s(v,{src:u.Cover})):s(v,{src:i.Songs[0].Cover})}):s(v,{src:"./assets/img/icons/music/unknown.png"}),n("div",{className:"text",children:[s("div",{className:"title",children:i.Title}),n("div",{className:"subtitle",children:[i.Songs.length," ",(h=d("APPS.MUSIC.SONGS"))==null?void 0:h.toLowerCase()]})]})]}),s(b,{})]},c)})})})]})}function Pe(){const t=m(g),e=m(P);return s("div",{className:"music-body",children:n("div",{className:"artist-profile",children:[n("div",{className:"avatar",style:{backgroundImage:`url(${e.Avatar})`},children:[s("div",{className:"top",children:s(H,{onClick:()=>A.set("search")})}),s("div",{className:"title",children:e.Name})]}),n("div",{className:"category",children:[n("div",{className:"title",children:[d("APPS.MUSIC.SONGS"),s(b,{})]}),s("section",{children:e.songs.map((o,r)=>{var l;let a=((l=t==null?void 0:t.song)==null?void 0:l.Path)===o.Path;return n("div",{className:"song","data-playing":a,onClick:()=>{U({song:o,queue:[...e.songs.filter((i,c)=>c>r)]})},children:[n("div",{className:"song-info",children:[s(v,{src:o.Cover,fallback:"./assets/img/icons/music/unknown.png"}),n("div",{className:"text",children:[s("div",{className:"title",children:o.Title}),o.Album&&s("div",{className:"subtitle",children:o.Album})]})]}),s(x,{onClick:i=>{i.stopPropagation(),q(o)}})]},r)})})]}),n("div",{className:"category",children:[n("div",{className:"title",children:[d("APPS.MUSIC.ALBUMS"),s(b,{})]}),s("section",{className:"albums",children:e.albums.map((o,r)=>n("div",{className:"album",onClick:()=>{$(o.Title,"Albums")},children:[s(v,{src:o.Cover,alt:""}),s("div",{className:"title",children:o.Title})]},r))})]})]})})}function Ne(){const t=m(B),[e,o]=C.useState(""),r=m(I),[a,l]=C.useState([]);return C.useEffect(()=>{Y("Music")&&p("Music",{action:"getPlaylists"}).then(i=>{i&&l(i.map(c=>({...c,Songs:c.Songs.map(h=>{var S;let u=r.Songs.find(f=>f.Path===h);return{...u,Cover:((S=r.Albums[u==null?void 0:u.Album])==null?void 0:S.Cover)??(u==null?void 0:u.Cover),Path:h}})})))})},[t==null?void 0:t.active]),n(R,{children:[s("div",{className:"music-header",children:n("div",{className:"space",children:[s("div",{className:"title",children:d("APPS.MUSIC.LIBRARY")}),s(Z,{onClick:()=>{O.PopUp.set({title:d("APPS.MUSIC.NEW_PLAYLIST_POPUP.TITLE"),description:d("APPS.MUSIC.NEW_PLAYLIST_POPUP.DESCRIPTION"),input:{placeholder:d("APPS.MUSIC.NEW_PLAYLIST_POPUP.PLACEHOLDER"),type:"text",minCharacters:1,onChange:i=>o(i)},buttons:[{title:d("APPS.MUSIC.NEW_PLAYLIST_POPUP.CANCEL")},{title:d("APPS.MUSIC.NEW_PLAYLIST_POPUP.PROCEED"),cb:()=>{o(i=>(p("Music",{action:"createPlaylist",name:i},Math.random()*1e6).then(c=>{c&&l(h=>[...h??[],{id:c,Title:i,Songs:[]}])}),i))}}]})}})]})}),s("div",{className:"music-body",children:s("div",{className:"library",children:a&&a.map((i,c)=>n("div",{className:"playlist",onClick:()=>$(i,"playlist",!0),children:[n("div",{className:"playlist-details",children:[i.Cover?s(v,{src:i.Cover}):i.Songs.length>0?s("div",{className:"playlist-cover","data-grid":i.Songs.filter(h=>h.Cover).length>=4,children:i.Songs.filter(h=>h.Cover).length>=4?i.Songs.filter((h,u)=>u<4).map((h,u)=>s(v,{src:h.Cover})):s(v,{src:i.Songs[0].Cover})}):s("img",{src:"./assets/img/icons/music/unknown.png"}),n("div",{className:"text",children:[s("div",{className:"title",children:i.Title}),n("div",{className:"subtitle",children:[i.Songs.length," songs"]})]})]}),s(b,{})]},c))})})]})}function Ae(){var r,a,l,i;const t=m(g),e=m(A);m(k);let o=[{icon:s(K,{}),value:"home",label:d("APPS.MUSIC.HOME")},{icon:s(w,{}),value:"library",label:d("APPS.MUSIC.LIBRARY")},{icon:s(y,{}),value:"search",label:d("APPS.MUSIC.SEARCH")}];return n("div",{className:"music-footer",children:[n("div",{className:"music-playing",onClick:()=>{t&&k.set(!0)},children:[n("div",{className:"song-info",children:[s(v,{src:(r=t==null?void 0:t.song)==null?void 0:r.Cover,fallback:"./assets/img/icons/music/unknown.png"}),n("div",{className:"text",children:[s("div",{className:"title",children:((a=t==null?void 0:t.song)==null?void 0:a.Title)??d("APPS.MUSIC.NOT_PLAYING")}),((l=t==null?void 0:t.song)==null?void 0:l.Artist)&&s("div",{className:"subtitle",children:(i=t==null?void 0:t.song)==null?void 0:i.Artist})]})]}),s("div",{className:"controls",onClick:c=>{c.stopPropagation(),t&&g.patch({playing:!t.playing})},children:t&&t.playing?s(W,{}):s(w,{})})]}),s("div",{className:"items",children:o.map((c,h)=>n("div",{className:"item","data-active":e===c.value,onClick:()=>A.set(c.value),children:[c.icon,c.label]},h))})]})}function fe(){var u,S,f,G;const t=m(j.Settings),e=m(g);m(k);const[o,r]=C.useState(!1),[a,l]=C.useState(null),[i,c]=C.useState("rgb(170, 170, 170)");C.useEffect(()=>{var N;e&&((N=e==null?void 0:e.song)!=null&&N.Cover)&&ee(e.song.Cover).then(M=>{M&&c(`
                    linear-gradient(0deg,
                        ${M.dominant} 0%,
                        ${M.average} 50%,
                        ${M.muted} 100%
                    )
                `)})},[(u=e==null?void 0:e.song)==null?void 0:u.Cover]);const h=N=>{if(!N)return"0:00";let M=Math.floor(N/60),D=Math.floor(N-M*60),J=D<10?"0"+D:D;return M+":"+J};return n(_.div,{className:"music-player",style:{background:i},initial:{y:"100%"},animate:{y:0},exit:{y:"100%"},transition:{duration:.3},children:[s("div",{className:"music-player-header",onClick:()=>k.set(!1)}),n("div",{className:"music-player-body",children:[s(v,{className:"cover",src:(S=e==null?void 0:e.song)==null?void 0:S.Cover,fallback:"./assets/img/icons/music/unknown.png"}),n("div",{className:"song-info",children:[s("div",{className:"title",children:((f=e==null?void 0:e.song)==null?void 0:f.Title)??d("APPS.MUSIC.NOT_PLAYING")}),s("div",{className:"subtitle",children:((G=e==null?void 0:e.song)==null?void 0:G.Artist)??""})]}),n("div",{className:"duration",children:[s(V,{type:"range",min:0,max:100,value:e?a||((e==null?void 0:e.current)??0/(e==null?void 0:e.duration)??0)*100:0,style:{background:e!=null&&e.current?`linear-gradient(to right, rgb(255, 255, 255) 0%, rgb(255, 255, 255) ${Math.floor(a||e.current/e.duration*100)}%, rgba(255, 255, 255, 0.3) ${Math.floor(a||e.current/e.duration*100)}%, rgba(255, 255, 255, 0.3) 100%)`:"rgba(255, 255, 255, 0.3)"},onMouseDown:()=>r(!0),onMouseUp:N=>{r(!1),a&&e&&(e.goTo(e.duration/100*a),l(null))},onChange:N=>l(N.target.value)}),n("div",{className:"time",children:[s("div",{className:"current",children:h(a?(e==null?void 0:e.duration)/100*a:e==null?void 0:e.current)}),s("div",{className:"total",children:h(e==null?void 0:e.duration)})]})]}),n("div",{className:"controls",children:[s(se,{onClick:()=>e==null?void 0:e.previous()}),s("div",{className:"play",onClick:()=>{e&&g.patch({playing:!e.playing})},children:e!=null&&e.playing?s(W,{}):s(w,{})}),s(te,{onClick:()=>e==null?void 0:e.next()})]}),n("div",{className:"volume",children:[s(ie,{}),s(V,{type:"range",min:0,max:1,step:.05,style:{background:`linear-gradient(to right, rgb(255, 255, 255) 0%, rgb(255, 255, 255) ${t.sound.volume*100}%, rgba(255, 255, 255, 0.3) ${t.sound.volume*100}%, rgba(255, 255, 255, 0.3) 100%)`},value:t.sound.volume,onChange:N=>{j.Settings.patch({sound:{...j.Settings.value.sound,volume:parseFloat(N.target.value)}})}}),s(ne,{})]})]})]})}function pe(){var l;const t=m(g);m(A);const e=m(P),[o]=C.useState(!!e.Artist),[r,a]=C.useState(!1);return n(R,{children:[s("div",{className:"music-header",children:n("div",{className:"space",children:[s(H,{onClick:()=>e.Artist?A.set("search"):A.set("library")}),!o&&(r?s(le,{onClick:()=>{p("Music",{action:"editPlaylist",id:e.Id,title:e.Title,cover:e.Cover},!0).then(i=>{if(!i)return X("warning","Failed to edit playlist");a(!1)})}}):s(ae,{onClick:i=>{i.stopPropagation(),O.ContextMenu.set({buttons:[{title:d("APPS.MUSIC.EDIT_PLAYLIST"),cb:()=>a(!0)},{title:d("APPS.MUSIC.DELETE_PLAYLIST"),color:"red",cb:()=>{p("Music",{action:"deletePlaylist",id:e.Id},!0).then(c=>{c&&A.set("library")})}}]})}}))]})}),s("div",{className:"music-body",children:n("div",{className:"playlist-wrapper",children:[n("div",{className:"playlist-details",children:[n("div",{className:"cover-wrapper",style:{cursor:r?"pointer":"default"},onClick:()=>{r&&O.Gallery.set({onSelect(i){P.patch({...e,Cover:i.src})}})},children:[e.Cover?s(v,{src:e.Cover}):e.Songs.length>0?s("div",{className:"playlist-cover","data-grid":e.Songs.filter(i=>i.Cover).length>=4,children:e.Songs.filter(i=>i.Cover).length>=4?e.Songs.filter((i,c)=>c<4).map((i,c)=>s(v,{src:i.Cover})):s(v,{src:e.Songs[0].Cover,fallback:"./assets/img/icons/music/unknown.png"})}):s("img",{src:"./assets/img/icons/music/unknown.png"}),n("div",{className:"actions",children:[r&&s(ce,{}),r&&e.Cover&&s(re,{onClick:i=>{i.stopPropagation(),P.patch({...e,Cover:null})}})]})]}),n("div",{className:"text",children:[r?s(V,{defaultValue:e.Title,onChange:i=>{if(i.target.value.length>30)return i.target.value=i.target.value.slice(0,30);P.patch({...e,Title:i.target.value})}}):s("div",{className:"title",children:e.Title}),s("div",{className:"subtitle",children:o?e.Artist:`${Object.keys(e.Songs).length} ${(l=d("APPS.MUSIC.SONGS"))==null?void 0:l.toLowerCase()}`})]}),n("div",{className:"buttons",children:[n("div",{className:"button",onClick:()=>{U({song:e.Songs[0],queue:[...e.Songs.filter((i,c)=>c>0)]})},children:[s(w,{})," ",d("APPS.MUSIC.PLAY")]}),n("div",{className:"button",onClick:()=>{U({song:e.Songs[Math.floor(Math.random()*e.Songs.length)],queue:[...e.Songs.filter((i,c)=>c>0)].sort(()=>Math.random()-.5)})},children:[s(oe,{}),d("APPS.MUSIC.SHUFFLE")]})]})]}),s("div",{className:"playlist-tracks",children:e.Songs.map((i,c)=>{var u;let h=((u=t==null?void 0:t.song)==null?void 0:u.Path)===i.Path;return n("div",{className:"track","data-album":o,"data-playing":h,onClick:()=>{U({song:i,queue:[...e.Songs.filter((S,f)=>f>c)]})},children:[n("div",{className:"track-info",children:[r&&s(de,{onClick:S=>{S.stopPropagation(),O.PopUp.set({title:d("APPS.MUSIC.REMOVE_SONG_POPUP.TITLE"),description:d("APPS.MUSIC.REMOVE_SONG_POPUP.DESCRIPTION"),buttons:[{title:d("APPS.MUSIC.REMOVE_SONG_POPUP.CANCEL")},{title:d("APPS.MUSIC.REMOVE_SONG_POPUP.PROCEED"),color:"red",cb:()=>{p("Music",{action:"removeSong",id:e.Id,song:i.Path},!0).then(f=>{f&&P.patch({...e,Songs:e.Songs.filter((G,N)=>N!==c)})})}}]})}}),o?s("div",{className:"index",children:c+1}):s(v,{src:i.Cover,fallback:"./assets/img/icons/music/unknown.png"}),n("div",{className:"text",children:[s("div",{className:"title",children:i.Title}),s("div",{className:"subtitle",children:o?e.Artist:i.Artist})]})]}),s(x,{onClick:S=>{S.stopPropagation(),q(i)}})]},c)})})]})})]})}function be(){const t=m(g),[e,o]=C.useState(""),r=m(I);return n(R,{children:[s("div",{className:"music-header",children:n("div",{className:"search",children:[s("div",{className:"title",children:d("APPS.MUSIC.SEARCH")}),s(ue,{placeholder:d("APPS.MUSIC.SEARCH_PLACEHOLDER"),onChange:a=>o(a.target.value)})]})}),s("div",{className:"music-body",children:s("div",{className:"search-results",children:e.length>0&&Object.keys(r).map(a=>Object.keys(r[a]).filter(l=>{switch(a){case"Artists":return l==null?void 0:l.toLowerCase().includes(e==null?void 0:e.toLowerCase());case"Albums":return l==null?void 0:l.toLowerCase().includes(e==null?void 0:e.toLowerCase());case"Songs":return r[a][l].Title.toLowerCase().includes(e.toLowerCase())}}).map((l,i)=>{var h;let c;return a==="Songs"&&(c=((h=t==null?void 0:t.song)==null?void 0:h.Path)===r[a][l].Path),n("div",{className:"item","data-playing":c,onClick:()=>{a==="Songs"?U({song:r[a][l],queue:[...r.Songs.filter((u,S)=>r[a][l].Path!==u.Path&&S>i)]}):$(l,a)},children:[n("div",{className:"item-info",children:[s(v,{src:a==="Artists"?r[a][l].Avatar:r[a][l].Cover,fallback:"./assets/img/icons/music/unknown.png","data-artist":a==="Artists"}),n("div",{className:"text",children:[s("div",{className:"title",children:a==="Songs"?r[a][l].Title:l}),n("div",{className:"subtitle",children:[a==="Artists"&&d("APPS.MUSIC.ARTIST"),a==="Albums"&&`${d("APPS.MUSIC.ALBUM")} • ${r[a][l].Artist}`,a==="Songs"&&`${d("APPS.MUSIC.SONG")} • ${r[a][l].Artist}`]})]})]}),s("div",{className:"item-actions",children:a==="Songs"?s(x,{onClick:u=>{u.stopPropagation(),q(r[a][l])}}):s(b,{})})]})}))})})]})}const Ie={Artists:{Future:{Avatar:"https://townsquare.media/site/812/files/2024/03/attachment-future-metro-boomin-we-dont-trust-you-photo.jpg?w=1080&q=75"}},Albums:[],Songs:{"music.mp3":{Title:"Like That",Artist:"Future",Cover:"https://townsquare.media/site/812/files/2024/03/attachment-future-metro-boomin-we-dont-trust-you-photo.jpg?w=1080&q=75"}}},T=E(null);function Me(){const t=m(g),e=m(I),o=m(T);return n(R,{children:[s(z,{children:o&&s(Ue,{category:o})}),s("div",{className:"music-header",children:s("div",{className:"space",children:s("div",{className:"title",children:d("APPS.MUSIC.HOME")})})}),s("div",{className:"music-body",children:n("div",{className:"home-page",children:[n("div",{className:"sections",children:[s("div",{className:"title",children:d("APPS.MUSIC.CATEGORIES")}),n("div",{className:"items",children:[n("div",{className:"section",onClick:()=>T.set("Artists"),children:[n("div",{className:"section-info",children:[s(he,{}),d("APPS.MUSIC.ARTISTS")]}),s(b,{})]}),n("div",{className:"section",onClick:()=>T.set("Albums"),children:[n("div",{className:"section-info",children:[s(me,{}),d("APPS.MUSIC.ALBUMS")]}),s(b,{})]}),n("div",{className:"section",onClick:()=>T.set("Songs"),children:[n("div",{className:"section-info",children:[s(Se,{}),d("APPS.MUSIC.SONGS")]}),s(b,{})]})]})]}),Object.keys((e==null?void 0:e.Songs)??{}).length>0&&n("div",{className:"recently-added",children:[s("div",{className:"title",children:d("APPS.MUSIC.RECENTLY_ADDED")}),s("div",{className:"items",children:Object.keys((e==null?void 0:e.Songs)??{}).slice(0,10).map((r,a)=>{var c;const l=e.Songs[r];let i=((c=t==null?void 0:t.song)==null?void 0:c.Path)===l.Path;return n("div",{className:"item","data-playing":i,onClick:()=>{U({song:l,queue:[...Object.values(e.Songs||{}).filter((h,u)=>h.Path!==l.Path&&u>a)]})},children:[s(v,{src:l.Cover,fallback:"./assets/img/icons/music/unknown.png"}),n("div",{className:"item-info",children:[s("div",{className:"song-title",children:l.Title}),s("div",{className:"song-artist",children:l.Artist})]})]})})})]})]})})]})}const Ue=({category:t})=>{const e=m(g),o=m(I),[r,a]=C.useState([]);return C.useEffect(()=>{a(Object.keys((o==null?void 0:o[t])??{}))},[t]),n(_.div,{...Q("right","list-view",.2),className:"list-view",children:[n("div",{className:"list-header",children:[s("div",{className:"back-button",children:s(ve,{onClick:()=>T.set(null)})}),s("div",{className:"title",children:t}),s("div",{})]}),s("div",{className:"search-results",children:r.map((l,i)=>{var h;let c;return t==="Songs"&&(c=((h=e==null?void 0:e.song)==null?void 0:h.Path)===o[t][l].Path),n("div",{className:"item","data-playing":c,onClick:()=>{t==="Songs"?U({song:o[t][l],queue:[...o.Songs.filter((u,S)=>o[t][l].Path!==u.Path&&S>i)]}):$(l,t)},children:[n("div",{className:"item-info",children:[s(v,{src:t==="Artists"?o[t][l].Avatar:o[t][l].Cover,fallback:"./assets/img/icons/music/unknown.png","data-artist":t==="Artists"}),n("div",{className:"text",children:[s("div",{className:"title",children:t==="Songs"?o[t][l].Title:l}),n("div",{className:"subtitle",children:[t==="Artists"&&d("APPS.MUSIC.ARTIST"),t==="Albums"&&`${d("APPS.MUSIC.ALBUM")} • ${o[t][l].Artist}`,t==="Songs"&&`${d("APPS.MUSIC.SONG")} • ${o[t][l].Artist}`]})]})]}),s("div",{className:"item-actions",children:t==="Songs"?s(x,{onClick:u=>{u.stopPropagation(),q(o[t][l])}}):s(b,{})})]})})})]})};const A=E("home"),F=E(null),I=E(null),P=E(null),k=E(!1),L=E(null);function Oe(){const t=m(A),e=m(F);m(I),m(P);const o=m(k),r=m(L),a={home:s(Me,{}),search:s(be,{}),artist:s(Pe,{}),library:s(Ne,{}),playlist:s(pe,{})};return C.useEffect(()=>{Y("Music")&&p("Music",{action:"getConfig"},Ie).then(l=>{if(!l)return;let i=Object.keys(l.Songs).map(c=>({...l.Songs[c],Cover:l.Songs[c].Album&&l.Albums[l.Songs[c].Album]?l.Albums[l.Songs[c].Album].Cover:l.Songs[c].Cover,Path:c}));I.set({...l,Songs:i})})},[]),C.useEffect(()=>{F.set(t)},[t]),n("div",{className:"music-container",children:[n(z,{initial:!1,children:[o&&s(fe,{}),r&&s(Ce,{})]}),s(_.div,{className:"music-content",...Q(ge(["home","library","search","artist","playlist"],e,t),t,.2),children:a[t]}),s(Ae,{})]})}const U=t=>{var e,o;!t||!t.song||(g.value?g.patch({playing:!0,song:t.song,queue:t.queue??((e=g.value)==null?void 0:e.queue)??[]}):g.set({playing:!0,song:t.song,queue:t.queue??((o=g.value)==null?void 0:o.queue)??[]}))},$=(t,e,o=!1)=>{const r=I.value;if(t&&!(!r[e]&&!o))if(e==="Albums"){const a=t;let l=Object.keys(r.Songs).filter(i=>r.Songs[i].Album===a).map(i=>r.Songs[i]);P.set({...r[e][a],Songs:l,Title:a}),A.set("playlist")}else if(e==="Artists"){const a=t;let l=Object.keys(r.Songs).filter(c=>r.Songs[c].Artist===a).map(c=>r.Songs[c]),i=Object.keys(r.Albums).filter(c=>r.Albums[c].Artist===a).map(c=>({...r.Albums[c],Title:c}));P.set({...r[e][a],Name:a,songs:l,albums:i}),A.set("artist")}else e==="playlist"&&(P.set(t),A.set("playlist"))},q=t=>{const e=P.value;if(!t)return X("warning","No song data found, can't set context menu.");let o=[{title:d("APPS.MUSIC.ADD_QUEUE"),color:null,cb:()=>g.patch({queue:[...g.value.queue,t]})},{title:d("APPS.MUSIC.ADD_PLAYLIST"),color:null,cb:()=>L.set(t)}];e&&(e!=null&&e.IsOwner)&&o.push({title:d("APPS.MUSIC.REMOVE_SONG"),color:"red",cb:()=>{p("Music",{action:"removeSong",id:e.Id,song:t.Path},!0).then(r=>{r&&P.set({...P.value,Songs:P.value.Songs.filter(a=>a.Path!==t.Path)})})}}),O.ContextMenu.set({buttons:o})};export{I as Data,F as PrevView,P as SelectedMusicData,L as ShowAddPlaylist,k as ShowMusicPlayer,A as View,Oe as default,$ as fetchAndSet,U as play,q as setContextMenu};
