:root{--phone-color-primary: rgb(255, 255, 255);--phone-color-opacity: rgba(242, 242, 242, .4);--phone-color-opacity2: rgb(30, 30, 30, .5);--phone-color-highlight: rgb(250, 250, 250);--phone-color-highlight2: rgb(240, 240, 240);--phone-color-highlight3: rgb(220, 220, 220);--phone-highlight-opacity15: rgba(145, 145, 145, .15);--phone-highlight-opacity35: rgba(145, 145, 145, .35);--phone-highlight-opacity45: rgba(145, 145, 145, .45);--phone-highlight-opacity55: rgba(145, 145, 145, .55);--phone-color-input: rgba(241, 241, 241, .656);--phone-text-primary: rgb(0, 0, 0);--phone-text-secondary: rgb(142, 142, 147);--phone-color-hover: rgb(240, 240, 240);--phone-color-border: rgba(200, 200, 200, .4);--phone-color-grey: #8e8e93;--phone-color-blue: #0a84ff;--phone-color-green: #32d74b;--phone-color-green-secondary: #092911;--phone-color-red: #ff3b30;--phone-color-orange: rgb(255, 157, 10);--phone-color-yellow: #cca250;--phone-color-pink: #ff3b30;--instagram-primary: #ffffff;--instagram-blue: rgb(0, 149, 246);--instagram-red: rgb(237, 73, 86);--instagram-primary-text: rgb(38, 38, 38);--instagram-secondary-text: rgb(142, 142, 142);--instagram-stroke: rgb(219, 219, 219);--instagram-border: rgb(219, 219, 219);--instagram-highlight: rgb(239, 239, 239);--tinder-color-pink: #ff4573;--tinder-color-orange: #ff5f65;--tinder-color-mix: #f5547c;--twitter-primary: #f5f8fa;--twitter-secondary: #14171a;--twitter-background-highlight: rgb(239, 243, 244);--twitter-primary-text: #14171a;--twitter-secondary-text: #657786;--twitter-alt-text: #657786;--twitter-border: #bdc5cd75;--twitter-border-secondary: #1d9bf0;--twitter-highlight: #1d9bf0;--twitter-hover: rgba(15, 20, 25, .1);--twitter-action: #14171a;--twitter-blue: #1d9bf0;--tiktok-primary: #ffffff;--tiktok-secondary: #000000;--tiktok-text-primary: #000000;--tiktok-text-secondary: #86878b;--tiktok-color-border: #d0d1d3;--tiktok-color-pink: #fe2c55;--tiktok-color-aqua: #00f2ea;--tiktok-color-yellow: #f8cd14;--tiktok-color-blue: #479fc5;--tiktok-color-unread: rgba(254, 44, 86, .2);--crypto-color-primary: rgb(255, 255, 255);--browser-primary: rgb(245, 245, 245);--browser-secondary: rgba(153, 153, 153, .15);--browser-gradient: linear-gradient(230deg, #f4d6ff, #c5f1ff);--browser-footer: rgba(255, 255, 255, .75);--browser-border: rgba(102, 102, 102, .75);--browser-text-secondary: #696969;--controlcentre-opacity: rgba(255, 255, 255, .15);--controlcentre-opacity2: rgba(255, 255, 255, .2);--controlcentre-active: rgba(255, 255, 255, .5);--notification-primary: rgba(215, 215, 215, .5);--notification-secondary: rgba(215, 215, 215, .1);--lockscreeneditor-background: rgba(255, 255, 255, .75);--lockscreeneditor-secondary: #d9d9d9;--app-bg: #ececec;--app-bg2: #ffffff;--app-secondary: #ffffff;--app-secondary2: #ececec;--app-highlight: #cccccc;--app-highlight2: #999999;--app-highlight3: #ffffff;--app-border: #666666;--app-slider: #cccccc;--app-slider-active: #333333;--app-button: #ffffff;--components-bg: #eeeeee;--components-secondary: #ffffff;--components-highlight: #cccccc}[data-theme=dark]{--phone-color-primary: #000000;--phone-color-opacity: rgb(30, 30, 30, .5);--phone-color-opacity2: rgba(242, 242, 242, .4);--phone-color-highlight: rgb(15, 15, 15);--phone-color-highlight2: rgb(20, 20, 20);--phone-color-highlight3: rgb(25, 25, 25);--phone-highlight-opacity35: rgba(145, 145, 145, .35);--phone-highlight-opacity45: rgba(50, 50, 50, .6);--phone-highlight-opacity55: rgb(60, 60, 60, .8);--phone-color-input: rgba(60, 60, 67, .6);--phone-text-primary: #f2f2f7;--phone-text-secondary: #6f6f6f;--phone-color-grey: #636366;--phone-color-hover: rgb(30, 30, 30);--phone-color-border: rgba(150, 150, 150, .2);--phone-color-blue: #076bcf;--instagram-primary: #000000;--instagram-blue: rgb(0, 149, 246);--instagram-red: rgb(237, 73, 86);--instagram-primary-text: rgb(250, 250, 250);--instagram-secondary-text: rgb(142, 142, 142);--instagram-stroke: rgb(219, 219, 219);--instagram-border: rgb(54, 54, 54);--instagram-highlight: rgb(38, 38, 38);--twitter-primary: #000000;--twitter-secondary: #f5f8fa;--twitter-background-highlight: rgb(20, 20, 20);--twitter-primary-text: #f5f8fa;--twitter-secondary-text: #aab8c2;--twitter-alt-text: #657786;--twitter-border: #38444d;--twitter-border-secondary: #38444d;--twitter-hover: rgba(150, 150, 150, .1);--twitter-highlight: #dcdcdc;--twitter-action: #1d9bf0;--twitter-blue: #1d9bf0;--crypto-color-primary: rgb(24, 26, 32);--tiktok-text-primary: #f2f2f7;--tiktok-text-secondary: #6f6f6f;--tiktok-color-border: #96969633;--browser-primary: rgb(15, 15, 15);--browser-secondary: rgba(153, 153, 153, .15);--browser-gradient: linear-gradient(230deg, #453b48, #2f393d);--browser-footer: rgba(51, 51, 51, .75);--browser-border: rgba(102, 102, 102, .75);--browser-text-secondary: #999999;--controlcentre-opacity: rgba(0, 0, 0, .15);--controlcentre-opacity2: rgba(0, 0, 0, .2);--controlcentre-active: rgba(0, 0, 0, .5);--notification-primary: rgba(0, 0, 0, .1);--notification-secondary: rgba(0, 0, 0, .12);--lockscreeneditor-background: rgba(0, 0, 0, .8);--lockscreeneditor-secondary: #333333;--app-bg: #000000;--app-bg2: #000000;--app-secondary: #141414;--app-secondary2: #141414;--app-highlight: #cccccc;--app-highlight2: #696969;--app-highlight3: #212121;--app-border: #cccccc;--app-slider: #999999;--app-slider-active: #ffffff;--app-button: #333333;--components-bg: #000000;--components-secondary: #141414;--components-highlight: #696969}@keyframes zoomIn{0%{transform:scale(.5)}to{transform:scale(1)}}@keyframes zoomOut{0%{transform:scale(1.5)}to{transform:scale(1)}}@keyframes slideDown{0%{transform:translateY(-20%)}to{transform:translateY(0)}}@keyframes slideUp{0%{transform:translateY(40%)}to{transform:translateY(0)}}@keyframes slideRight{0%{transform:translate(-10%)}to{transform:translate(0)}}@keyframes slideLeft{0%{transform:translate(10%)}to{transform:translate(0)}}@keyframes appJiggle{0%{transform:rotate(-1deg);animation-timing-function:ease-in}50%{transform:rotate(1.5deg);animation-timing-function:ease-out}}@keyframes appJiggle2{0%{transform:rotate(1deg);animation-timing-function:ease-in}50%{transform:rotate(-1.5deg);animation-timing-function:ease-out}}@keyframes widgetJiggle{0%{transform:rotate(-.5deg);animation-timing-function:ease-in}50%{transform:rotate(.5deg);animation-timing-function:ease-out}}@keyframes widgetJiggle2{0%{transform:rotate(.5deg);animation-timing-function:ease-in}50%{transform:rotate(-.5deg);animation-timing-function:ease-out}}.phone-app-container{display:flex;flex-direction:column;align-items:center;justify-content:flex-start;height:100%;background-color:var(--app-bg)}.phone-app-container .wrapper{width:100%;height:88%;display:flex;flex-direction:column}.phone-app-container .wrapper .animation-container{height:100%;width:100%;display:flex;flex-direction:column}.phone-app-container .wrapper .contacts-header{display:flex;flex-direction:column;align-items:center;gap:1rem;padding:0 6%;margin-top:4.5rem}.phone-app-container .wrapper .contacts-header .disabled{color:var(--phone-text-secondary)}.phone-app-container .wrapper .contacts-header span{cursor:pointer;color:var(--phone-color-blue);font-size:18px}.phone-app-container .wrapper .contacts-header span svg{font-size:15px}.phone-app-container .wrapper .contacts-header span.disabled{color:var(--phone-text-secondary)}.phone-app-container .wrapper .contacts-header .back{color:var(--phone-color-blue);display:flex;align-items:center;font-size:18px;cursor:pointer}.phone-app-container .wrapper .contacts-header .searchbox{background-color:var(--app-secondary);padding:.35rem 1rem;border-radius:12px}.phone-app-container .wrapper .contacts-header .items{display:flex;flex-direction:row;justify-content:space-between;align-items:center;width:100%}.phone-app-container .wrapper .contacts-header .items.col{flex-direction:column;align-items:flex-start;gap:.25rem}.phone-app-container .wrapper .contacts-header .items .title{color:var(--phone-text-primary);font-size:30px;font-weight:600;font-style:bold}.phone-app-container .wrapper .contacts-header .items svg{font-size:30px;color:var(--phone-color-blue);cursor:pointer}.phone-app-container .wrapper .contacts-header .items svg:hover{filter:brightness(.7)}.phone-app-container .wrapper .contacts-header .items .selector-container{width:100%;display:flex;align-items:center;justify-content:center}.phone-app-container .wrapper .contacts-header .items .selector-container div{box-sizing:border-box}.phone-app-container .wrapper .contacts-header .items .selector-container .selector{display:flex;flex-direction:row;gap:.1rem;background-color:var(--app-secondary);border-radius:6px;padding:.25rem .3rem}.phone-app-container .wrapper .contacts-header .items .selector-container .selector .option{display:flex;align-items:center;justify-content:center;width:100%;height:100%;padding:.3rem 1.5rem;color:var(--phone-text-primary);font-size:14px;cursor:pointer;transition:all .2s ease-in-out;border-radius:6px}.phone-app-container .wrapper .contacts-header .items .selector-container .selector .option:hover{filter:brightness(.5)}.phone-app-container .wrapper .contacts-header .items .selector-container .selector .option[data-active=true]{background-color:var(--phone-highlight-opacity45)}.phone-app-container .wrapper .content{max-height:100%;overflow:auto}.phone-app-container .wrapper .content.noscroll{overflow:hidden}.phone-app-container .wrapper .content::-webkit-scrollbar{display:none}.phone-app-container .wrapper .content .contact-list{margin:0rem 5%}.phone-app-container .wrapper .content .contact-list .contacts{background-color:var(--app-secondary);border-radius:15px}.phone-app-container .wrapper .content .keypad-container{height:98%}.phone-app-container .wrapper .content .keypad-container .inputbox{display:flex;flex-direction:column;align-items:center;gap:.5rem;margin-top:5rem}.phone-app-container .wrapper .content .keypad-container .inputbox .input{text-align:center;height:4rem;width:100%;color:var(--phone-text-primary)}.phone-app-container .wrapper .content .keypad-container .inputbox span{font-size:20px;color:var(--phone-color-blue);cursor:pointer;height:1.5rem}.phone-app-container .wrapper .content .keypad-container .keypad-wrapper{display:flex;flex-direction:column;justify-content:center;align-items:center}.phone-app-container .wrapper .content .keypad-container .keypad-wrapper .keypad{padding:2rem;display:grid;align-items:center;grid-template-columns:repeat(3,1fr);-ms-grid-columns:repeat(3,1fr);gap:1rem 2rem}.phone-app-container .wrapper .content .keypad-container .keypad-wrapper .keypad div:not(.delete){display:flex;flex-direction:column;align-items:center;justify-content:center;background-color:var(--app-secondary);color:var(--phone-text-primary);border-radius:50%;height:5rem;width:5rem;text-align:center;font-size:32px;font-family:Roboto;font-weight:400}.phone-app-container .wrapper .content .keypad-container .keypad-wrapper .keypad div:not(.delete):hover{cursor:pointer;background-color:var(--phone-highlight-opacity55)}.phone-app-container .wrapper .content .keypad-container .keypad-wrapper .keypad div:not(.delete) span{font-size:12px;font-weight:400;letter-spacing:3px}.phone-app-container .wrapper .content .keypad-container .keypad-wrapper .keypad div:not(.delete).call{color:#fff;background-color:var(--phone-color-green)}.phone-app-container .wrapper .content .keypad-container .keypad-wrapper .keypad div:not(.delete).call svg{font-size:33px}.phone-app-container .wrapper .content .keypad-container .keypad-wrapper .keypad div:not(.delete).call:hover{filter:brightness(.9)}.phone-app-container .wrapper .content .keypad-container .keypad-wrapper .keypad .delete{display:flex;align-items:center;justify-content:center}.phone-app-container .wrapper .content .keypad-container .keypad-wrapper .keypad .delete svg{font-size:40px;color:var(--phone-text-primary);cursor:pointer}.phone-app-container .wrapper .content .keypad-container .keypad-wrapper .keypad .delete svg:hover{filter:brightness(.7)}.phone-app-container .wrapper .content .favourite{display:flex;flex-direction:column;margin:1rem 5%;background-color:var(--app-secondary);border-radius:15px}.phone-app-container .wrapper .content .favourite .item{display:flex;justify-content:space-between;align-items:center;padding:.7rem 5%;cursor:pointer;color:var(--phone-text-primary);border-bottom:1px solid var(--phone-color-border)}.phone-app-container .wrapper .content .favourite .item:last-child{border-bottom:none}.phone-app-container .wrapper .content .favourite .item .user{display:flex;align-items:center;gap:.5rem;transition:all .3s ease-in-out}.phone-app-container .wrapper .content .favourite .item .user svg{font-size:28px;color:var(--phone-color-red);font-weight:600;margin-right:.25rem}.phone-app-container .wrapper .content .favourite .item .user .info{display:flex;flex-direction:column;font-size:18px;font-weight:400}.phone-app-container .wrapper .content .favourite .item .user .info span{color:#8e8e93;font-size:14px;font-weight:400}.phone-app-container .wrapper .content .favourite .item svg{margin-right:.1rem;font-size:28px;font-weight:300;color:var(--phone-color-blue)}.phone-app-container .wrapper .content .favourite .item svg:hover{filter:brightness(.7)}.phone-app-container .wrapper .content .voicemails{display:flex;flex-direction:column;align-items:center;gap:.75rem;margin-top:1rem}.phone-app-container .wrapper .content .voicemails .voicemail-item{display:flex;flex-direction:column;gap:.2rem;width:90%;box-sizing:border-box;padding:.7rem 1rem;border-radius:12px;background-color:var(--app-secondary);cursor:pointer;transition:background-color .2s ease-in-out}.phone-app-container .wrapper .content .voicemails .voicemail-item[data-expanded=false]:hover{background-color:#96969626}.phone-app-container .wrapper .content .voicemails .voicemail-item>input{background:transparent;border:none;font-size:16px;font-weight:500;color:#f2f2f7;width:25ch}.phone-app-container .wrapper .content .voicemails .voicemail-item>input:focus{outline:none}.phone-app-container .wrapper .content .voicemails .voicemail-item .voicemail-info{width:100%;display:flex;flex-direction:column;color:var(--phone-text-secondary);font-weight:400;font-size:14px}.phone-app-container .wrapper .content .voicemails .voicemail-item .voicemail-info .voicemail-row{width:100%;display:flex;justify-content:space-between}.phone-app-container .wrapper .content .voicemails .voicemail-item .voicemail-info .voicemail-row .voicemail-title{font-size:16px;color:var(--phone-text-primary)}.phone-app-container .wrapper .content .voicemails .voicemail-item .voicemail-info .voicemail-row .subtitle{color:var(--phone-text-secondary);font-weight:400;font-size:13px;margin-top:-.1rem}.phone-app-container .wrapper .content .voicemails .voicemail-item .voicemail-info .voicemail-row .details{display:flex;flex-direction:column;gap:.3rem}.phone-app-container .wrapper .content .voicemails .voicemail-item .voicemail-info .voicemail-row .info{display:flex;flex-direction:column;justify-content:flex-end;align-items:flex-end}.phone-app-container .wrapper .content .voicemails .voicemail-item .voicemail-info .voicemail-row .info .date{color:var(--phone-text-primary);font-size:15px;font-weight:400}.phone-app-container .wrapper .content .voicemails .voicemail-item .voicemail-info .voicemail-row .info .duration{color:var(--phone-text-secondary);font-size:13px;font-weight:400}.phone-app-container .wrapper .content .voicemails .voicemail-item .voicemail-info .voicemail-actions{width:100%;flex-direction:column;align-items:center;justify-content:center}.phone-app-container .wrapper .content .voicemails .voicemail-item .voicemail-info .voicemail-actions .voicemail-duration-slider{display:flex;flex-direction:column;align-items:center;justify-content:center;gap:.5rem;width:100%;margin-top:.5rem}.phone-app-container .wrapper .content .voicemails .voicemail-item .voicemail-info .voicemail-actions .voicemail-duration-slider input{width:100%;-webkit-appearance:none;-moz-appearance:none;appearance:none;background:transparent;outline:none;-webkit-transition:.2s;transition:opacity .2s;border-radius:20px;position:relative}.phone-app-container .wrapper .content .voicemails .voicemail-item .voicemail-info .voicemail-actions .voicemail-duration-slider input::-webkit-slider-runnable-track{width:100%;height:.4rem;cursor:pointer}.phone-app-container .wrapper .content .voicemails .voicemail-item .voicemail-info .voicemail-actions .voicemail-duration-slider input::-webkit-slider-thumb{-webkit-appearance:none;height:.85rem;width:.85rem;margin-top:-.25rem;background-color:var(--app-slider-active);border-radius:50%;cursor:pointer}.phone-app-container .wrapper .content .voicemails .voicemail-item .voicemail-info .voicemail-actions .voicemail-duration-slider input:focus{outline:none}.phone-app-container .wrapper .content .voicemails .voicemail-item .voicemail-info .voicemail-actions .voicemail-duration-slider .duration{width:100%;display:flex;align-items:center;justify-content:space-between;color:var(--phone-text-secondary);font-size:12px;font-weight:500}.phone-app-container .wrapper .content .voicemails .voicemail-item .voicemail-info .voicemail-item-footer{display:flex;align-items:center;justify-content:space-between;margin-top:.5rem;width:100%}.phone-app-container .wrapper .content .voicemails .voicemail-item .voicemail-info .voicemail-item-footer .play{cursor:pointer}.phone-app-container .wrapper .content .voicemails .voicemail-item .voicemail-info .voicemail-item-footer .play svg{font-size:16px;color:#fff;padding:.45rem;border-radius:50%;background-color:var(--phone-color-blue)}.phone-app-container .wrapper .content .voicemails .voicemail-item .voicemail-info .voicemail-item-footer .buttons{display:flex;align-items:center;gap:.5rem}.phone-app-container .wrapper .content .voicemails .voicemail-item .voicemail-info .voicemail-item-footer .buttons svg{font-size:16px;color:#fff;padding:.45rem;border-radius:50%}.phone-app-container .wrapper .content .voicemails .voicemail-item .voicemail-info .voicemail-item-footer .buttons svg.red{background-color:var(--phone-color-red)}.phone-app-container .wrapper .content .voicemails .voicemail-item .voicemail-info .voicemail-item-footer .buttons svg.green{background-color:var(--phone-color-green)}.phone-app-container .wrapper .content .voicemails .voicemail-item .voicemail-info .voicemail-item-footer .buttons svg.blue{background-color:var(--phone-color-blue)}.phone-app-container .wrapper .content .recent-calls{display:flex;flex-direction:column;margin:.5rem 5%;background-color:var(--app-secondary);border-radius:15px}.phone-app-container .wrapper .content .recent-calls .item{display:flex;justify-content:space-between;align-items:center;padding:.7rem 5%;cursor:pointer;border-top:1px solid var(--phone-color-border)}.phone-app-container .wrapper .content .recent-calls .item:first-child{border-top:none}.phone-app-container .wrapper .content .recent-calls .item[data-missed=true] .user .info{color:var(--phone-color-red)}.phone-app-container .wrapper .content .recent-calls .item .user{display:flex;align-items:center;gap:.5rem}.phone-app-container .wrapper .content .recent-calls .item .user .info{display:flex;align-items:flex-start;flex-direction:column;gap:.1rem;font-size:18px;font-weight:400;color:var(--phone-text-primary)}.phone-app-container .wrapper .content .recent-calls .item .user .info span{color:var(--phone-text-secondary);font-size:14px;font-weight:400}.phone-app-container .wrapper .content .recent-calls .item .info{display:flex;align-items:center;gap:.5rem;padding-right:.1rem;font-size:14px;font-weight:400;color:#8e8e93}.phone-app-container .wrapper .content .recent-calls .item .info svg{font-size:28px;font-weight:300;color:var(--phone-color-blue)}.phone-app-container .wrapper .content .recent-calls .item .info svg:hover{filter:brightness(.7)}.phone-app-container .wrapper .content .letters{position:absolute;height:100%;display:flex;flex-direction:column;align-items:center;right:0;top:28%;z-index:10;margin-right:6px}.phone-app-container .wrapper .content .letters a{text-decoration:none;color:var(--phone-color-blue);font-size:13px;cursor:pointer}.phone-app-container .wrapper .content .letters a:active{color:var(--phone-color-blue)}.phone-app-container .wrapper .content .divider{margin-bottom:.25rem;font-size:15px;font-weight:500;padding:0 5%;padding-top:1.5rem;color:var(--phone-text-secondary)}.phone-app-container .wrapper .content .divider.no-border{border:none;padding-top:0}.phone-app-container .wrapper .content .section{background-color:var(--app-secondary);border-radius:15px}.phone-app-container .wrapper .content .contact{cursor:pointer;transition:all .1s ease-in-out;padding:.7rem 5%;font-size:18px;font-weight:400;color:var(--phone-text-primary)}.phone-app-container .wrapper .content .contact.item{display:flex;align-items:center;gap:.5rem;padding:.7rem 3.5%}.phone-app-container .wrapper .content .contact.item:hover{background-color:var(--phone-color-hover)}.phone-app-container .wrapper .content .contact.item:hover:first-child{border-radius:15px 15px 0 0}.phone-app-container .wrapper .content .contact.item:hover:last-child{border-radius:0 0 15px 15px}.phone-app-container .wrapper .content .contact.item:hover:only-child{border-radius:15px}.phone-app-container .wrapper .content .contact.card{background-color:var(--app-secondary);border-radius:15px;margin:1rem 0}.phone-app-container .wrapper .content .contact.details{cursor:auto}.phone-app-container .wrapper .content .contact .details{display:flex;flex-direction:column}.phone-app-container .wrapper .content .contact .details .name{font-size:18px}.phone-app-container .wrapper .content .contact .details .phone-number{color:var(--phone-text-secondary);font-size:14px;font-weight:400}.phone-app-container .wrapper .content .contact.border{border-bottom:1px solid var(--phone-color-border)}.phone-app-container .wrapper .content .contact.border:last-child{border-bottom:none}.phone-app-container .wrapper .content .contact .user{display:flex;flex-direction:column;align-items:center;gap:.5rem}.phone-app-container .wrapper .content .contact .user .name{font-size:27px;font-weight:400;color:var(--phone-text-primary);text-align:center}.phone-app-container .wrapper .content .contact .user span{cursor:pointer;color:var(--phone-color-blue);font-size:18px;font-weight:400}.phone-app-container .wrapper .content .contact .user span.add{margin-top:-1rem}.phone-app-container .wrapper .content .contact .user svg{color:var(--phone-text-secondary);font-size:150px;cursor:pointer;padding:0;margin:0}.phone-app-container .wrapper .content .contact .user svg.big{font-size:200px}.phone-app-container .wrapper .content .contact .actions{margin-top:1.5rem;display:flex;align-items:center;justify-content:center;gap:.6rem}.phone-app-container .wrapper .content .contact .actions .item{display:flex;flex-direction:column;align-items:center;justify-content:center;gap:.5rem;flex:1;padding:.5rem;border-radius:12px;background-color:var(--phone-color-highlight);color:var(--phone-color-blue);font-size:14px;font-weight:400;cursor:pointer;transition:all .1s ease-in-out}.phone-app-container .wrapper .content .contact .actions .item svg{font-size:28px}.phone-app-container .wrapper .content .contact .actions .item i{font-weight:600;font-size:16px}.phone-app-container .wrapper .content .contact .actions .item:hover{filter:brightness(.9)}.phone-app-container .wrapper .content .contact .actions [data-disabled=true]{filter:brightness(.9);pointer-events:none;cursor:not-allowed}.phone-app-container .wrapper .content .contact .contact-info{margin-top:1.5rem;display:flex;flex-direction:column;align-items:center;background-color:var(--phone-color-highlight);border-radius:12px}.phone-app-container .wrapper .content .contact .contact-info .item{display:flex;flex-direction:column;gap:.25rem;width:90%;padding:.75rem;border-top:1px solid var(--phone-color-border);font-size:17px;color:var(--phone-color-blue);font-weight:400;cursor:pointer;transition:all .1s ease-in-out}.phone-app-container .wrapper .content .contact .contact-info .item.red{color:var(--phone-color-red)}.phone-app-container .wrapper .content .contact .contact-info .item .title{font-size:14px;color:var(--phone-text-primary)}.phone-app-container .wrapper .content .contact .contact-info .item .value{font-size:18px;color:var(--phone-color-blue);-webkit-user-select:all;user-select:all}.phone-app-container .wrapper .content .contact .contact-info .item .value.calls{display:flex;flex-direction:column;gap:.35rem;margin-top:.25rem}.phone-app-container .wrapper .content .contact .contact-info .item .value.calls .call-item{display:flex;gap:.55rem}.phone-app-container .wrapper .content .contact .contact-info .item .value.calls .call-item .timestamp,.phone-app-container .wrapper .content .contact .contact-info .item .value.calls .call-item .type{font-size:13px;color:var(--phone-text-primary)}.phone-app-container .wrapper .content .contact .contact-info .item .value.calls .call-item .call-content{display:flex;flex-direction:column;gap:.25rem}.phone-app-container .wrapper .content .contact .contact-info .item .value.calls .call-item .call-content .call-duration{font-size:13px;color:var(--phone-text-secondary)}.phone-app-container .wrapper .content .contact .contact-info .item:first-child{border-top:none}.phone-app-container .wrapper .content .contact .contact-info .item input{width:100%;height:100%;font-size:18px;font-weight:400;border:none;background-color:transparent;background-color:var(--phone-color-highlight);color:var(--phone-text-primary)}.phone-app-container .wrapper .content .contact .contact-info .item input:focus,.phone-app-container .wrapper .content .contact .contact-info .item input:active{border:none;outline:none}.phone-app-container .wrapper .content .contact .contact-info .item input.phone_number{color:var(--phone-color-blue);-webkit-appearance:none}.phone-app-container .wrapper .content .contact .contact-info .item input.phone_number::-webkit-inner-spin-button,.phone-app-container .wrapper .content .contact .contact-info .item input.phone_number::-webkit-outer-spin-button{-webkit-appearance:none;margin:0}.phone-app-container .wrapper .content .contact .contact-info .item:hover:not(.input){filter:brightness(.9)}.phone-app-container .wrapper .content .contact .profile{display:flex;flex-direction:row;align-items:center;width:100%;padding:.25rem 0;gap:.5rem}.phone-app-container .wrapper .content .contact .profile .profile-info{display:flex;flex-direction:column;color:var(--phone-text-primary)}.phone-app-container .wrapper .content .contact .profile .profile-info .name{font-size:20px}.phone-app-container .wrapper .content .contact .profile .profile-info .info{font-size:14px;font-weight:400;color:var(--phone-text-secondary)}.phone-app-container .wrapper .content .contact .profile svg{margin-left:auto;margin-right:.5rem;color:var(--phone-text-secondary);font-size:28px}.phone-app-container .wrapper .content .total-contacts{padding-top:2.5rem;padding-bottom:2rem;font-size:17px;font-weight:600;text-align:center}.phone-app-container .profile-image{display:flex;width:4rem;height:4rem;aspect-ratio:1/1;border-radius:50%;justify-content:center;align-items:center;font-size:23px;font-weight:400;background-position:center;background-size:cover;background-repeat:no-repeat;background-color:var(--phone-text-secondary);color:#fff}.phone-app-container .profile-image.big{width:5rem;height:5rem;font-size:22px}.phone-app-container .profile-image.bigger{width:9rem;height:9rem;font-size:50px}.phone-app-container .profile-image.custom{background-color:transparent}.phone-app-container .footer{height:12%;width:100%;display:flex;flex-direction:row;justify-content:center;gap:2rem;border-top:2px solid var(--phone-color-border);background-color:var(--app-secondary)}.phone-app-container .footer .item{padding-top:1rem;display:flex;flex-direction:column;align-items:center;cursor:pointer;transition:all .1s ease-in-out;gap:.2rem;color:var(--phone-text-secondary)}.phone-app-container .footer .item.active{color:var(--phone-color-blue)}.phone-app-container .footer .item:hover{color:var(--phone-color-blue);filter:brightness(.8)}.phone-app-container .footer .item svg{font-size:30px}.phone-app-container .footer .item span{font-size:12px}.phone-app-container .avatar{display:flex;width:3rem;height:3rem;border-radius:50%;justify-content:center;align-items:center;font-size:18px;font-weight:400;background-position:center!important;background-size:cover!important;background-repeat:no-repeat!important;background:linear-gradient(180deg,#e3e3e3,#999999);color:#fff;box-shadow:inset 0 4px 15px #00000040}.phone-app-container .avatar.big{width:5rem;height:5rem;font-size:22px;background-size:cover}.phone-app-container .avatar.bigger{width:9rem;height:9rem;font-size:50px;background-size:cover}
