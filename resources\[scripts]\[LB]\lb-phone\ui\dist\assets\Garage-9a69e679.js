import{u as g,r as v,G as f,q as p,s as O,j as a,a as e,m as R,F as m,bD as C,bn as S,L as s,S as I,y as o,bE as T,aN as k,bF as L,bG as U,bH as y,C as G,bI as _,aB as V,aF as D,bJ as B,bK as M,bt as F,bL as K,bM as x,bi as X,bu as j,X as w,o as W,A as Y,b as J}from"./index-a04bc7c5.js";const q={vehicles:[{type:"car",name:"Blista Compact",plate:"49XJL722",model:"Blista Compact",location:"Richman Glen",impounded:!1,locked:!1,statistics:{engine:100,body:100,fuel:100}},{type:"car",name:"Buccaneer",plate:"49XJL723",model:"Buccaneer",location:"Buccaneer Way",impounded:!1,locked:!0,statistics:{engine:87,body:98,fuel:53}},{type:"car",name:"Buffalo",plate:"49XJL724",model:"Buffalo",location:"Grove Street",impounded:!0,locked:!1,statistics:{engine:100,body:100,fuel:100},impoundReason:{reason:"Parking Violation",retrievable:0,price:250,impounder:"Police Department"}}]};function z(){const r=g(Y),[c,t]=v.useState([]),[i,d]=v.useState(null);v.useEffect(()=>{f("Garage")&&p("Garage",{action:"getVehicles"},q.vehicles).then(l=>{if(!l)return O("info","No response from getVehicles");t(l)})},[r==null?void 0:r.active]);const A={car:e(S,{}),boat:e(T,{}),plane:e(k,{}),bike:e(L,{}),truck:e(U,{}),train:e(y,{})};return a("div",{className:"garage-container",style:{backgroundImage:"url(./assets/img/backgrounds/default/apps/garage/2.jpg)"},children:[e(R.div,{animate:{backdropFilter:"blur(10px) brightness(0.75)"},exit:{backdropFilter:"inherit"},transition:{duration:1},className:"blur-overlay"}),e("div",{className:"garage-header",children:i?a(m,{children:[e("div",{className:"title",children:i.model}),e(C,{className:"close",onClick:()=>d(null)})]}):a("div",{className:"title",children:[e(S,{})," ",s("APPS.GARAGE.MY_CARS")]})}),e(R.div,{...I(i?"right":"left",i?"home":"homes",.2),className:"garage-wrapper",children:i?e(H,{Vehicle:[i,d],Vehicles:[c,t]}):a(m,{children:[c.filter(l=>l.location==="out"&&!l.impounded).length>0&&a(m,{children:[a("div",{className:"subtitle",children:[s("APPS.GARAGE.OUT"),e(o,{})]}),e("section",{children:c.filter(l=>l.location==="out"&&!l.impounded).map(l=>a("div",{className:"item small",onClick:()=>d(l),children:[e("div",{className:"icon blue",children:A[l.type]}),a("div",{className:"info",children:[e("div",{className:"title",children:l.model}),e("div",{className:"value",children:l.plate})]})]},l.plate))})]}),c.filter(l=>l.location!=="out"&&!l.impounded).length>0&&a(m,{children:[a("div",{className:"subtitle",children:[s("APPS.GARAGE.GARAGE"),e(o,{})]}),e("section",{children:c.filter(l=>l.location!=="out"&&!l.impounded).map((l,P)=>a("div",{className:"item small",onClick:()=>d(l),children:[e("div",{className:"icon blue",children:A[l.type]}),a("div",{className:"info",children:[e("div",{className:"title",children:l.model}),a("div",{className:"value",children:[l.plate," - ",l.location]})]})]},l.plate))})]}),c.filter(l=>l.impounded).length>0&&a(m,{children:[a("div",{className:"subtitle",children:[s("APPS.GARAGE.IMPOUNDED"),e(o,{})]}),e("section",{children:c.filter(l=>l.impounded).map((l,P)=>a("div",{className:"item small",onClick:()=>d(l),children:[e("div",{className:"icon blue",children:A[l.type]}),a("div",{className:"info",children:[e("div",{className:"title",children:l.model}),a("div",{className:"value",children:[l.plate," - ",l.location]})]})]},l.plate))})]})]})})]})}const H=({Vehicle:r,Vehicles:c})=>{var P,E,b;const t=g(J),[i,d]=r,[A,l]=c;return a("div",{className:"options-container",children:[a("div",{className:"grid-wrapper",children:[a("div",{className:"subtitle",children:[s("APPS.GARAGE.ACTIONS"),e(o,{})]}),a("div",{className:"grid",children:[a("div",{className:"item big",onClick:()=>{G.PopUp.set({title:s("APPS.GARAGE.WAYPOINT_POPUP.TITLE"),description:s("APPS.GARAGE.WAYPOINT_POPUP.TEXT").format({model:i.model}),buttons:[{title:s("APPS.GARAGE.WAYPOINT_POPUP.CANCEL")},{title:s("APPS.GARAGE.WAYPOINT_POPUP.PROCEED"),cb:()=>{p("Garage",{action:"setWaypoint",plate:i.plate})}}]})},children:[e("div",{className:"icon blue",children:e(_,{})}),a("div",{className:"info",children:[e("div",{className:"title",children:s("APPS.GARAGE.LOCATION")}),e("div",{className:"value",children:V(i.location==="out"?s("APPS.GARAGE.OUT"):i.location)})]})]}),i.locked!==void 0&&a("div",{className:"item big","data-active":i.locked,onClick:()=>{var u;G.PopUp.set({title:s("APPS.GARAGE.LOCK_POPUP.TITLE").format({toggle:i.locked?s("APPS.GARAGE.UNLOCK"):s("APPS.GARAGE.LOCK")}),description:s("APPS.GARAGE.LOCK_POPUP.TEXT").format({toggle:(u=i.locked?s("APPS.GARAGE.UNLOCK"):s("APPS.GARAGE.LOCK"))==null?void 0:u.toLowerCase()}),buttons:[{title:s("APPS.GARAGE.LOCK_POPUP.CANCEL")},{title:s("APPS.GARAGE.LOCK_POPUP.PROCEED"),cb:()=>{p("Garage",{action:"toggleLocked",plate:i.plate},!i.locked).then(h=>{h!==void 0&&(l(n=>n.map(N=>N.plate===i.plate?{...N,locked:!N.locked}:N)),d(n=>({...n,locked:!n.locked})))})}}]})},children:[e("div",{className:"icon blue",children:e(D,{})}),a("div",{className:"info",children:[e("div",{className:"title",children:s("APPS.GARAGE.STATUS")}),e("div",{className:"value",children:i.locked?s("APPS.GARAGE.LOCKED"):s("APPS.GARAGE.UNLOCKED")})]})]}),((P=t==null?void 0:t.valet)==null?void 0:P.enabled)&&i.location!=="out"&&!i.impounded&&((E=t.valet)==null?void 0:E.vehicleTypes.includes(i.type))&&a("div",{className:"item big",onClick:()=>{G.PopUp.set({title:s("APPS.GARAGE.VALET_POPUP.TITLE"),description:t.valet.price&&t.valet.price>0?s("APPS.GARAGE.VALET_POPUP.TEXT_PAID").format({model:i.model,plate:i.plate,amount:t.valet.price}):s("APPS.GARAGE.VALET_POPUP.TEXT").format({model:i.model,plate:i.plate}),buttons:[{title:s("APPS.GARAGE.VALET_POPUP.CANCEL")},{title:s("APPS.GARAGE.VALET_POPUP.PROCEED"),cb:()=>{p("Garage",{action:"valet",plate:i.plate}).then(u=>{u&&l(h=>h.map(n=>n.plate===i.plate?{...n,location:"out"}:n))})}}]})},children:[e("div",{className:"icon blue",children:e(B,{})}),a("div",{className:"info",children:[e("div",{className:"title",children:s("APPS.GARAGE.SUMMON")}),e("div",{className:"value",children:s("APPS.GARAGE.VALET")})]})]})]})]}),i.statistics&&Object.keys(i.statistics).length>0&&a("div",{className:"grid-wrapper",children:[a("div",{className:"subtitle",children:[s("APPS.GARAGE.STATISTICS"),e(o,{})]}),a("div",{className:"grid",children:[i.statistics.fuel&&a("div",{className:"item small",children:[e("div",{className:"icon blue",children:e(M,{})}),a("div",{className:"info",children:[e("div",{className:"title",children:s("APPS.GARAGE.FUEL")}),a("div",{className:"value",children:[i.statistics.fuel,"%"]})]})]}),i.statistics.engine&&a("div",{className:"item small",children:[e("div",{className:"icon blue",children:e(F,{})}),a("div",{className:"info",children:[e("div",{className:"title",children:s("APPS.GARAGE.ENGINE")}),a("div",{className:"value",children:[i.statistics.engine,"%"]})]})]}),i.statistics.body&&a("div",{className:"item small",children:[e("div",{className:"icon blue",children:e(K,{})}),a("div",{className:"info",children:[e("div",{className:"title",children:s("APPS.GARAGE.BODY")}),a("div",{className:"value",children:[i.statistics.body,"%"]})]})]})]})]}),i.impounded&&i.impoundReason&&Object.keys(i.impoundReason).length>0&&a("div",{className:"grid-wrapper",children:[a("div",{className:"subtitle",children:[s("APPS.GARAGE.IMPOUND_INFO"),e(o,{})]}),a("div",{className:"grid",children:[i.impoundReason.reason&&a("div",{className:"item big",children:[e("div",{className:"icon blue",children:e(x,{})}),a("div",{className:"info",children:[e("div",{className:"title",children:s("APPS.GARAGE.IMPOUND_REASON.REASON")}),e("div",{className:"value",children:i.impoundReason.reason})]})]}),i.impoundReason.impounder&&a("div",{className:"item big",children:[e("div",{className:"icon blue",children:e(X,{})}),a("div",{className:"info",children:[e("div",{className:"title",children:s("APPS.GARAGE.IMPOUND_REASON.IMPOUNDER")}),e("div",{className:"value",children:i.impoundReason.impounder})]})]}),i.impoundReason.price!==void 0&&a("div",{className:"item small",children:[e("div",{className:"icon blue",children:e(j,{})}),a("div",{className:"info",children:[e("div",{className:"title",children:s("APPS.GARAGE.IMPOUND_REASON.PRICE")}),e("div",{className:"value",children:(b=t==null?void 0:t.CurrencyFormat)==null?void 0:b.replace("%s",i.impoundReason.price.toString())})]})]}),i.impoundReason.retrievable&&a("div",{className:"item small",children:[e("div",{className:"icon blue",children:e(w,{})}),a("div",{className:"info",children:[e("div",{className:"title",children:s("APPS.GARAGE.IMPOUND_REASON.RETRIEVABLE")}),e("div",{className:"value",children:W(i.impoundReason.retrievable)})]})]})]})]})]})};export{z as default};
