import{J as B,u as p,r as f,j as n,m as O,S as F,a as e,I as Z,L as s,V as y,d as j,q as M,s as K,b5 as v,ax as Y,F as E,z as J,C as z,y as X,P as D,b6 as G,b7 as Q,aO as ee,b8 as te,R as ie,b as U,aX as ne,A as le,b9 as re,ba as se,bb as ce,bc as ae}from"./index-a04bc7c5.js";import{S as $}from"./Switch-1ce279b8.js";const k=B(null);function oe(){const t=p(k),[a,u]=f.useState((t==null?void 0:t.label)??""),[c,d]=f.useState({hour:(t==null?void 0:t.hour)??0,minute:(t==null?void 0:t.minute)??0}),i=()=>{let r={label:a&&a.length>0?a:null,minutes:c.minute,hours:c.hour};M("Clock",{action:t!=null&&t.id?"updateAlarm":"createAlarm",id:t==null?void 0:t.id,...r},Math.floor(Math.random()*1e3)+1).then(m=>{if(!m)return K("error",`Failed to ${t!=null&&t.id?"update":"create"} alarm`);t!=null&&t.id?v.Alarms.set(v.Alarms.value.map(h=>h.id===t.id?{id:t.id,...r,enabled:h.enabled}:h)):v.Alarms.set([...v.Alarms.value,{...r,id:m,enabled:!0}]),k.reset(),N.set("Alarm")})},l=(r,m)=>{const L=document.querySelector(`#${m}`).querySelector(".active");let C=r.deltaY>0?L.nextElementSibling:L.previousElementSibling;C==null||C.click()};return f.useEffect(()=>{if(!(t!=null&&t.hour))return;const r=document.querySelector("#hour"),m=document.getElementById(`h-${c.hour}`);if(r&&m){const C=m.offsetTop-r.offsetHeight/2+m.offsetHeight/2;r.scrollTo(0,C+12)}const h=document.querySelector("#minute"),L=document.getElementById(`m-${c.minute}`);if(h&&L){const C=L.offsetTop-h.offsetHeight/2+L.offsetHeight/2;h.scrollTo(0,C+12)}},[t]),n(O.div,{...F("left","new-alarm",.2),className:"new-alarm-container",children:[n("div",{className:"new-alarm-header",children:[n("div",{className:"cancel",onClick:()=>{k.reset(),N.set("Alarm")},children:[e(Z,{}),s("APPS.CLOCK.ALARM.ALARMS")]}),n("div",{className:"title",children:[" ",t!=null&&t.id?s("APPS.CLOCK.ALARM.EDIT_ALARM"):s("APPS.CLOCK.ALARM.NEW_ALARM")]}),e("div",{className:"save",onClick:()=>i(),children:s("APPS.CLOCK.ALARM.SAVE")})]}),n("div",{className:"new-alarm-content",children:[n("div",{className:"picker alarm",children:[e("div",{children:n("ul",{id:"hour",onWheel:r=>l(r,"hour"),children:[e("li",{}),[...Array(24).keys()].map((r,m)=>n("li",{id:`h-${r.toString()}`,className:y(c.hour===r&&"active"),onClick:h=>{h.target.classList.contains("active")||(h.target.scrollIntoView({block:"center",behavior:"smooth"}),d({...c,hour:r}))},children:[r<10?`0${r}`:r," "]},m)),e("li",{})]})}),e("div",{className:"separator",children:":"}),e("div",{children:n("ul",{id:"minute",onWheel:r=>l(r,"minute"),children:[e("li",{}),[...Array(60).keys()].map((r,m)=>e("li",{id:`m-${r.toString()}`,className:y(c.minute===r&&"active"),onClick:h=>{h.target.classList.contains("active")||(h.target.scrollIntoView({block:"center",behavior:"smooth"}),d({...c,minute:r}))},children:r<10?`0${r}`:r},m)),e("li",{})]})}),e("div",{className:"selected"})]}),n("div",{className:"settings",children:[n("div",{className:"item",children:[e("div",{className:"label",children:s("APPS.CLOCK.ALARM.LABEL")}),e(j,{type:"text",placeholder:s("APPS.CLOCK.ALARM.TITLE"),defaultValue:a,onChange:r=>u(r.target.value)})]}),n("div",{className:"item",children:[e("div",{className:"label",children:s("APPS.CLOCK.ALARM.SOUND")}),e($,{disabled:!0,checked:!0})]}),n("div",{className:"item",children:[e("div",{className:"label",children:s("APPS.CLOCK.ALARM.SNOOZE")}),e($,{disabled:!0,checked:!1})]})]})]})]})}function de(){const t=p(D.Settings),[a,u]=f.useState(!1),c=p(v.Alarms),d=i=>{let l=i.hour,r=i.minute;K("info",`Formatting time: ${l}:${r}`),t.time.twelveHourClock&&(l>12?l=l-12:l===0&&(l=12));let m,h;return l<10?m=`0${l}`:m=`${l}`,r<10?h=`0${r}`:h=`${r}`,t.time.twelveHourClock?`${m}:${h} ${i.hour>12?"PM":"AM"}`:`${m}:${h}`};return n("div",{className:"alarm-container",children:[n("div",{className:"alarm-header",children:[n("div",{className:"alarm-header-top",children:[e("div",{className:"edit",onClick:()=>u(!a),children:a?s("APPS.CLOCK.ALARM.DONE"):s("APPS.CLOCK.ALARM.EDIT")}),e(Y,{onClick:()=>N.set("NewAlarm")})]}),e("div",{className:"alarm-title",children:s("APPS.CLOCK.ALARM.TITLE")})]}),e("div",{className:"alarm-content",children:c.sort((i,l)=>i.hours===l.hours?i.minutes-l.minutes:i.hours-l.hours).map((i,l)=>e(O.div,{initial:{opacity:0,scale:.9},whileInView:{opacity:1,scale:1},viewport:{once:!0},className:"alarm","data-active":i.enabled,onClick:()=>{a&&(k.set({id:i.id,hour:i.hours,minute:i.minutes,label:i.label}),N.set("NewAlarm"))},children:a?n(E,{children:[n("div",{className:"alarm-wrapper",children:[e(J,{onClick:r=>{r.stopPropagation(),z.PopUp.set({title:s("APPS.CLOCK.ALARM.DELETE_TITLE"),description:s("APPS.CLOCK.ALARM.DELETE_TEXT"),buttons:[{title:s("APPS.CLOCK.ALARM.BUTTON_CANCEL")},{title:s("APPS.CLOCK.ALARM.BUTTON_DELETE"),color:"red",cb:()=>{M("Clock",{action:"deleteAlarm",id:i.id},!0).then(()=>{v.Alarms.set(c.filter(m=>m.id!==i.id))})}}]})}}),n("div",{className:"alarm-info",children:[e("div",{className:"time",children:d({hour:i.hours,minute:i.minutes})}),e("div",{className:"label",children:i.label??s("APPS.CLOCK.ALARM.TITLE")})]})]}),e(X,{})]}):n(E,{children:[n("div",{className:"alarm-info",children:[e("div",{className:"time",children:d({hour:i.hours,minute:i.minutes})}),e("div",{className:"label",children:i.label??s("APPS.CLOCK.ALARM.TITLE")})]}),e($,{checked:i.enabled,onChange:()=>{M("Clock",{action:"toggleAlarm",id:i.id,enabled:!i.enabled},!0).then(r=>{if(r===void 0)return K("error","Failed to toggle alarm");v.Alarms.set(c.map((m,h)=>(h===l&&(m.enabled=!m.enabled),m)))})}})]})},l))})]})}function R(t){let a=t.disabled,u=t.active;return e("div",{className:y("button",u&&"active",a&&"disabled"),children:e("div",{className:"button-inner",style:{fontSize:t.content.length>7?"12px":"15px"},onClick:()=>t.onClick(),children:t.content})})}function me(){const t=p(v.Stopwatch);return n("div",{className:"stopwatch-container",children:[e("div",{className:"stopwatch-header",children:e("div",{className:"stopwatch-title",children:s("APPS.CLOCK.STOPWATCH.TITLE")})}),n("div",{className:"stopwatch-content",children:[e("div",{className:"clock",children:b(t.time,!0)}),n("div",{className:"actions",children:[e(R,{content:t.running||!t.running&&t.time==0?s("APPS.CLOCK.STOPWATCH.LAP"):s("APPS.CLOCK.STOPWATCH.RESET"),disabled:!t.running&&t.time===0,onClick:()=>{!t.running&&t.time!==0?v.Stopwatch.set({running:!1,time:0,laps:[]}):t.time>0&&v.Stopwatch.set({...t,laps:[...t.laps,t.time]})}}),e(R,{active:t.running,content:t.running?s("APPS.CLOCK.STOPWATCH.STOP"):s("APPS.CLOCK.STOPWATCH.START"),onClick:()=>v.Stopwatch.set({...t,running:!t.running})})]}),e("div",{className:"laps","data-empty":t.laps.length===0,children:n("table",{style:{borderSpacing:`${t.laps.length===0?"3.5rem":"2.6rem"} 1rem`},children:[e("thead",{children:n("tr",{children:[e("th",{children:s("APPS.CLOCK.STOPWATCH.LAP")}),e("th",{children:s("APPS.CLOCK.STOPWATCH.TIME")}),e("th",{children:s("APPS.CLOCK.STOPWATCH.TOTAL")})]})}),n("tbody",{children:[t.laps.length===0&&e("tr",{children:e("td",{className:"no-laps",colSpan:3,children:s("APPS.CLOCK.STOPWATCH.NO_LAPS")})}),(()=>{if(t.laps.length===0)return null;const a=t.laps.map((d,i)=>i===0?d:d-t.laps[i-1]),u=a.indexOf(Math.min(...a)),c=a.indexOf(Math.max(...a));return t.laps.map((d,i)=>{const l=i===u,r=i===c;return n(O.tr,{initial:{opacity:0,scale:.9},whileInView:{opacity:1,scale:1},viewport:{once:!0},"data-best":l,"data-worst":r,children:[e("td",{children:i+1}),e("td",{children:b(i===0?d:d-t.laps[i-1],!0)}),e("td",{children:b(d,!0)})]},d)})})()]})]})})]})]})}function he(){const t=p(U),a=p(D.Settings),u=p(ne.Visible),c=p(v.Timer),[d,i]=f.useState([]),[l,r]=f.useState({hours:0,minutes:0,seconds:0}),m=f.useRef(null),h=f.useRef(null),L=f.useRef(null),[C,W]=f.useState(null);f.useEffect(()=>{let o=new Date(Date.now()+c.time*1e3);W(o.toLocaleTimeString(t==null?void 0:t.DateLocale,{hour:"numeric",minute:"numeric",hour12:a.time.twelveHourClock}));let A=localStorage.getItem("recent-timers");i(A?JSON.parse(A):[])},[]);const H=o=>{if(o||(o=l.hours*3600+l.minutes*60+l.seconds),o>0){v.Timer.set({running:!0,time:o});let A=new Date(Date.now()+o*1e3);W(A.toLocaleTimeString(t==null?void 0:t.DateLocale,{hour:"numeric",minute:"numeric",hourCycle:"h23"}));let S=[o,...d.filter(g=>g!==o)];S.length>5&&S.shift(),localStorage.setItem("recent-timers",JSON.stringify(S)),i(S)}},I=(o,A)=>{const g=document.querySelector(`#${A}`).querySelector(".active");let P=o.deltaY>0?g.nextElementSibling:g.previousElementSibling;P==null||P.click()},V=f.useRef(!0);return f.useEffect(()=>{var o,A,S;if(V.current){V.current=!1;return}if(!c.running&&(v.Timer.set({running:!1,time:0}),u)){let g=(o=m.current)==null?void 0:o.querySelector(".active"),P=(A=h.current)==null?void 0:A.querySelector(".active"),w=(S=L.current)==null?void 0:S.querySelector(".active");g&&(g==null||g.scrollIntoView({block:"center"})),P&&(P==null||P.scrollIntoView({block:"center"})),w&&(w==null||w.scrollIntoView({block:"center"}))}},[c.running]),n("div",{className:"timer-container",children:[e("div",{className:"timer-header",children:e("div",{className:"timer-title",children:s("APPS.CLOCK.TIMER.TITLE")})}),n("div",{className:"timer-content",children:[c.running?e(E,{children:n("div",{className:"timer-wrapper",children:[e("div",{className:"border-wrapper",children:e(G,{value:c.time/(l.hours*3600+l.minutes*60+l.seconds)*100,strokeWidth:4,styles:Q({strokeLinecap:"butt",pathColor:"#348DE9",trailColor:a.display.theme==="light"?"#AAAAAA":"#303030",pathTransitionDuration:.5})})}),n("div",{className:"timer",children:[e("div",{className:"timer-text",children:b(c.time,!1)}),C&&n("div",{className:"when-done",children:[e(ee,{}),C]})]})]})}):n(E,{children:[n("div",{className:"picker-labels",children:[e("div",{children:s("APPS.CLOCK.TIMER.HOURS")}),e("div",{children:s("APPS.CLOCK.TIMER.MINUTES")}),e("div",{children:s("APPS.CLOCK.TIMER.SECONDS")})]}),n("div",{className:"picker",children:[e("div",{ref:m,children:n("ul",{id:"hours",onWheel:o=>{I(o,"hours")},children:[e("li",{}),[...Array(24).keys()].map((o,A)=>e("li",{onClick:S=>{S.target.scrollIntoView({block:"center",behavior:"smooth"}),r({...l,hours:o})},className:l.hours===o?"active":"",children:o},A)),e("li",{})]})}),e("div",{ref:h,children:n("ul",{id:"minutes",onWheel:o=>{I(o,"minutes")},children:[e("li",{}),[...Array(60).keys()].map((o,A)=>e("li",{onClick:S=>{S.target.scrollIntoView({block:"center",behavior:"smooth"}),r({...l,minutes:o})},className:l.minutes===o?"active":"",children:o},A)),e("li",{})]})}),e("div",{ref:L,children:n("ul",{id:"seconds",onWheel:o=>{I(o,"seconds")},children:[e("li",{}),[...Array(60).keys()].map((o,A)=>e("li",{onClick:S=>{S.target.scrollIntoView({block:"center",behavior:"smooth"}),r({...l,seconds:o})},className:l.seconds===o?"active":"",children:o},A)),e("li",{})]})}),e("div",{className:"selected"})]})]}),n("div",{className:"actions",children:[e(R,{content:s("APPS.CLOCK.TIMER.CANCEL"),disabled:!c.running,onClick:()=>{c.running&&v.Timer.set({running:!1,time:0})}}),e(R,{content:c.running?s("APPS.CLOCK.TIMER.STOP"):s("APPS.CLOCK.TIMER.START"),active:c.running,onClick:()=>c.running?v.Timer.set({...c,running:!1}):H()})]}),!c.running&&n("div",{className:"recents",children:[e("div",{className:"title",children:"Recents"}),n("div",{className:"recents-wrapper","data-empty":d.length===0,children:[d.length===0&&e("div",{className:"no-recents",children:s("APPS.CLOCK.TIMER.NO_RECENTS")}),d.map((o,A)=>n(O.div,{initial:{opacity:0,scale:.9},whileInView:{opacity:1,scale:1},viewport:{once:!0},className:"item",children:[e("div",{className:"time",children:b(o)}),n("div",{className:"buttons",children:[e(te,{className:"red",onClick:()=>{let S=d.filter((g,P)=>P!==A);localStorage.setItem("recent-timers",JSON.stringify(S)),i(S)}}),e(ie,{className:"green",onClick:()=>H(o)})]})]},A))]})]})]})]})}function ue(){const t=p(U),a=p(D.Settings),u=t.world_clock_locations,c=i=>new Date().toLocaleTimeString(t.DateLocale,{timeZone:u[i],hour:"2-digit",minute:"2-digit",hour12:a.time.twelveHourClock}),d=i=>{const l=new Date;let r=l.toLocaleTimeString([],{hour:"2-digit",hour12:!1}),m=l.toLocaleDateString([],{day:"numeric"}),h=l.toLocaleTimeString([],{hour:"2-digit",hour12:!1,timeZone:u[i]}),L=l.toLocaleDateString([],{day:"numeric",timeZone:u[i]}),C=parseInt(h)-parseInt(r);return C===0?"Local time":L>m?`${s("APPS.CLOCK.WORLDCLOCK.TOMORROW")} ${C>0?"+":""}${C}HRS`:L<m?`${s("APPS.CLOCK.WORLDCLOCK.YESTERDAY")} ${C>0?"-":""}${C}HRS`:`${s("APPS.CLOCK.WORLDCLOCK.TODAY")} ${C>0?"+":""}${C}HRS`};return n("div",{className:"worldclock-container",children:[e("div",{className:"worldclock-header",children:e("div",{className:"worldclock-title",children:s("APPS.CLOCK.WORLDCLOCK.TITLE")})}),e("div",{className:"worldclock-content",children:Object.keys(u).map((i,l)=>n(O.div,{initial:{opacity:0,scale:.9},whileInView:{opacity:1,scale:1},viewport:{once:!0},className:"worldclock",children:[n("div",{className:"worldclock-location",children:[e("div",{className:"location",children:i}),e("div",{className:"difference",children:d(i)})]}),e("div",{className:"time",children:c(i)})]},l))})]})}var x,_,q;const N=B(((q=(_=(x=le.value)==null?void 0:x.active)==null?void 0:_.data)==null?void 0:q.view)??"Timer"),T={"World Clock":{icon:e(re,{}),title:s("APPS.CLOCK.WORLDCLOCK.TITLE"),view:e(ue,{})},Alarm:{icon:e(se,{}),title:s("APPS.CLOCK.ALARM.TITLE"),view:e(de,{})},Stopwatch:{icon:e(ce,{}),title:s("APPS.CLOCK.STOPWATCH.TITLE"),view:e(me,{})},Timer:{icon:e(ae,{}),title:s("APPS.CLOCK.TIMER.TITLE"),view:e(he,{})},NewAlarm:{view:e(oe,{}),title:s("APPS.CLOCK.ALARM.NEW_ALARM"),hidden:!0}};function Se(){const t=p(N);return n("div",{className:"clock-container",children:[e(O.div,{...F(t==="NewAlarm"?"right":"left",t,.2),className:"clock-body",children:T[t].view}),e("div",{className:"clock-footer",children:Object.keys(T).filter(a=>!T[a].hidden).map((a,u)=>{var d,i,l;let c=t==a||t=="NewAlarm"&&a=="Alarm";return n("div",{className:`${c?"active":""}`,style:{fontSize:((d=T[a])==null?void 0:d.title.length)>10?"13px":"14px"},onClick:()=>N.set(a),children:[(i=T[a])==null?void 0:i.icon,(l=T[a])==null?void 0:l.title]},u)})})]})}const b=(t,a)=>{if(a){let u=t%100,c=Math.floor(t/100)%60,d=Math.floor(t/6e3)%60,i=Math.floor(t/36e4)%24;return i>0?`${i.toString().padStart(2,"0")}:${d.toString().padStart(2,"0")}:${c.toString().padStart(2,"0")}.${u.toString().padStart(2,"0")}`:`${d.toString().padStart(2,"0")}:${c.toString().padStart(2,"0")}.${u.toString().padStart(2,"0")}`}else{let u=t%60,c=Math.floor(t/60)%60;return`${(Math.floor(t/3600)%24).toString().padStart(2,"0")}:${c.toString().padStart(2,"0")}:${u.toString().padStart(2,"0")}`}},ve=t=>{if(!t)return;const a=t.parentElement.parentElement;if(!a)return console.error("Failed to snap element");const u=a.getBoundingClientRect(),c=t.getBoundingClientRect(),d=c.top-u.top,l=a.scrollTop+d-u.height/2+c.height/2;a.scrollTo({top:l,behavior:"smooth"})};export{N as View,Se as default,b as formatTime,ve as snapElement};
