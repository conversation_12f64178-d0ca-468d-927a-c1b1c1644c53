:root{--phone-color-primary: rgb(255, 255, 255);--phone-color-opacity: rgba(242, 242, 242, .4);--phone-color-opacity2: rgb(30, 30, 30, .5);--phone-color-highlight: rgb(250, 250, 250);--phone-color-highlight2: rgb(240, 240, 240);--phone-color-highlight3: rgb(220, 220, 220);--phone-highlight-opacity15: rgba(145, 145, 145, .15);--phone-highlight-opacity35: rgba(145, 145, 145, .35);--phone-highlight-opacity45: rgba(145, 145, 145, .45);--phone-highlight-opacity55: rgba(145, 145, 145, .55);--phone-color-input: rgba(241, 241, 241, .656);--phone-text-primary: rgb(0, 0, 0);--phone-text-secondary: rgb(142, 142, 147);--phone-color-hover: rgb(240, 240, 240);--phone-color-border: rgba(200, 200, 200, .4);--phone-color-grey: #8e8e93;--phone-color-blue: #0a84ff;--phone-color-green: #32d74b;--phone-color-green-secondary: #092911;--phone-color-red: #ff3b30;--phone-color-orange: rgb(255, 157, 10);--phone-color-yellow: #cca250;--phone-color-pink: #ff3b30;--instagram-primary: #ffffff;--instagram-blue: rgb(0, 149, 246);--instagram-red: rgb(237, 73, 86);--instagram-primary-text: rgb(38, 38, 38);--instagram-secondary-text: rgb(142, 142, 142);--instagram-stroke: rgb(219, 219, 219);--instagram-border: rgb(219, 219, 219);--instagram-highlight: rgb(239, 239, 239);--tinder-color-pink: #ff4573;--tinder-color-orange: #ff5f65;--tinder-color-mix: #f5547c;--twitter-primary: #f5f8fa;--twitter-secondary: #14171a;--twitter-background-highlight: rgb(239, 243, 244);--twitter-primary-text: #14171a;--twitter-secondary-text: #657786;--twitter-alt-text: #657786;--twitter-border: #bdc5cd75;--twitter-border-secondary: #1d9bf0;--twitter-highlight: #1d9bf0;--twitter-hover: rgba(15, 20, 25, .1);--twitter-action: #14171a;--twitter-blue: #1d9bf0;--tiktok-primary: #ffffff;--tiktok-secondary: #000000;--tiktok-text-primary: #000000;--tiktok-text-secondary: #86878b;--tiktok-color-border: #d0d1d3;--tiktok-color-pink: #fe2c55;--tiktok-color-aqua: #00f2ea;--tiktok-color-yellow: #f8cd14;--tiktok-color-blue: #479fc5;--tiktok-color-unread: rgba(254, 44, 86, .2);--crypto-color-primary: rgb(255, 255, 255);--browser-primary: rgb(245, 245, 245);--browser-secondary: rgba(153, 153, 153, .15);--browser-gradient: linear-gradient(230deg, #f4d6ff, #c5f1ff);--browser-footer: rgba(255, 255, 255, .75);--browser-border: rgba(102, 102, 102, .75);--browser-text-secondary: #696969;--controlcentre-opacity: rgba(255, 255, 255, .15);--controlcentre-opacity2: rgba(255, 255, 255, .2);--controlcentre-active: rgba(255, 255, 255, .5);--notification-primary: rgba(215, 215, 215, .5);--notification-secondary: rgba(215, 215, 215, .1);--lockscreeneditor-background: rgba(255, 255, 255, .75);--lockscreeneditor-secondary: #d9d9d9;--app-bg: #ececec;--app-bg2: #ffffff;--app-secondary: #ffffff;--app-secondary2: #ececec;--app-highlight: #cccccc;--app-highlight2: #999999;--app-highlight3: #ffffff;--app-border: #666666;--app-slider: #cccccc;--app-slider-active: #333333;--app-button: #ffffff;--components-bg: #eeeeee;--components-secondary: #ffffff;--components-highlight: #cccccc}[data-theme=dark]{--phone-color-primary: #000000;--phone-color-opacity: rgb(30, 30, 30, .5);--phone-color-opacity2: rgba(242, 242, 242, .4);--phone-color-highlight: rgb(15, 15, 15);--phone-color-highlight2: rgb(20, 20, 20);--phone-color-highlight3: rgb(25, 25, 25);--phone-highlight-opacity35: rgba(145, 145, 145, .35);--phone-highlight-opacity45: rgba(50, 50, 50, .6);--phone-highlight-opacity55: rgb(60, 60, 60, .8);--phone-color-input: rgba(60, 60, 67, .6);--phone-text-primary: #f2f2f7;--phone-text-secondary: #6f6f6f;--phone-color-grey: #636366;--phone-color-hover: rgb(30, 30, 30);--phone-color-border: rgba(150, 150, 150, .2);--phone-color-blue: #076bcf;--instagram-primary: #000000;--instagram-blue: rgb(0, 149, 246);--instagram-red: rgb(237, 73, 86);--instagram-primary-text: rgb(250, 250, 250);--instagram-secondary-text: rgb(142, 142, 142);--instagram-stroke: rgb(219, 219, 219);--instagram-border: rgb(54, 54, 54);--instagram-highlight: rgb(38, 38, 38);--twitter-primary: #000000;--twitter-secondary: #f5f8fa;--twitter-background-highlight: rgb(20, 20, 20);--twitter-primary-text: #f5f8fa;--twitter-secondary-text: #aab8c2;--twitter-alt-text: #657786;--twitter-border: #38444d;--twitter-border-secondary: #38444d;--twitter-hover: rgba(150, 150, 150, .1);--twitter-highlight: #dcdcdc;--twitter-action: #1d9bf0;--twitter-blue: #1d9bf0;--crypto-color-primary: rgb(24, 26, 32);--tiktok-text-primary: #f2f2f7;--tiktok-text-secondary: #6f6f6f;--tiktok-color-border: #96969633;--browser-primary: rgb(15, 15, 15);--browser-secondary: rgba(153, 153, 153, .15);--browser-gradient: linear-gradient(230deg, #453b48, #2f393d);--browser-footer: rgba(51, 51, 51, .75);--browser-border: rgba(102, 102, 102, .75);--browser-text-secondary: #999999;--controlcentre-opacity: rgba(0, 0, 0, .15);--controlcentre-opacity2: rgba(0, 0, 0, .2);--controlcentre-active: rgba(0, 0, 0, .5);--notification-primary: rgba(0, 0, 0, .1);--notification-secondary: rgba(0, 0, 0, .12);--lockscreeneditor-background: rgba(0, 0, 0, .8);--lockscreeneditor-secondary: #333333;--app-bg: #000000;--app-bg2: #000000;--app-secondary: #141414;--app-secondary2: #141414;--app-highlight: #cccccc;--app-highlight2: #696969;--app-highlight3: #212121;--app-border: #cccccc;--app-slider: #999999;--app-slider-active: #ffffff;--app-button: #333333;--components-bg: #000000;--components-secondary: #141414;--components-highlight: #696969}@keyframes zoomIn{0%{transform:scale(.5)}to{transform:scale(1)}}@keyframes zoomOut{0%{transform:scale(1.5)}to{transform:scale(1)}}@keyframes slideDown{0%{transform:translateY(-20%)}to{transform:translateY(0)}}@keyframes slideUp{0%{transform:translateY(40%)}to{transform:translateY(0)}}@keyframes slideRight{0%{transform:translate(-10%)}to{transform:translate(0)}}@keyframes slideLeft{0%{transform:translate(10%)}to{transform:translate(0)}}@keyframes appJiggle{0%{transform:rotate(-1deg);animation-timing-function:ease-in}50%{transform:rotate(1.5deg);animation-timing-function:ease-out}}@keyframes appJiggle2{0%{transform:rotate(1deg);animation-timing-function:ease-in}50%{transform:rotate(-1.5deg);animation-timing-function:ease-out}}@keyframes widgetJiggle{0%{transform:rotate(-.5deg);animation-timing-function:ease-in}50%{transform:rotate(.5deg);animation-timing-function:ease-out}}@keyframes widgetJiggle2{0%{transform:rotate(.5deg);animation-timing-function:ease-in}50%{transform:rotate(-.5deg);animation-timing-function:ease-out}}@keyframes heart-animation{0%{display:block;transform:scale(.5)}10%{transform:scale(1)}30%{transform:scale(1.1)}50%{transform:scale(1)}90%{transform:scale(1)}to{display:none;transform:scale(.5)}}.instagram-container{font-family:-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,Helvetica,Arial,sans-serif;height:100%;width:100%;position:relative;display:flex;flex-direction:column;align-items:center;background-color:var(--instagram-primary)}.instagram-container .slide.right{animation:slideRight .5s cubic-bezier(.19,1,.22,1)}.instagram-container .slide.up{animation:slideUp .5s cubic-bezier(.19,1,.22,1)}.instagram-container .loading{height:100%;display:flex;align-items:center;justify-content:center}.instagram-container .instagram-footer{display:flex;align-items:center;justify-content:space-between;width:80%;padding:.75rem 2.5rem 2.5rem;position:absolute;bottom:0;z-index:2;border-top:1px solid var(--instagram-border);background-color:var(--instagram-primary);border-bottom-left-radius:30px;border-bottom-right-radius:30px}.instagram-container .instagram-footer div{display:flex;align-items:center;justify-content:space-between}.instagram-container .instagram-footer img,.instagram-container .instagram-footer svg{font-size:28px;cursor:pointer;transition:all .1s ease-in-out;color:var(--instagram-primary-text)}.instagram-container .instagram-footer img:hover,.instagram-container .instagram-footer svg:hover{transform:scale(1.05)}.instagram-container .instagram-footer img.active,.instagram-container .instagram-footer svg.active{color:var(--instagram-primary-text);fill:var(--instagram-primary-text)!important}.instagram-container .instagram-footer img.profile,.instagram-container .instagram-footer svg.profile{width:28px;height:28px;border-radius:50%;object-fit:cover}.instagram-container .instagram-wrapper{width:100%;height:100%}.instagram-container .instagram-wrapper .instagram-header{margin-top:3.5rem;width:90%;display:flex;align-items:center;gap:.3rem;padding:0 1.5rem .5rem}.instagram-container .instagram-wrapper .instagram-header.feed-header{width:90%;display:flex;flex-direction:row;align-items:center;justify-content:space-between}.instagram-container .instagram-wrapper .instagram-header.feed-header svg{font-size:25px;cursor:pointer;transition:all .1s ease-in-out;color:var(--instagram-primary-text)}.instagram-container .instagram-wrapper .instagram-header .name{font-size:25px;font-weight:700;color:var(--instagram-primary-text);display:flex;align-items:center;gap:.25rem}.instagram-container .instagram-wrapper .instagram-header .name span{display:flex;align-items:center;justify-content:center}.instagram-container .instagram-wrapper .instagram-header .name span img{width:22px;height:22px}.instagram-container .instagram-wrapper .instagram-header .notifications{display:flex;align-items:center;justify-content:center;height:1.3rem;width:1.3rem;border-radius:50%;background-color:#eb4956;font-size:13px;font-weight:400;color:#fff}.instagram-container .instagram-wrapper .feed{width:100%;display:flex;flex-direction:column;padding-bottom:5rem;overflow-y:auto;overflow-x:hidden;max-height:80%;animation:slideRight .5s cubic-bezier(.19,1,.22,1)}.instagram-container .instagram-wrapper .feed::-webkit-scrollbar{display:none}.instagram-container .instagram-wrapper .feed .stories-wrapper{padding:.25rem 0}.instagram-container .instagram-wrapper .feed .stories{display:flex;align-items:center;gap:1.5rem;overflow-x:auto;overflow-y:hidden;scroll-behavior:auto;max-width:100%;padding:.5rem 1rem}.instagram-container .instagram-wrapper .feed .stories::-webkit-scrollbar{display:none}.instagram-container .instagram-wrapper .feed .stories div{display:flex;flex-direction:column;align-items:center;gap:.5rem;cursor:pointer;transition:all .1s ease-in-out}.instagram-container .instagram-wrapper .feed .stories div.self .profile-picture{display:block;position:relative}.instagram-container .instagram-wrapper .feed .stories div.self .profile-picture .plus{position:absolute;bottom:-5px;right:-5px;display:flex;align-items:center;justify-content:center;border:4px solid var(--instagram-primary);height:1.5rem;width:1.5rem;border-radius:50%;color:#fff;font-size:14px;background-color:#0095f6}.instagram-container .instagram-wrapper .feed .stories div .profile-picture{width:3.75rem;height:3.75rem;border-radius:50%;display:flex;align-items:center;justify-content:center}.instagram-container .instagram-wrapper .feed .stories div .profile-picture .avatar{width:100%;height:100%;border-radius:50%}.instagram-container .instagram-wrapper .feed .stories div .profile-picture .circle{position:absolute;top:0;left:0;transform:scale(1.1);width:100%;height:100%;background-position:center;background-size:cover;z-index:2;cursor:pointer}.instagram-container .instagram-wrapper .feed .stories div .profile-picture .circle[data-seen=true]{filter:grayscale(90%);opacity:.9}.instagram-container .instagram-wrapper .feed .stories div .name{text-align:center;font-size:14px;font-weight:500;color:var(--instagram-primary-text)}.instagram-container .instagram-wrapper .feed .stories div:hover{transform:scale(1.025)}.instagram-container .instagram-wrapper .no-posts{color:var(--instagram-primary-text);text-align:center;margin-top:10rem;font-size:16px;font-weight:500;box-sizing:border-box;padding:0 2rem}.instagram-container .instagram-wrapper .post{border-top:1px solid rgba(69,69,69,.**********);padding:1rem 0;position:relative}.instagram-container .instagram-wrapper .post .post-header{display:flex;align-items:center;gap:.5rem;padding-left:.75rem;padding-bottom:.75rem;background-color:var(--instagram-primary);cursor:pointer}.instagram-container .instagram-wrapper .post .post-header .profile-picture{width:2.25rem;height:2.25rem;border-radius:50%;box-shadow:0 0 0 1px #45454525;object-fit:cover}.instagram-container .instagram-wrapper .post .post-header .post-info{display:flex;flex-direction:column;color:var(--instagram-primary-text)}.instagram-container .instagram-wrapper .post .post-header .post-info .name{display:flex;align-items:center;gap:.2rem;font-size:17px;font-weight:500}.instagram-container .instagram-wrapper .post .post-header .post-info .location{font-size:14px;font-weight:400}.instagram-container .instagram-wrapper .post .post-body .image-container{display:flex;align-items:center;width:100%;height:25rem;overflow-x:auto;overflow-y:hidden}.instagram-container .instagram-wrapper .post .post-body .image-container::-webkit-scrollbar{display:none}.instagram-container .instagram-wrapper .post .post-body .image-container div{flex:1;min-width:100%;width:100%;height:100%;pointer-events:all;cursor:pointer;-webkit-user-drag:auto}.instagram-container .instagram-wrapper .post .post-body .image-container div img,.instagram-container .instagram-wrapper .post .post-body .image-container div video{width:100%;height:100%;object-fit:cover;pointer-events:none}.instagram-container .instagram-wrapper .post .post-body .popup-heart{position:absolute;top:13rem;left:0;right:0;margin-left:auto;margin-right:auto;width:6rem;height:6rem;animation-name:heart-animation;animation-duration:1s;display:none}.instagram-container .instagram-wrapper .post .post-body .popup-heart[data-visible=true]{display:block}.instagram-container .instagram-wrapper .post .post-body .popup-heart svg{opacity:.95;width:100%;height:100%;color:#fff}.instagram-container .instagram-wrapper .post .post-body .actions{display:flex;align-items:center;justify-content:space-between;padding:1rem .75rem;gap:1rem;position:relative;cursor:pointer}.instagram-container .instagram-wrapper .post .post-body .actions .like-comment{display:flex;align-items:center;gap:.75rem}.instagram-container .instagram-wrapper .post .post-body .actions .like-comment svg{color:var(--instagram-primary-text);font-size:24px;cursor:pointer;pointer-events:all;z-index:1}.instagram-container .instagram-wrapper .post .post-body .actions .scroll-dots{position:absolute;margin:auto;left:0;right:0;display:flex;align-items:center;justify-content:center;gap:.25rem}.instagram-container .instagram-wrapper .post .post-body .actions .scroll-dots div{background-color:#737373;height:.6rem;width:.6rem;border-radius:50%;cursor:pointer;z-index:1;pointer-events:all;transition:all .1s ease-in-out}.instagram-container .instagram-wrapper .post .post-body .actions .scroll-dots div:hover{background-color:#2f81edc1}.instagram-container .instagram-wrapper .post .post-body .actions .scroll-dots div.active{background-color:#2f80ed}.instagram-container .instagram-wrapper .post .post-body .liked-by{padding:0 .75rem;color:var(--instagram-primary-text);font-size:15px;font-weight:400;margin-bottom:.1rem;cursor:pointer}.instagram-container .instagram-wrapper .post .post-body .caption{padding:0 .75rem;color:var(--instagram-primary-text);font-size:16px;font-weight:400;width:90%;overflow-wrap:break-word}.instagram-container .instagram-wrapper .post .post-body .caption .user{font-weight:500;cursor:pointer;margin-right:.3rem}.instagram-container .instagram-wrapper .post .post-body .comments{padding:.5rem .75rem;color:var(--instagram-secondary-text);font-size:15px;cursor:pointer}.instagram-container .instagram-wrapper .post .post-body .date{padding:0 .75rem;color:var(--instagram-secondary-text);font-size:14px}.instagram-container .instagram-wrapper .profile .edit-profile{position:absolute;z-index:2;background-color:var(--instagram-primary);height:100%;width:100%}.instagram-container .instagram-wrapper .profile .edit-profile-header{margin-top:.5rem;width:88%;display:flex;align-items:center;justify-content:space-between;gap:.5rem;padding:0 1.5rem;color:var(--instagram-primary-text)}.instagram-container .instagram-wrapper .profile .edit-profile-header .cancel{cursor:pointer;font-size:18px;font-weight:400}.instagram-container .instagram-wrapper .profile .edit-profile-header .title{font-size:20px;font-weight:600}.instagram-container .instagram-wrapper .profile .edit-profile-header .save{cursor:pointer;font-size:18px;font-weight:500;color:var(--instagram-blue)}.instagram-container .instagram-wrapper .profile .edit-profile-body{display:flex;flex-direction:column;align-items:center;margin-top:.3rem;padding:1rem 2rem}.instagram-container .instagram-wrapper .profile .edit-profile-body .profile-picture{display:flex;flex-direction:column;align-items:center;gap:.5rem;cursor:pointer}.instagram-container .instagram-wrapper .profile .edit-profile-body .profile-picture img{object-fit:cover;width:6rem;height:6rem;border-radius:50%}.instagram-container .instagram-wrapper .profile .edit-profile-body .profile-picture span{color:var(--instagram-blue);font-size:16px;font-weight:500}.instagram-container .instagram-wrapper .profile .edit-profile-body .profile-items{display:flex;flex-direction:column;align-items:center;margin-top:2rem;gap:1rem;width:100%;color:var(--instagram-primary-text);font-weight:500;font-size:18px}.instagram-container .instagram-wrapper .profile .edit-profile-body .profile-items .item{display:flex;flex-direction:row;align-items:center;border-top:1px solid var(--instagram-border);padding:.5rem 0;width:100%}.instagram-container .instagram-wrapper .profile .edit-profile-body .profile-items .item .switch-wrapper{width:100%;display:flex;align-items:flex-end}.instagram-container .instagram-wrapper .profile .edit-profile-body .profile-items .item:last-child{border-bottom:1px solid var(--instagram-border)}.instagram-container .instagram-wrapper .profile .edit-profile-body .profile-items .item input,.instagram-container .instagram-wrapper .profile .edit-profile-body .profile-items .item textarea{margin-left:auto;width:17rem;height:2rem;resize:none;font-size:18px;font-weight:500;border:none;font-family:inherit;background-color:transparent;color:var(--instagram-primary-text)}.instagram-container .instagram-wrapper .profile .edit-profile-body .profile-items .item input::placeholder,.instagram-container .instagram-wrapper .profile .edit-profile-body .profile-items .item textarea::placeholder{font-size:18px}.instagram-container .instagram-wrapper .profile .edit-profile-body .profile-items .item input:active,.instagram-container .instagram-wrapper .profile .edit-profile-body .profile-items .item input:focus,.instagram-container .instagram-wrapper .profile .edit-profile-body .profile-items .item textarea:active,.instagram-container .instagram-wrapper .profile .edit-profile-body .profile-items .item textarea:focus{outline:none}.instagram-container .instagram-wrapper .profile .edit-profile-body .profile-items .item textarea{min-height:4rem}.instagram-container .instagram-wrapper .profile .edit-profile-body .profile-items .item textarea::-webkit-scrollbar{display:none}.instagram-container .instagram-wrapper .profile .edit-profile-body .buttons{display:flex;flex-direction:column;gap:.75rem;margin-top:8rem}.instagram-container .instagram-wrapper .profile .edit-profile-body .buttons .button{color:#fff;font-size:16px;text-align:center;cursor:pointer;padding:.35rem 1rem;border-radius:12px}.instagram-container .instagram-wrapper .profile .edit-profile-body .buttons .button.blue{color:var(--instagram-blue);background-color:var(--phone-color-highlight3)}.instagram-container .instagram-wrapper .profile .edit-profile-body .buttons .button.red{background-color:var(--phone-color-red)}.instagram-container .instagram-wrapper .profile .profile-header{margin-top:4rem;width:88%;display:flex;align-items:center;justify-content:space-between;gap:.5rem;padding:0 1.5rem;color:var(--instagram-primary-text)}.instagram-container .instagram-wrapper .profile .profile-header i{cursor:pointer;transition:all .1s ease-in-out;font-size:18px;font-weight:600;flex:1}.instagram-container .instagram-wrapper .profile .profile-header div{font-size:18px;font-weight:600;text-align:center;display:flex;align-items:center;gap:.2rem}.instagram-container .instagram-wrapper .profile .profile-header span{flex:1}.instagram-container .instagram-wrapper .profile .profile-body{display:flex;flex-direction:column;margin-top:.3rem}.instagram-container .instagram-wrapper .profile .profile-body .profile-wrapper{padding:1rem 1rem 2rem}.instagram-container .instagram-wrapper .profile .profile-body .profile-wrapper .top{display:flex;align-items:center;justify-content:space-between;padding-bottom:.75rem}.instagram-container .instagram-wrapper .profile .profile-body .profile-wrapper .top .live{margin-top:10px;height:135%;width:140%}.instagram-container .instagram-wrapper .profile .profile-body .profile-wrapper .top .stats{display:flex;align-items:center;gap:1rem;margin-right:1rem}.instagram-container .instagram-wrapper .profile .profile-body .profile-wrapper .top .stats div{display:flex;flex-direction:column;align-items:center;color:var(--instagram-primary-text);cursor:pointer}.instagram-container .instagram-wrapper .profile .profile-body .profile-wrapper .top .stats div[data-disabled=true]{cursor:default}.instagram-container .instagram-wrapper .profile .profile-body .profile-wrapper .top .stats div[data-disabled=true] .value{color:var(--instagram-secondary-text)}.instagram-container .instagram-wrapper .profile .profile-body .profile-wrapper .top .stats div .value{font-size:18px;font-weight:500}.instagram-container .instagram-wrapper .profile .profile-body .profile-wrapper .top .stats div .label{font-size:16px;font-weight:400}.instagram-container .instagram-wrapper .profile .profile-body .profile-wrapper .name{font-size:18px;font-weight:600;color:var(--instagram-primary-text)}.instagram-container .instagram-wrapper .profile .profile-body .profile-wrapper .description{font-size:16px;font-weight:400;color:var(--instagram-primary-text);padding-bottom:.75rem;max-width:60%;overflow-wrap:break-word}.instagram-container .instagram-wrapper .profile .profile-body .profile-wrapper .buttons{display:flex;align-items:center;gap:.25rem}.instagram-container .instagram-wrapper .profile .profile-body .profile-wrapper .button{display:flex;align-items:center;justify-content:center;gap:.5rem;width:100%;padding:.4rem .7rem;border-radius:7px;cursor:pointer;transition:all .1s ease-in-out;font-size:16px;font-weight:600}.instagram-container .instagram-wrapper .profile .profile-body .profile-wrapper .button:hover{filter:brightness(.9)}.instagram-container .instagram-wrapper .profile .profile-body .profile-wrapper .button.follow{color:#fff;background-color:#0095f6}.instagram-container .instagram-wrapper .profile .profile-body .profile-wrapper .button.following,.instagram-container .instagram-wrapper .profile .profile-body .profile-wrapper .button.edit{background-color:#dadada;color:#000}.instagram-container .instagram-wrapper .profile .profile-body .private-account{min-height:25rem;display:flex;flex-direction:column;align-items:center;justify-content:center;gap:.75rem}.instagram-container .instagram-wrapper .profile .profile-body .private-account .icon{display:flex;align-items:center;justify-content:center;border:4px solid var(--instagram-primary-text);border-radius:50%}.instagram-container .instagram-wrapper .profile .profile-body .private-account .icon svg{padding:.75rem;font-size:50px;color:var(--instagram-primary-text)}.instagram-container .instagram-wrapper .profile .profile-body .private-account .title{font-size:18px;font-weight:700;line-height:10px;margin-top:.5rem;color:var(--instagram-primary-text)}.instagram-container .instagram-wrapper .profile .profile-body .private-account .subtitle{line-height:5px;font-size:16px;color:var(--instagram-secondary-text)}.instagram-container .instagram-wrapper .profile .profile-body .posts{display:grid;grid-template-columns:repeat(3,1fr);gap:.15rem;border-top:1px solid rgba(69,69,69,.**********);padding:.2rem .1rem 5rem;overflow-y:auto;overflow-x:hidden;max-height:29rem}.instagram-container .instagram-wrapper .profile .profile-body .posts::-webkit-scrollbar{display:none}.instagram-container .instagram-wrapper .profile .profile-body .posts div{width:100%;height:8rem;max-height:8rem;cursor:pointer;transition:all .1s ease-in-out}.instagram-container .instagram-wrapper .profile .profile-body .posts div:hover{filter:brightness(.6)}.instagram-container .instagram-wrapper .profile .profile-body .posts div img,.instagram-container .instagram-wrapper .profile .profile-body .posts div video{width:100%;height:100%;object-fit:cover}.instagram-container .instagram-wrapper .profile .profile-body .profile-picture{width:6rem;height:6rem;border-radius:50%;display:flex;align-items:center;justify-content:center;box-shadow:0 0 5px #0000004d}.instagram-container .instagram-wrapper .profile .profile-body .profile-picture .avatar{width:100%;height:100%;border-radius:50%}.instagram-container .instagram-wrapper .profile .profile-body .profile-picture .circle{position:absolute;top:0;left:0;transform:scale(1.1);width:100%;height:100%;background-position:center;background-size:cover;cursor:pointer}.instagram-container .instagram-wrapper .profile .profile-body .profile-picture .circle.live{width:100%;height:100%;transform:scale(1.08);margin-bottom:.25rem}.instagram-container .instagram-wrapper .profile .profile-body .profile-picture .circle[data-seen=true]{filter:grayscale(90%);opacity:.9}.instagram-container .instagram-wrapper .instagram-live-container{width:100%;height:100%;position:relative;background-color:#000}.instagram-container .instagram-wrapper .instagram-streams{display:grid;grid-template-columns:repeat(1,1fr);background-color:#000;width:100%;height:100%}.instagram-container .instagram-wrapper .instagram-streams[data-streams="3"],.instagram-container .instagram-wrapper .instagram-streams[data-streams="4"]{grid-gap:.5rem;height:75%;padding-top:7rem}.instagram-container .instagram-wrapper .instagram-streams[data-streams="3"] .instagram-stream,.instagram-container .instagram-wrapper .instagram-streams[data-streams="4"] .instagram-stream{box-shadow:inset 0 0 10px #0000004d;border-radius:6px}.instagram-container .instagram-wrapper .instagram-streams[data-streams="3"]{grid-template-columns:repeat(2,1fr);grid-template-rows:repeat(2,1fr)}.instagram-container .instagram-wrapper .instagram-streams[data-streams="3"] :first-child{grid-column:span 2}.instagram-container .instagram-wrapper .instagram-streams[data-streams="4"]{grid-template-columns:repeat(2,1fr);grid-template-rows:repeat(2,1fr)}.instagram-container .instagram-wrapper .instagram-streams .instagram-stream{height:100%!important;width:100%;overflow:hidden;position:relative;border-radius:0}.instagram-container .instagram-wrapper .instagram-streams .instagram-stream video,.instagram-container .instagram-wrapper .instagram-streams .instagram-stream canvas{height:100%!important;width:100%;filter:brightness(.9);object-fit:cover}.instagram-container .instagram-wrapper .instagram-streams .instagram-stream svg{position:absolute;z-index:4;top:.75rem;right:.75rem;font-size:30px;color:#fff;opacity:.9;cursor:pointer;pointer-events:all}.instagram-container .instagram-wrapper .instagram-stream-ended{position:absolute;top:0;left:0;width:100%;height:100%;background-color:#0006;-webkit-backdrop-filter:blur(10px);backdrop-filter:blur(10px);z-index:21;display:flex;align-items:center;justify-content:center}.instagram-container .instagram-wrapper .instagram-stream-ended .title{font-size:26px;font-weight:500;color:#fff}.instagram-container .instagram-wrapper .instagram-stream-ended svg{position:absolute;top:3.5rem;right:1rem;color:#fff;font-size:40px;cursor:pointer;transition:all .2s ease-in-out}.instagram-container .instagram-wrapper .instagram-stream-ended svg:hover{filter:brightness(.8)}.instagram-container .instagram-wrapper .instagram-story{height:100%;width:100%;background-color:#000}.instagram-container .instagram-wrapper .instagram-story .background-image{position:absolute;top:3rem;height:82%;width:99%;background-position:center;background-size:cover;background-repeat:no-repeat;border-radius:10px;filter:brightness(.8)}.instagram-container .instagram-wrapper .instagram-story .story-wrapper{padding-top:3.5rem;display:flex;flex-direction:column;align-items:center}.instagram-container .instagram-wrapper .instagram-story .story-wrapper .story-header{width:100%;display:flex;flex-direction:column;align-items:center;gap:.75rem;z-index:1}.instagram-container .instagram-wrapper .instagram-story .story-wrapper .story-header .story-steps{width:100%;height:.2rem;width:95%;display:flex;align-items:center;gap:.5rem}.instagram-container .instagram-wrapper .instagram-story .story-wrapper .story-header .story-steps>div{height:100%;width:100%;background-color:#fff;opacity:.5;border-radius:2px;cursor:pointer;transition:all .2s ease-in-out}.instagram-container .instagram-wrapper .instagram-story .story-wrapper .story-header .story-steps>div.active{opacity:1}.instagram-container .instagram-wrapper .instagram-story .story-wrapper .story-header .story-steps>div:hover:not(.active){opacity:.7}.instagram-container .instagram-wrapper .instagram-story .story-wrapper .story-header .profile-details{width:92%;display:flex;align-items:center;justify-content:space-between;gap:.5rem;padding:0 1.5rem;cursor:pointer}.instagram-container .instagram-wrapper .instagram-story .story-wrapper .story-header .profile-details .profile{display:flex;align-items:center;gap:.5rem}.instagram-container .instagram-wrapper .instagram-story .story-wrapper .story-header .profile-details .profile .profile-picture .avatar{width:2.25rem;height:2.25rem;border-radius:50%}.instagram-container .instagram-wrapper .instagram-story .story-wrapper .story-header .profile-details .profile .name{font-size:18px;font-weight:500;color:#fff;display:flex;align-items:center;gap:.2rem}.instagram-container .instagram-wrapper .instagram-story .story-wrapper .story-header .profile-details .time{color:#fff;opacity:.75;font-size:16px}.instagram-container .instagram-wrapper .instagram-story .story-wrapper .story-header .profile-details .stats svg{color:#d7d7d7e6;font-size:35px;cursor:pointer}.instagram-container .instagram-wrapper .instagram-story .story-wrapper .story-post{width:70%;height:100%;z-index:3;margin-top:10rem;cursor:pointer}.instagram-container .instagram-wrapper .instagram-story .story-wrapper .story-post .post-wrapper{position:relative;display:flex;flex-direction:column;gap:.25rem;width:100%;height:100%}.instagram-container .instagram-wrapper .instagram-story .story-wrapper .story-post .post-wrapper img,.instagram-container .instagram-wrapper .instagram-story .story-wrapper .story-post .post-wrapper video{aspect-ratio:1/1;object-fit:cover;object-position:center;width:100%;border-radius:.25rem;box-shadow:0 0 25px 5px #00000026}.instagram-container .instagram-wrapper .instagram-story .story-wrapper .story-post .post-wrapper .post-username{font-size:14px;font-weight:500;color:#fffc}.instagram-container .instagram-wrapper .instagram-story .story-wrapper .story-footer{position:absolute;bottom:3rem;left:0;right:0;margin:0 auto;width:90%;z-index:1;display:flex;align-items:center;justify-content:space-between;gap:1rem}.instagram-container .instagram-wrapper .instagram-story .story-wrapper .story-footer[data-stats=true]{bottom:1.75rem}.instagram-container .instagram-wrapper .instagram-story .story-wrapper .story-footer .seen-by{display:flex;flex-direction:column;align-items:center;gap:.25rem;font-size:15px;font-weight:500;color:#fff;cursor:pointer;transition:all .2s ease-in-out}.instagram-container .instagram-wrapper .instagram-story .story-wrapper .story-footer .seen-by:hover{filter:brightness(.8)}.instagram-container .instagram-wrapper .instagram-story .story-wrapper .story-footer .seen-by .images{min-height:50px;display:flex;flex-direction:row;align-items:center}.instagram-container .instagram-wrapper .instagram-story .story-wrapper .story-footer .seen-by .images img{width:2.5rem;height:2.5rem;border-radius:50%;border:5px solid #000000;object-fit:cover;margin-left:-1rem}.instagram-container .instagram-wrapper .instagram-story .story-wrapper .story-footer .seen-by .images img:first-child{margin-left:0}.instagram-container .instagram-wrapper .instagram-story .story-wrapper .story-footer .input{width:75%}.instagram-container .instagram-wrapper .instagram-story .story-wrapper .story-footer .input input{width:100%;padding:.75rem 1rem;border-radius:20px;font-size:16px;color:#fff;border:2px solid #dbdbdb;opacity:.4;background:transparent}.instagram-container .instagram-wrapper .instagram-story .story-wrapper .story-footer .input input::placeholder{font-size:16px;color:#fff}.instagram-container .instagram-wrapper .instagram-story .story-wrapper .story-footer .input input:active,.instagram-container .instagram-wrapper .instagram-story .story-wrapper .story-footer .input input:focus{outline:none}.instagram-container .instagram-wrapper .instagram-story .story-wrapper .story-footer svg{cursor:pointer;transition:all .1s ease-in-out;color:#fff;font-size:28px;font-weight:300;margin-right:.5rem}.instagram-container .instagram-wrapper .story-modal{position:absolute;bottom:0;z-index:5;height:60%;width:100%;background-color:var(--instagram-primary);display:flex;flex-direction:column;box-sizing:border-box}.instagram-container .instagram-wrapper .story-modal[data-type=live]{border-radius:12px 12px 0 0}.instagram-container .instagram-wrapper .story-modal[data-type=live] .story-modal-header{display:flex;flex-direction:column;align-items:center;justify-content:center;gap:.75rem}.instagram-container .instagram-wrapper .story-modal[data-type=live] .story-modal-header .title{font-size:20px;font-weight:500;padding:0 .75rem;color:var(--instagram-primary-text)}.instagram-container .instagram-wrapper .story-modal[data-type=live] .story-modal-header .close-border{height:6px;width:3rem;border-radius:6px;background-color:var(--phone-color-highlight3);cursor:pointer}.instagram-container .instagram-wrapper .story-modal .story-modal-header{display:flex;align-items:center;justify-content:space-between;width:100%;box-sizing:border-box;padding:.75rem;border-top:1px solid var(--phone-color-border);border-bottom:1px solid var(--phone-color-border)}.instagram-container .instagram-wrapper .story-modal .story-modal-header .close{font-size:25px;color:var(--instagram-primary-text);cursor:pointer}.instagram-container .instagram-wrapper .story-modal .story-modal-header .views{display:flex;align-items:center;gap:.4rem}.instagram-container .instagram-wrapper .story-modal .story-modal-header .views svg{font-size:25px;color:var(--instagram-primary-text)}.instagram-container .instagram-wrapper .story-modal .story-modal-header .views .views{color:var(--instagram-primary-text);font-size:16px;font-weight:500}.instagram-container .instagram-wrapper .story-modal .story-modal-content{display:flex;flex-direction:column;gap:1rem;width:100%;height:100%;padding-top:1rem;overflow:hidden}.instagram-container .instagram-wrapper .story-modal .story-modal-content .title{font-size:20px;font-weight:500;padding:0 .75rem;color:var(--instagram-primary-text)}.instagram-container .instagram-wrapper .story-modal .story-modal-content .viewers{display:flex;flex-direction:column;max-height:100%;overflow-y:auto;padding-bottom:7rem}.instagram-container .instagram-wrapper .story-modal .story-modal-content .viewers::-webkit-scrollbar{display:none}.instagram-container .instagram-wrapper .story-modal .story-modal-content .viewers .item{display:flex;align-items:center;justify-content:space-between;gap:.75rem;padding:.5rem .75rem;cursor:pointer;transition:all .2s ease-in-out}.instagram-container .instagram-wrapper .story-modal .story-modal-content .viewers .item:hover{background-color:var(--phone-color-hover)}.instagram-container .instagram-wrapper .story-modal .story-modal-content .viewers .item .user{display:flex;align-items:center;gap:.6rem;cursor:pointer}.instagram-container .instagram-wrapper .story-modal .story-modal-content .viewers .item .user:last-child{margin-left:.5rem}.instagram-container .instagram-wrapper .story-modal .story-modal-content .viewers .item .user .profile-picture{width:4rem;height:4rem}.instagram-container .instagram-wrapper .story-modal .story-modal-content .viewers .item .user .profile-picture img{object-fit:cover;width:100%;height:100%;border-radius:50%}.instagram-container .instagram-wrapper .story-modal .story-modal-content .viewers .item .user .user-details{display:flex;flex-direction:column;line-height:20px}.instagram-container .instagram-wrapper .story-modal .story-modal-content .viewers .item .user .user-details .username{font-size:18px;color:var(--instagram-primary-text);font-weight:600;display:flex;align-items:center;gap:.2rem}.instagram-container .instagram-wrapper .story-modal .story-modal-content .viewers .item .user .user-details .name{font-size:16px;color:var(--instagram-secondary-text);font-weight:400}.instagram-container .instagram-wrapper .story-modal .story-modal-content .viewers .item .user .user-details .following{font-size:14px;color:var(--instagram-secondary-text)}.instagram-container .instagram-wrapper .story-modal .story-modal-content .viewers .item .invite,.instagram-container .instagram-wrapper .story-modal .story-modal-content .viewers .item .remove{display:flex;align-items:center;justify-content:center;gap:.5rem;padding:.4rem 1.25rem;border-radius:7px;font-size:15px;font-weight:600;color:#fff;background-color:var(--instagram-blue);cursor:pointer;transition:all .1s ease-in-out}.instagram-container .instagram-wrapper .story-modal .story-modal-content .viewers .item .invite.remove,.instagram-container .instagram-wrapper .story-modal .story-modal-content .viewers .item .remove.remove{background-color:var(--phone-color-red)}.instagram-container .instagram-wrapper .story-modal .story-modal-content .viewers .item .invite:hover,.instagram-container .instagram-wrapper .story-modal .story-modal-content .viewers .item .remove:hover{filter:brightness(.9)}.instagram-container .instagram-wrapper .instagram-live{z-index:2;height:100%;width:100%;position:absolute;top:0;display:flex;flex-direction:column;align-items:center;justify-content:space-between}.instagram-container .instagram-wrapper .instagram-live .profile-picture .avatar{width:2.5rem;height:2.5rem;border-radius:50%}.instagram-container .instagram-wrapper .instagram-live .live-header{margin-top:3.5rem;width:88%;display:flex;align-items:center;justify-content:space-between;gap:.5rem;padding:0 1.5rem}.instagram-container .instagram-wrapper .instagram-live .live-header .profile{display:flex;align-items:center;gap:.5rem;cursor:pointer}.instagram-container .instagram-wrapper .instagram-live .live-header .profile .name{font-size:18px;font-weight:500;color:#fff;display:flex;align-items:center;gap:.2rem}.instagram-container .instagram-wrapper .instagram-live .live-header .profile .name svg{font-size:22px}.instagram-container .instagram-wrapper .instagram-live .live-header .stats{display:flex;align-items:center;gap:.75rem;margin-right:.2rem}.instagram-container .instagram-wrapper .instagram-live .live-header .stats div{padding:.5rem .8rem;background-color:#000;border-radius:10px;color:#d7d7d7e6}.instagram-container .instagram-wrapper .instagram-live .live-header .stats .live{text-transform:uppercase;font-size:13px;font-weight:500;background:linear-gradient(35deg,#ffd600 -20%,#ff7a00 27%,#ff0169 73%,#e6e6fa 120%);overflow:hidden;max-width:2.5rem}.instagram-container .instagram-wrapper .instagram-live .live-header .stats .viewers{display:flex;align-items:center;gap:.3rem;text-transform:uppercase;font-size:14px;font-weight:400;background-color:#0006;cursor:pointer}.instagram-container .instagram-wrapper .instagram-live .live-header .stats .x{margin-left:.25rem;color:#ffffffe6;font-size:22px;font-weight:300;cursor:pointer}.instagram-container .instagram-wrapper .instagram-live .comment-section{width:85%;display:flex;flex-direction:column;gap:1.5rem;margin-bottom:2rem}.instagram-container .instagram-wrapper .instagram-live .comment-section .comment-feed{display:flex;flex-direction:column;gap:.5rem;max-height:10rem;overflow-y:auto}.instagram-container .instagram-wrapper .instagram-live .comment-section .comment-feed::-webkit-scrollbar{display:none}.instagram-container .instagram-wrapper .instagram-live .comment-section .comment-feed .comment{display:flex;align-items:center;gap:.75rem}.instagram-container .instagram-wrapper .instagram-live .comment-section .comment-feed .comment img{width:2.75rem;height:2.75rem;border-radius:50%}.instagram-container .instagram-wrapper .instagram-live .comment-section .comment-feed .comment .info{display:flex;flex-direction:column;font-size:17px;color:#fff;max-width:75%}.instagram-container .instagram-wrapper .instagram-live .comment-section .comment-feed .comment .info .name{font-size:18px;font-weight:500;display:flex;align-items:center;gap:.2rem}.instagram-container .instagram-wrapper .instagram-live .comment-section .comment-feed .comment .info .name .verified img{width:1.2rem;height:1.2rem}.instagram-container .instagram-wrapper .instagram-live .comment-section .comment-feed .comment .info .comment-text{font-weight:400;overflow-wrap:break-word}.instagram-container .instagram-wrapper .instagram-live .comment-section .comment-input{display:flex;align-items:center;gap:1rem;width:100%}.instagram-container .instagram-wrapper .instagram-live .comment-section .comment-input input{width:100%;padding:.75rem 1rem;border-radius:20px;font-size:18px;color:#fff;border:2px solid #dbdbdb;background:transparent}.instagram-container .instagram-wrapper .instagram-live .comment-section .comment-input input::placeholder{font-size:18px;color:#fff}.instagram-container .instagram-wrapper .instagram-live .comment-section .comment-input input:active,.instagram-container .instagram-wrapper .instagram-live .comment-section .comment-input input:focus{outline:none}.instagram-container .instagram-wrapper .instagram-live .comment-section .comment-input svg{cursor:pointer;transition:all .1s ease-in-out;color:#fff;font-size:42px}.instagram-container .instagram-wrapper .log-in{height:100%;display:flex;flex-direction:column;align-items:center;justify-content:center}.instagram-container .instagram-wrapper .log-in .wrapper{width:80%;display:flex;flex-direction:column;align-items:center;justify-content:center}.instagram-container .instagram-wrapper .log-in img{width:60%;margin-bottom:1.5rem}.instagram-container .instagram-wrapper .log-in img[data-theme=dark]{filter:invert(1)}.instagram-container .instagram-wrapper .log-in .form{display:flex;flex-direction:column;align-items:center;justify-content:center;gap:1rem;width:100%}.instagram-container .instagram-wrapper .log-in .form input{width:90%;padding:1rem;color:var(--instagram-primary-text);background-color:#d7d7d74d;border-radius:5px;font-size:16px;border:2px solid rgba(69,69,69,.**********);outline:none}.instagram-container .instagram-wrapper .log-in .form input:active,.instagram-container .instagram-wrapper .log-in .form input:focus{border:2px solid rgba(69,69,69,.768627451);outline:none}.instagram-container .instagram-wrapper .log-in .form input.username{text-transform:lowercase}.instagram-container .instagram-wrapper .log-in .form .button{width:100%;padding:1rem 0;background-color:#3997f0;text-align:center;font-size:17px;font-weight:500;color:#ffffffd6;border-radius:7.5px;cursor:pointer;transition:all .1s ease-in-out}.instagram-container .instagram-wrapper .log-in .form .button:hover{filter:brightness(.9)}.instagram-container .instagram-wrapper .log-in .form .instagram-wrong{color:var(--phone-color-red);font-size:14px;margin-top:-.75rem;margin-right:auto}.instagram-container .instagram-wrapper .log-in .footer{position:absolute;bottom:0;margin-bottom:2.75rem;font-size:17px;font-weight:400;color:var(--instagram-secondary-text)}.instagram-container .instagram-wrapper .log-in .footer span{cursor:pointer;color:#000;font-size:17px;font-weight:600;color:var(--instagram-primary-text)}.instagram-container .instagram-wrapper .search-container{animation:slideRight .5s cubic-bezier(.19,1,.22,1)}.instagram-container .instagram-wrapper .search-container .search-header{margin-top:4rem;width:88%;display:flex;align-items:center;justify-content:space-between;gap:.5rem;padding:0 1.5rem}.instagram-container .instagram-wrapper .search-container .search-body{width:100%;align-items:center;display:flex;flex-direction:column;margin-top:1.5rem;max-height:44.5rem;overflow-y:auto}.instagram-container .instagram-wrapper .search-container .search-body::-webkit-scrollbar{display:none}.instagram-container .instagram-wrapper .search-container .search-body .feed-grid{display:grid;grid-template-columns:repeat(3,1fr);grid-gap:.15rem;width:100%}.instagram-container .instagram-wrapper .search-container .search-body .feed-grid .grid-post{height:8.5rem;width:100%}.instagram-container .instagram-wrapper .search-container .search-body .feed-grid .grid-post.big{grid-column:span 2;grid-row:span 2;height:17rem}.instagram-container .instagram-wrapper .search-container .search-body .feed-grid .grid-post img,.instagram-container .instagram-wrapper .search-container .search-body .feed-grid .grid-post video{width:100%;height:100%;object-fit:cover;cursor:pointer;transition:all .1s ease-in-out}.instagram-container .instagram-wrapper .search-container .search-body .feed-grid .grid-post img:hover,.instagram-container .instagram-wrapper .search-container .search-body .feed-grid .grid-post video:hover{filter:brightness(.8)}.instagram-container .instagram-wrapper .search-container .search-body .search-results{display:flex;flex-direction:column;gap:1rem;width:85%}.instagram-container .instagram-wrapper .search-container .search-body .search-results .item{display:flex;align-items:center;gap:.75rem;cursor:pointer}.instagram-container .instagram-wrapper .search-container .search-body .search-results .item .profile-picture{width:4rem;height:4rem}.instagram-container .instagram-wrapper .search-container .search-body .search-results .item .profile-picture img{object-fit:cover;width:100%;height:100%;border-radius:50%}.instagram-container .instagram-wrapper .search-container .search-body .search-results .item .user{display:flex;flex-direction:column;line-height:20px}.instagram-container .instagram-wrapper .search-container .search-body .search-results .item .user .username{font-size:18px;color:var(--instagram-primary-text);font-weight:600;display:flex;align-items:center;gap:.2rem}.instagram-container .instagram-wrapper .search-container .search-body .search-results .item .user .name{font-size:16px;color:var(--instagram-secondary-text);font-weight:400}.instagram-container .instagram-wrapper .search-container .search-body .search-results .item .user .following{font-size:14px;color:var(--instagram-secondary-text)}.instagram-container .instagram-wrapper .requests-container{width:100%;height:100%;position:absolute;left:0;top:0;z-index:3;display:flex;flex-direction:column;gap:1rem;background-color:var(--instagram-primary)}.instagram-container .instagram-wrapper .requests-container .requests-header{display:flex;align-items:center;justify-content:space-between;padding:3.5rem .75rem 0}.instagram-container .instagram-wrapper .requests-container .requests-header div,.instagram-container .instagram-wrapper .requests-container .requests-header span{flex:1}.instagram-container .instagram-wrapper .requests-container .requests-header .icon{display:flex;align-items:flex-start}.instagram-container .instagram-wrapper .requests-container .requests-header .icon svg{font-size:28px;color:var(--instagram-primary-text)}.instagram-container .instagram-wrapper .requests-container .requests-header .title{font-size:17px;font-weight:500;color:var(--instagram-primary-text);text-align:center}.instagram-container .instagram-wrapper .requests-container .requests-body{display:flex;flex-direction:column;align-items:center;gap:1rem}.instagram-container .instagram-wrapper .requests-container .requests-body .searchbox{width:85%}.instagram-container .instagram-wrapper .requests-container .requests-body .items{display:flex;flex-direction:column;width:100%;max-height:100%;overflow-y:auto;overflow-x:hidden}.instagram-container .instagram-wrapper .requests-container .requests-body .items::-webkit-scrollbar{display:none}.instagram-container .instagram-wrapper .requests-container .requests-body .items .item{display:flex;align-items:center;justify-content:space-between;width:100%;padding:.7rem 1rem;box-sizing:border-box;cursor:pointer;transition:all .2s ease-in-out}.instagram-container .instagram-wrapper .requests-container .requests-body .items .item:hover{background-color:var(--phone-color-hover)}.instagram-container .instagram-wrapper .requests-container .requests-body .items .item .user{display:flex;align-items:center;gap:.75rem}.instagram-container .instagram-wrapper .requests-container .requests-body .items .item .user .avatar{width:3.25rem;height:3.25rem;border-radius:50%;object-fit:cover}.instagram-container .instagram-wrapper .requests-container .requests-body .items .item .user .user-info{display:flex;flex-direction:column;font-size:15px;font-weight:400}.instagram-container .instagram-wrapper .requests-container .requests-body .items .item .user .user-info .username{display:flex;align-items:center;gap:.2rem;font-weight:500;color:var(--instagram-primary-text)}.instagram-container .instagram-wrapper .requests-container .requests-body .items .item .user .user-info .username .verified{display:flex;align-items:center;justify-content:center}.instagram-container .instagram-wrapper .requests-container .requests-body .items .item .user .user-info .name{color:var(--instagram-secondary-text)}.instagram-container .instagram-wrapper .requests-container .requests-body .items .item .buttons{display:flex;align-items:center;gap:.5rem}.instagram-container .instagram-wrapper .requests-container .requests-body .items .item .buttons .button{padding:.4rem .8rem;border-radius:7px;cursor:pointer;transition:all .1s ease-in-out;font-size:14px;font-weight:500}.instagram-container .instagram-wrapper .requests-container .requests-body .items .item .buttons .button:hover{filter:brightness(.9)}.instagram-container .instagram-wrapper .requests-container .requests-body .items .item .buttons .button.follow{color:#fff;background-color:#0095f6}.instagram-container .instagram-wrapper .requests-container .requests-body .items .item .buttons .button.following{background-color:#dadada;color:#000}.instagram-container .instagram-wrapper .notifications-container{animation:slideRight .5s cubic-bezier(.19,1,.22,1)}.instagram-container .instagram-wrapper .notifications-container .notifications-header{margin-top:3.5rem;width:88%;display:flex;font-size:25px;font-weight:700;padding:0 1.5rem}.instagram-container .instagram-wrapper .notifications-container .notifications-body{align-items:center;display:flex;flex-direction:column;margin-top:1.5rem;max-height:43rem;gap:1.25rem;overflow-y:auto}.instagram-container .instagram-wrapper .notifications-container .notifications-body::-webkit-scrollbar{display:none}.instagram-container .instagram-wrapper .notifications-container .notifications-body .follow-requests{width:100%;display:flex;align-items:center;justify-content:space-between;padding:.5rem 1.75rem;box-sizing:border-box;margin-bottom:.25rem;cursor:pointer;transition:all .2s ease-in-out}.instagram-container .instagram-wrapper .notifications-container .notifications-body .follow-requests:hover{background-color:var(--phone-color-hover)}.instagram-container .instagram-wrapper .notifications-container .notifications-body .follow-requests .item-data{display:flex;align-items:center;gap:1rem}.instagram-container .instagram-wrapper .notifications-container .notifications-body .follow-requests .item-data .profile-pictures{display:flex;align-items:center;justify-content:center;width:2.75rem}.instagram-container .instagram-wrapper .notifications-container .notifications-body .follow-requests .item-data .profile-pictures img{width:2.75rem;height:2.75rem;border-radius:50%}.instagram-container .instagram-wrapper .notifications-container .notifications-body .follow-requests .item-data .profile-pictures[data-multiple=true]{position:relative}.instagram-container .instagram-wrapper .notifications-container .notifications-body .follow-requests .item-data .profile-pictures[data-multiple=true] img{position:absolute;border-radius:50%;width:2.25rem;height:2.25rem;border:2px solid var(--instagram-primary)}.instagram-container .instagram-wrapper .notifications-container .notifications-body .follow-requests .item-data .profile-pictures[data-multiple=true] img:nth-child(1){bottom:-.75rem;left:-.25rem;z-index:1}.instagram-container .instagram-wrapper .notifications-container .notifications-body .follow-requests .item-data .profile-pictures[data-multiple=true] img:nth-child(2){top:-.75rem;right:-.25rem;z-index:2}.instagram-container .instagram-wrapper .notifications-container .notifications-body .follow-requests .item-data .item-body{display:flex;flex-direction:column}.instagram-container .instagram-wrapper .notifications-container .notifications-body .follow-requests .item-data .item-body .title{color:var(--instagram-primary-text);font-size:16px;font-weight:500}.instagram-container .instagram-wrapper .notifications-container .notifications-body .follow-requests .item-data .item-body .subtitle{color:var(--instagram-secondary-text);font-size:14px}.instagram-container .instagram-wrapper .notifications-container .notifications-body .follow-requests .actions{display:flex;align-items:center;gap:.75rem}.instagram-container .instagram-wrapper .notifications-container .notifications-body .follow-requests .actions .blue-dot{width:.65rem;height:.65rem;border-radius:50%;background-color:var(--instagram-blue)}.instagram-container .instagram-wrapper .notifications-container .notifications-body .follow-requests .actions svg{font-size:20px;color:var(--instagram-secondary-text)}.instagram-container .instagram-wrapper .notifications-container .notifications-body .notification-item{width:88%;display:flex;align-items:center;justify-content:space-between}.instagram-container .instagram-wrapper .notifications-container .notifications-body .notification-item .notification-body{display:flex;align-items:center;gap:.75rem;cursor:pointer;width:90%}.instagram-container .instagram-wrapper .notifications-container .notifications-body .notification-item .notification-body .avatar{width:3.25rem;height:3.25rem;border-radius:50%;object-fit:cover}.instagram-container .instagram-wrapper .notifications-container .notifications-body .notification-item .notification-body .content{font-size:16px;font-weight:400;max-width:75%;color:var(--instagram-primary-text)}.instagram-container .instagram-wrapper .notifications-container .notifications-body .notification-item .notification-body .content span{font-weight:500}.instagram-container .instagram-wrapper .notifications-container .notifications-body .notification-item .notification-body .content span.date{font-weight:400;color:var(--instagram-secondary-text);margin-left:.25rem}.instagram-container .instagram-wrapper .notifications-container .notifications-body .notification-item .post-preview{height:3.25rem;width:3.25rem}.instagram-container .instagram-wrapper .notifications-container .notifications-body .notification-item .button{display:flex;align-items:center;justify-content:center;gap:.5rem;width:6.5rem;padding:.4rem 0rem;border-radius:7px;cursor:pointer;transition:all .1s ease-in-out;font-size:15px;font-weight:600}.instagram-container .instagram-wrapper .notifications-container .notifications-body .notification-item .button:hover{filter:brightness(.9)}.instagram-container .instagram-wrapper .notifications-container .notifications-body .notification-item .button.follow{color:#fff;background-color:#0095f6}.instagram-container .instagram-wrapper .notifications-container .notifications-body .notification-item .button.following{background-color:#dadada;color:#000}.instagram-container .instagram-wrapper .comments-container{height:85%;position:absolute;top:0;left:0;z-index:3;height:100%;width:100%;background-color:var(--instagram-primary)}.instagram-container .instagram-wrapper .comments-container .comments-header{margin-top:4rem;width:88%;display:flex;align-items:center;justify-content:space-between;gap:.5rem;padding:0 1.5rem;color:var(--instagram-primary-text)}.instagram-container .instagram-wrapper .comments-container .comments-header .title{flex:1;text-align:center;font-size:21px;font-weight:600}.instagram-container .instagram-wrapper .comments-container .comments-header svg{font-size:32px;cursor:pointer}.instagram-container .instagram-wrapper .comments-container .comments-body{display:flex;flex-direction:column;width:88%;height:67.5%;overflow-y:auto;overflow-x:hidden;position:relative;margin-top:1.5rem;padding:0 1.5rem 4rem}.instagram-container .instagram-wrapper .comments-container .comments-body::-webkit-scrollbar{display:none}.instagram-container .instagram-wrapper .comments-container .comments-body .profile-picture{margin-top:.25rem;cursor:pointer}.instagram-container .instagram-wrapper .comments-container .comments-body .profile-picture img{object-fit:cover;width:3rem;height:3rem;border-radius:50%}.instagram-container .instagram-wrapper .comments-container .comments-body .caption{font-size:16px;color:var(--instagram-primary-text);font-weight:500;max-width:80%;overflow-wrap:break-word}.instagram-container .instagram-wrapper .comments-container .comments-body .caption .username{margin-right:.3rem;font-weight:700;display:flex;align-items:center;gap:.2rem}.instagram-container .instagram-wrapper .comments-container .comments-body .post-data{display:flex;flex-direction:column;gap:.4rem;width:100%}.instagram-container .instagram-wrapper .comments-container .comments-body .actions{display:flex;align-items:center;gap:.5rem;color:var(--instagram-secondary-text);font-weight:500;font-size:14px}.instagram-container .instagram-wrapper .comments-container .comments-body .post-info{display:flex;align-items:flex-start;gap:.75rem;padding-bottom:1rem;border-bottom:1px solid var(--instagram-border)}.instagram-container .instagram-wrapper .comments-container .comments-body .comment{display:flex;align-items:center;justify-content:space-between;padding:.75rem 0;width:100%}.instagram-container .instagram-wrapper .comments-container .comments-body .comment .comment-data{display:flex;flex-direction:column;gap:.5rem;width:100%}.instagram-container .instagram-wrapper .comments-container .comments-body .comment .comment-data .username{display:flex;align-items:center;gap:.2rem}.instagram-container .instagram-wrapper .comments-container .comments-body .comment .comment-data .caption{max-width:80%;overflow-wrap:break-word}.instagram-container .instagram-wrapper .comments-container .comments-body .comment .user{display:flex;align-items:flex-start;gap:.75rem;width:100%;cursor:pointer}.instagram-container .instagram-wrapper .comments-container .comments-body .comment svg{cursor:pointer;font-size:18px;pointer-events:all;z-index:1}.instagram-container .instagram-wrapper .comments-container .add-comment{width:100%;display:flex;align-items:center;gap:.5rem;padding:1rem 2rem 2rem;background-color:var(--instagram-primary);border-bottom-left-radius:30px;border-bottom-right-radius:30px}.instagram-container .instagram-wrapper .comments-container .add-comment .profile-picture img{object-fit:cover;width:3.25rem;height:3.25rem;border-radius:50%}.instagram-container .instagram-wrapper .comments-container .add-comment .input-container{width:75%;position:relative;display:flex;align-items:center;border:2px solid var(--instagram-border);border-radius:50px}.instagram-container .instagram-wrapper .comments-container .add-comment .input-container input{width:90%;height:3rem;padding:0 1rem;font-size:16px;color:var(--instagram-secondary-text);border:none;background-color:transparent}.instagram-container .instagram-wrapper .comments-container .add-comment .input-container input:focus,.instagram-container .instagram-wrapper .comments-container .add-comment .input-container input:active{outline:none}.instagram-container .instagram-wrapper .comments-container .add-comment .input-container .send{color:var(--instagram-blue);z-index:9;font-size:17px;margin-right:1rem;cursor:pointer}.instagram-container .instagram-wrapper .newpost-container{height:85%;animation:slideUp .5s cubic-bezier(.19,1,.22,1);box-sizing:border-box;display:flex;flex-direction:column;align-items:center;gap:2rem}.instagram-container .instagram-wrapper .newpost-container .newpost-header{margin-top:4rem;width:90%;display:flex;align-items:center;justify-content:space-between;gap:.5rem;padding:0 1rem}.instagram-container .instagram-wrapper .newpost-container .newpost-header .title{flex:1;text-align:center;font-size:18px;font-weight:600;color:var(--instagram-primary-text)}.instagram-container .instagram-wrapper .newpost-container .newpost-header .next{font-size:16px;font-weight:600;color:var(--instagram-blue);cursor:pointer}.instagram-container .instagram-wrapper .newpost-container .newpost-header svg{font-size:24px;cursor:pointer;text-align:left;color:var(--instagram-primary-text)}.instagram-container .instagram-wrapper .newpost-container .searchbox{width:85%}.instagram-container .instagram-wrapper .newpost-container .newpost-body{height:100%;width:98%;display:flex;flex-direction:column;align-items:center;overflow-x:hidden;overflow-y:auto}.instagram-container .instagram-wrapper .newpost-container .newpost-body::-webkit-scrollbar{display:none}.instagram-container .instagram-wrapper .newpost-container .newpost-body .images{display:grid;grid-template-columns:repeat(4,1fr);gap:.2rem}.instagram-container .instagram-wrapper .newpost-container .newpost-body .images .image{width:6.2rem;height:6.2rem;position:relative;cursor:pointer;transition:all .2s ease-in-out}.instagram-container .instagram-wrapper .newpost-container .newpost-body .images .image img,.instagram-container .instagram-wrapper .newpost-container .newpost-body .images .image video{width:100%;height:100%;object-fit:cover;border-radius:2px}.instagram-container .instagram-wrapper .newpost-container .newpost-body .images .image img:hover,.instagram-container .instagram-wrapper .newpost-container .newpost-body .images .image video:hover{filter:brightness(.7)}.instagram-container .instagram-wrapper .newpost-container .newpost-body .images .image .video-duration{position:absolute;right:0;bottom:0;margin:.5rem;font-size:16px;color:#fff;font-weight:400}.instagram-container .instagram-wrapper .newpost-container .newpost-body .images .image .select{position:absolute;bottom:.4rem;left:.6rem}.instagram-container .instagram-wrapper .newpost-container .newpost-body .newpost-content{display:flex;flex-direction:column;width:90%}.instagram-container .instagram-wrapper .newpost-container .newpost-body .newpost-content .caption{flex-direction:row;align-items:flex-start;gap:.5rem;width:100%;overflow-wrap:break-word;display:flex}.instagram-container .instagram-wrapper .newpost-container .newpost-body .newpost-content .caption .text-area{display:flex;flex-direction:column}.instagram-container .instagram-wrapper .newpost-container .newpost-body .newpost-content .caption .text-area textarea{font-size:18px;color:var(--instagram-primary-text);border:none;background-color:transparent;resize:none;font-family:Roboto;width:70%;height:7rem}.instagram-container .instagram-wrapper .newpost-container .newpost-body .newpost-content .caption .text-area textarea:active,.instagram-container .instagram-wrapper .newpost-container .newpost-body .newpost-content .caption .text-area textarea:focus{outline:none}.instagram-container .instagram-wrapper .newpost-container .newpost-body .newpost-content .caption .attachments{position:relative;height:5rem;width:5rem;cursor:pointer}.instagram-container .instagram-wrapper .newpost-container .newpost-body .newpost-content .caption .attachments .attachment{height:5rem;width:5rem;position:absolute}.instagram-container .instagram-wrapper .newpost-container .newpost-body .newpost-content .caption .attachments .attachment img,.instagram-container .instagram-wrapper .newpost-container .newpost-body .newpost-content .caption .attachments .attachment video{width:100%;height:100%;border-radius:4px;object-fit:cover}.instagram-container .instagram-wrapper .newpost-container .newpost-body .newpost-content .item{display:flex;align-items:center;justify-content:space-between;width:100%;border-top:1px solid var(--instagram-border);padding:.75rem 0;cursor:pointer}.instagram-container .instagram-wrapper .newpost-container .newpost-body .newpost-content .item .label{display:flex;align-items:center;font-size:18px;font-weight:500;color:var(--instagram-primary-text)}.instagram-container .instagram-wrapper .newpost-container .newpost-body .newpost-content .item .label svg{font-size:22px;color:var(--instagram-primary-text)}.instagram-container .instagram-wrapper .newpost-container .newpost-body .newpost-content .item svg{font-size:24px;color:var(--instagram-primary-text)}.instagram-container .instagram-wrapper .viewpost-container{position:absolute;top:0;left:0;width:100%;height:100%;z-index:2;background-color:var(--instagram-primary)}.instagram-container .instagram-wrapper .viewpost-container .viewpost-header{margin-top:4rem;width:88%;display:flex;align-items:center;justify-content:space-between;gap:.5rem;padding:0 .85rem;color:var(--instagram-primary-text)}.instagram-container .instagram-wrapper .viewpost-container .viewpost-header svg{cursor:pointer;transition:all .1s ease-in-out;font-size:25px;margin-right:5rem}.instagram-container .instagram-wrapper .viewpost-container .viewpost-header div{font-size:18px;font-weight:600;text-align:center;flex:1}.instagram-container .instagram-wrapper .viewpost-container .viewpost-header span{flex:1}.instagram-container .instagram-wrapper .viewpost-container .profile-body{display:flex;flex-direction:column;margin-top:2rem;max-height:42.5rem;overflow:auto}.instagram-container .instagram-wrapper .viewpost-container .profile-body::-webkit-scrollbar{display:none}.instagram-container .instagram-wrapper .userpanel-container{width:100%;height:100%;position:absolute;top:0;left:0;z-index:10;background-color:var(--instagram-primary)}.instagram-container .instagram-wrapper .userpanel-container .userpanel-header{margin-top:4rem;width:88%;display:flex;align-items:center;justify-content:space-between;gap:.5rem;padding:0 1.5rem;color:var(--instagram-primary-text)}.instagram-container .instagram-wrapper .userpanel-container .userpanel-header i{cursor:pointer;transition:all .1s ease-in-out;font-size:22px;font-weight:600;flex:1}.instagram-container .instagram-wrapper .userpanel-container .userpanel-header div{font-size:18px;font-weight:600;text-align:center;flex:1}.instagram-container .instagram-wrapper .userpanel-container .userpanel-header span{flex:1}.instagram-container .instagram-wrapper .userpanel-container .userpanel-body{margin-top:1rem;max-height:50rem;overflow-y:auto}.instagram-container .instagram-wrapper .userpanel-container .userpanel-body::-webkit-scrollbar{display:none}.instagram-container .instagram-wrapper .userpanel-container .userpanel-body .items{display:flex;flex-direction:column;gap:.75rem;padding:0 1.5rem}.instagram-container .instagram-wrapper .userpanel-container .userpanel-body .items .item{display:flex;align-items:center;justify-content:space-between;gap:.75rem;cursor:pointer}.instagram-container .instagram-wrapper .userpanel-container .userpanel-body .items .item .user{display:flex;align-items:center;gap:.6rem;cursor:pointer}.instagram-container .instagram-wrapper .userpanel-container .userpanel-body .items .item .user .profile-picture{width:4rem;height:4rem}.instagram-container .instagram-wrapper .userpanel-container .userpanel-body .items .item .user .profile-picture img{object-fit:cover;width:100%;height:100%;border-radius:50%}.instagram-container .instagram-wrapper .userpanel-container .userpanel-body .items .item .user .user-details{display:flex;flex-direction:column;line-height:20px}.instagram-container .instagram-wrapper .userpanel-container .userpanel-body .items .item .user .user-details .username{font-size:18px;color:var(--instagram-primary-text);font-weight:600;display:flex;align-items:center;gap:.2rem}.instagram-container .instagram-wrapper .userpanel-container .userpanel-body .items .item .user .user-details .name{font-size:16px;color:var(--instagram-secondary-text);font-weight:400}.instagram-container .instagram-wrapper .userpanel-container .userpanel-body .items .item .user .user-details .following{font-size:14px;color:var(--instagram-secondary-text)}.instagram-container .instagram-wrapper .userpanel-container .userpanel-body .items .item .action .button{display:flex;align-items:center;justify-content:center;gap:.5rem;width:6.5rem;padding:.4rem 0rem;border-radius:7px;cursor:pointer;transition:all .1s ease-in-out;font-size:15px;font-weight:600}.instagram-container .instagram-wrapper .userpanel-container .userpanel-body .items .item .action .button:hover{filter:brightness(.9)}.instagram-container .instagram-wrapper .userpanel-container .userpanel-body .items .item .action .button.follow{color:#fff;background-color:#0095f6}.instagram-container .instagram-wrapper .userpanel-container .userpanel-body .items .item .action .button.following,.instagram-container .instagram-wrapper .userpanel-container .userpanel-body .items .item .action .button.edit{background-color:#dadada;color:#000}.instagram-container .instagram-wrapper .dm-list{animation:slideLeft .5s cubic-bezier(.19,1,.22,1)}.instagram-container .instagram-wrapper .dm-list .dm-list-header{margin-top:4rem;width:90%;display:flex;flex-direction:column;align-items:center;gap:1rem;padding:0 1.5rem;color:var(--instagram-primary-text)}.instagram-container .instagram-wrapper .dm-list .dm-list-header .top{width:95%;display:flex;flex-direction:row;align-items:center;justify-content:space-between;gap:.5rem}.instagram-container .instagram-wrapper .dm-list .dm-list-header i{cursor:pointer;font-size:1.5rem}.instagram-container .instagram-wrapper .dm-list .dm-list-header.new{display:flex;flex-direction:row;align-items:center}.instagram-container .instagram-wrapper .dm-list .dm-list-header.new i{flex:1}.instagram-container .instagram-wrapper .dm-list .dm-list-header.new .title{flex:1;text-align:center;font-size:16px;font-weight:500}.instagram-container .instagram-wrapper .dm-list .dm-list-header.new span{flex:1}.instagram-container .instagram-wrapper .dm-list .dm-list-body{display:flex;flex-direction:column;margin-top:.3rem;padding:1rem;overflow-y:auto;height:40rem}.instagram-container .instagram-wrapper .dm-list .dm-list-body::-webkit-scrollbar{display:none}.instagram-container .instagram-wrapper .dm-list .dm-list-body .input{display:flex;flex-direction:column;gap:.4rem;margin-bottom:1.5rem}.instagram-container .instagram-wrapper .dm-list .dm-list-body .input span{color:var(--instagram-primary-text);font-weight:600;font-size:18px}.instagram-container .instagram-wrapper .dm-list .dm-list-body .input input{width:50%;border:none;font-size:18px;font-family:inherit;color:var(--instagram-secondary-text);background-color:transparent}.instagram-container .instagram-wrapper .dm-list .dm-list-body .input input:focus{outline:none}.instagram-container .instagram-wrapper .dm-list .dm-list-body .items{display:flex;flex-direction:column;gap:.75rem}.instagram-container .instagram-wrapper .dm-list .dm-list-body .items .item{display:flex;flex-direction:row;align-items:center;gap:.8rem;cursor:pointer}.instagram-container .instagram-wrapper .dm-list .dm-list-body .items .item .avatar img{object-fit:cover;border-radius:50%;width:4rem;height:4rem}.instagram-container .instagram-wrapper .dm-list .dm-list-body .items .item .info{display:flex;flex-direction:column;gap:0rem;max-width:75%;font-size:15px;color:var(--instagram-secondary-text);font-weight:400}.instagram-container .instagram-wrapper .dm-list .dm-list-body .items .item .info .username{font-size:16px;color:var(--instagram-primary-text);font-weight:600;display:flex;align-items:center;gap:.2rem}.instagram-container .instagram-wrapper .dm-container{display:flex;flex-direction:column;align-items:center;height:89%;animation:slideLeft .5s cubic-bezier(.19,1,.22,1)}.instagram-container .instagram-wrapper .dm-container .dm-header{margin-top:4rem;width:85%;display:flex;flex-direction:row;align-items:center;gap:1.5rem;padding:0 2rem 1rem;color:var(--instagram-primary-text)}.instagram-container .instagram-wrapper .dm-container .dm-header i{cursor:pointer;font-size:1.5rem}.instagram-container .instagram-wrapper .dm-container .dm-header .profile{display:flex;align-items:center;gap:.5rem;cursor:pointer}.instagram-container .instagram-wrapper .dm-container .dm-header .profile .avatar{width:2.3rem;height:2.3rem}.instagram-container .instagram-wrapper .dm-container .dm-header .profile .avatar img{object-fit:cover;width:100%;height:100%;border-radius:50%}.instagram-container .instagram-wrapper .dm-container .dm-header .profile .username{font-weight:600;font-size:20px;display:flex;align-items:center;gap:.2rem}.instagram-container .instagram-wrapper .dm-container .dm-body-container{display:relative;display:flex;flex-direction:column-reverse;height:100%;width:95%;overflow-y:auto;overflow-x:hidden;scroll-behavior:auto}.instagram-container .instagram-wrapper .dm-container .dm-body-container::-webkit-scrollbar{display:none}.instagram-container .instagram-wrapper .dm-container .dm-body{display:flex;flex-direction:column;padding:.5rem 1rem;gap:.5rem}.instagram-container .instagram-wrapper .dm-container .dm-body .message{font-size:16px;font-weight:400;width:auto;max-width:85%;color:var(--instagram-primary-text);display:flex;flex-direction:column;gap:.2rem}.instagram-container .instagram-wrapper .dm-container .dm-body .message .attatchments img,.instagram-container .instagram-wrapper .dm-container .dm-body .message .attatchments video{width:100%;border-radius:15px}.instagram-container .instagram-wrapper .dm-container .dm-body .message .profile-picture.hide{visibility:hidden}.instagram-container .instagram-wrapper .dm-container .dm-body .message .profile-picture img{width:2.2rem;height:2.2rem;border-radius:50%;object-fit:cover;object-position:center}.instagram-container .instagram-wrapper .dm-container .dm-body .message .date{padding:0 .5rem;font-size:13px;font-weight:300;color:var(--instagram-secondary-text)}.instagram-container .instagram-wrapper .dm-container .dm-body .message .story-reply{display:flex;flex-direction:column;gap:.5rem}.instagram-container .instagram-wrapper .dm-container .dm-body .message .story-reply .title{font-size:14px;font-weight:400;color:var(--instagram-secondary-text)}.instagram-container .instagram-wrapper .dm-container .dm-body .message .story-reply img,.instagram-container .instagram-wrapper .dm-container .dm-body .message .story-reply video{width:7.5rem;border-radius:15px;object-fit:cover;object-position:center}.instagram-container .instagram-wrapper .dm-container .dm-body .message .story-reply .content{font-size:16px;font-weight:400;color:var(--instagram-primary-text);word-break:break-word;width:max-content;max-width:85%}.instagram-container .instagram-wrapper .dm-container .dm-body .message.self{align-items:flex-end;margin-left:auto}.instagram-container .instagram-wrapper .dm-container .dm-body .message.self .story-reply{align-items:flex-end;margin-right:.5rem}.instagram-container .instagram-wrapper .dm-container .dm-body .message.self .date{text-align:right;margin-left:auto}.instagram-container .instagram-wrapper .dm-container .dm-body .message.self .content{padding:.5rem 1.2rem;border-radius:25px;text-align:left;background-color:var(--instagram-highlight);border:1px solid var(--instagram-border);word-break:break-word}.instagram-container .instagram-wrapper .dm-container .dm-body .message.other{margin-right:auto}.instagram-container .instagram-wrapper .dm-container .dm-body .message.other .story-reply{border-left:1px solid var(--instagram-border);padding-left:.75rem;margin-left:-.25rem}.instagram-container .instagram-wrapper .dm-container .dm-body .message.other .message-with-pfp{display:flex;flex-direction:row;align-items:flex-end;gap:.8rem}.instagram-container .instagram-wrapper .dm-container .dm-body .message.other .message-content{display:flex;flex-direction:column;align-items:flex-start}.instagram-container .instagram-wrapper .dm-container .dm-body .message.other .message-content .content{display:flex;align-items:center;justify-self:center;padding:.5rem 1.1rem;border-radius:25px;background-color:transparent;border:1px solid var(--instagram-border);word-break:break-word}.instagram-container .instagram-wrapper .dm-container .dm-body .message.other .message-content .content:not(img)+.other{padding:0}.instagram-container .instagram-wrapper .dm-container .dm-body .message.other .message-content .date{margin-left:3rem;margin-right:auto}.instagram-container .instagram-wrapper .dm-container .attachments{position:absolute;display:flex;flex-direction:row;align-items:center;gap:.5rem;bottom:10rem;left:2rem}.instagram-container .instagram-wrapper .dm-container .attachments .attachment{position:relative;height:5rem;width:5rem}.instagram-container .instagram-wrapper .dm-container .attachments .attachment img,.instagram-container .instagram-wrapper .dm-container .attachments .attachment video{width:100%;height:100%;object-fit:cover;border-radius:10px}.instagram-container .instagram-wrapper .dm-container .attachments .attachment svg{position:absolute;top:.25rem;right:.25rem;height:1.3rem;width:1.3rem;padding:.1rem;border-radius:50%;display:flex;align-items:center;justify-content:center;background-color:#000c;color:#fff;cursor:pointer;font-size:13px}.instagram-container .instagram-wrapper .dm-container .dm-footer{display:flex;flex-direction:row;align-items:center;width:88%;height:2.5rem;border:2px solid var(--instagram-border);margin:.5rem;border-radius:30px}.instagram-container .instagram-wrapper .dm-container .dm-footer input{width:100%;height:2.5rem;padding:.2rem 1rem;font-size:16px;background-color:transparent;border:none;color:var(--instagram-primary-text)}.instagram-container .instagram-wrapper .dm-container .dm-footer input:focus{outline:none}.instagram-container .instagram-wrapper .dm-container .dm-footer svg{font-size:35px;margin-right:1rem;cursor:pointer;color:var(--instagram-primary-text)}.instagram-container .instagram-wrapper .dm-container .dm-footer span{font-size:15px;font-weight:500;color:var(--instagram-blue);margin-right:1rem;cursor:pointer}.instagram-container .instagram-wrapper .verified{display:flex;align-items:center;justify-content:center}.instagram-container .instagram-wrapper .verified img{width:1.15rem;height:1.15rem}.instagram-container .profile-picture{position:relative}.instagram-container .profile-picture .avatar{width:100%;height:100%;border-radius:50%;object-fit:cover;object-position:center}
