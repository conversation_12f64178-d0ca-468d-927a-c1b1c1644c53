import{u as N,j as n,m as b,S as w,a as e,L as l,F as T,y as F,a5 as k,f as C,o as B,r as p,v as W,b as D,P as M,I as x,d as I,C as O,s as A,q as v,b4 as j,aJ as H,aa as X,t as P,K as G,G as z,a0 as _,O as Y,J as R,A as J}from"./index-a04bc7c5.js";function K(){const d=N(D),o=N(M.Settings),c=N(S),m=({amount:a})=>{const[r,i]=p.useState(!0);let t;return r&&(o!=null&&o.streamerMode)?t="•".repeat(W(a,2).toString().length):t=W(a,2).toLocaleString(),t.length>15&&(t=t.slice(0,15)+"..."),e("div",{className:"value",onClick:()=>i(!r),children:d.CurrencyFormat.replace("%s",t)})};return n(b.div,{...w("left","home",.2),className:"overview",children:[e("div",{className:"wallet-header",children:e("div",{className:"title",children:l("APPS.WALLET.TITLE")})}),n("div",{className:"wrapper",children:[e("div",{className:"card"}),e("section",{children:n("div",{className:"item space",children:[n("div",{className:"balance",children:[e("div",{className:"title",children:l("APPS.WALLET.BALANCE")}),e(m,{amount:c.balance})]}),e("div",{className:"button",onClick:()=>E.set("newpayment"),children:l("APPS.WALLET.SEND")})]})}),c.recentTransactions&&c.recentTransactions.length>0&&n(T,{children:[n("div",{className:"subtitle",children:[e("div",{className:"title",children:l("APPS.WALLET.LATEST_TRANSACTIONS")}),n("div",{className:"link",onClick:()=>E.set("transactions"),children:[l("APPS.WALLET.ALL_TRANSACTIONS")," ",e(F,{})]})]}),e("section",{children:c.recentTransactions.slice(0,5).map((a,r)=>{let i=a.amount>0,t=a.company.match(/\d{10}/);return n("div",{className:"item multi",children:[a.logo?e(k,{src:a.logo}):!t&&e("div",{className:"image",children:a.company[0]}),n("div",{className:"info",children:[n("div",{className:"transaction-details",children:[e("div",{className:"title",children:t?C(a.company):a.company}),e("div",{className:"date",children:B(a.timestamp)})]}),n("div",{className:`amount ${i?"green":"red"}`,children:[!i&&"-",d.CurrencyFormat.replace("%s",Math.abs(a.amount).toLocaleString())]})]})]},r)})})]}),c.bills&&c.bills.length>0&&n(T,{children:[n("div",{className:"subtitle",children:[e("div",{className:"title",children:l("APPS.WALLET.LATEST_BILLS")}),n("div",{className:"link",children:["All Bills ",e(F,{})]})]}),e("section",{})]})]})]})}function V(){N(M.Settings);const d=N(D),o=N(S),[c,m]=p.useState(""),[a,r]=p.useState(0),[i,t]=p.useState(""),[h,u]=p.useState(!1),L=s=>(A("info",`Can afford? ${o.balance>=s} Balance: ${o.balance}, amount: ${s}`),o.balance>=s);p.useEffect(()=>{a===0?u(i.length>3):a===1&&u(i.length>0&&L(parseFloat(i))&&parseFloat(i)<=(d.MaxTransferAmount??Number.MAX_SAFE_INTEGER))},[i]);const U=s=>{if(s=="backspace")return t(f=>f.length>0?f.slice(0,-1):"0");t(f=>f+s)};p.useEffect(()=>{if(a!==1||i.length>=9)return;const s=document.getElementById("number");s.style.width=`${i.length}ch`},[i]);const g=s=>{O.PopUp.set({title:l(`APPS.WALLET.${s}.TITLE`),description:l(`APPS.WALLET.${s}.DESCRIPTION`),buttons:[{title:l("APPS.WALLET.OK")}]})},$=()=>{if(h){if(a==0)v("Wallet",{action:"doesNumberExist",number:i},!0).then(s=>{if(!s)return g("NUMBER_DOES_NOT_EXIST");m(i),r(f=>f+1),t("")});else if(a==1){if(!L(parseFloat(i)))return g("INSUFFICIENT_FUNDS");if(parseFloat(i)>(d.MaxTransferAmount??Number.MAX_SAFE_INTEGER))return g("Max transfer amount reached");if(!X())return g("AIRPLANE_MODE");v("Wallet",{action:"sendPayment",number:c,amount:Math.floor(parseFloat(i))},{success:!0}).then(s=>{s!=null&&s.success?r(f=>f+1):(A("warning","Could not send payment",s.reason),g(s.reason))})}}};return n(b.div,{...w("right","sendpayment",.25),className:"send-container",children:[n("div",{className:"send-header",children:[e("div",{onClick:()=>E.set("home"),children:a!==2&&n(T,{children:[e(x,{}),l("APPS.WALLET.HOME")]})}),a===2?e("div",{onClick:()=>E.set("home"),children:l("APPS.WALLET.DONE")}):e("div",{className:!h&&"disabled",onClick:$,children:l("APPS.WALLET.NEXT")})]}),n(b.div,{...w(a!==0?"right":null,a.toString(),.25),className:"send-wrapper",children:[a===0&&n(T,{children:[e("div",{className:"user",children:e(I,{className:"number_input",type:"number",onChange:s=>t(s.target.value.replace(/[^0-9]/g,"")),value:i,placeholder:C("1234567890")})}),e("div",{className:"button",onClick:()=>{O.ContactSelector.set({onSelect:s=>{if(!(s!=null&&s.number))return A("warning","No contact found");v("Wallet",{action:"doesNumberExist",number:s.number}).then(f=>{f?(m(s.number),r(1),t("")):setTimeout(()=>g("NUMBER_DOES_NOT_EXIST"),250)})}})},children:l("APPS.WALLET.SELECT_CONTACT")}),e("div",{className:"description",children:l("APPS.WALLET.NO_REFUNDS")})]}),a===1&&n(T,{children:[n("div",{className:"amount",children:[e("div",{className:"symbol",children:d.CurrencyFormat.replace("%s","")}),e(I,{type:"number",id:"number",value:i,onChange:s=>t(s.target.value.replace(/[^0-9]/g,""))})]}),n("div",{className:"description",children:[l("APPS.WALLET.NEW_BALANCE"),": ",d.CurrencyFormat.replace("%s",""),isNaN(W(o.balance-parseFloat(i),2))?o.balance:W(o.balance-parseFloat(i),2)]})]}),a===2&&n("div",{className:"success",children:[e(j,{}),e("div",{className:"title",children:l("APPS.WALLET.DONE")}),e("div",{className:"subtitle",children:l("APPS.WALLET.DONE_SUBTITLE").format({amount:d.CurrencyFormat.replace("%s",i),number:C(c)})})]})]}),a!==2&&e("div",{className:"numpad-wrapper",children:e(H,{cb:s=>U(s)})})]})}const y={balance:10230,transactions:[{amount:-99,company:"LB",logo:"https://docs.lbscripts.com/images/icons/icon.png",timestamp:Date.now()-1e3*60*60*2},{amount:-24.45,company:"Burgershot",logo:"https://ih0.redbubble.net/image.5360028329.7056/raf,360x360,075,t,fafafa:ca443f4786.jpg",timestamp:Date.now()-1e3*60*60*6},{amount:10,company:"Breze",timestamp:Date.now()-1e3*60*60*12},{amount:-22,company:"Breze",timestamp:Date.now()-1e3*60*60*24},{amount:-99,company:"Breze",timestamp:Date.now()-1e3*60*60*24*2},{amount:3400,company:"Salary",timestamp:Date.now()-1e3*60*60*24*4},{amount:-700,company:"Invoice #1234",timestamp:Date.now()-1e3*60*60*24*4}],bills:[]};function q(){const d=N(D),o=N(P.APPS.WALLET.transactions),[c,m]=p.useState({});p.useEffect(()=>{if(o.length>10)return A("info","Cache exists and is a lot of shit, nto fetching getTransations");v("Wallet",{action:"getTransactions"},y.transactions).then(r=>{if(!r)return A("warning","Failed to fetch transactions");P.APPS.WALLET.transactions.set(r)})},[]);const{handleScroll:a}=G({fetchData:r=>v("Wallet",{action:"getTransactions",page:r}),onDataFetched:r=>P.APPS.WALLET.transactions.set(r),perPage:25});return p.useEffect(()=>{if(!o)return;const r=o.reduce((i,t)=>{let h=new Date(t.timestamp).toDateString(),u=h.split(" ").slice(1,3).join(" ");return h===new Date().toDateString()?u=l("APPS.WALLET.TODAY"):h===new Date(new Date().setDate(new Date().getDate()-1)).toDateString()&&(u=l("APPS.WALLET.YESTERDAY")),i[u]||(i[u]=[]),i[u].push(t),i},{});m(r)},[o]),n(b.div,{className:"overview",...w("right","transactions",.2),children:[n("div",{className:"wallet-header",children:[n("div",{className:"navigation",onClick:()=>E.set("home"),children:[e(x,{}),l("APPS.WALLET.HOME")]}),e("div",{className:"title",children:l("APPS.WALLET.TRANSACTIONS")})]}),e("div",{className:"wrapper",onScroll:a,children:Object.keys(c).map((r,i)=>n(p.Fragment,{children:[n("div",{className:"subtitle",children:[e("div",{className:"title",children:r}),e("div",{className:"value",children:n(T,{children:[(()=>{const t=c[r].reduce((h,u)=>h+u.amount,0);return t===0?"":t>0?"+":"-"})(),d.CurrencyFormat.replace("%s",Math.abs(c[r].reduce((t,h)=>t+h.amount,0)).toLocaleString())]})})]}),e("section",{children:c[r].map((t,h)=>{let u=t.amount>=0,L=t.company.match(/\d{10}/);return n("div",{className:"item multi",children:[t.logo?e(k,{src:t.logo}):!L&&e("div",{className:"image",children:t.company[0]}),n("div",{className:"info",children:[n("div",{className:"transaction-details",children:[e("div",{className:"title",children:L?C(t.company):t.company}),e("div",{className:"date",children:B(t.timestamp)})]}),n("div",{className:`amount ${u?"green":"red"}`,children:[!u&&"-",d.CurrencyFormat.replace("%s",Math.abs(t.amount).toLocaleString())]})]})]},h)})})]},i))})]})}const E=R("home"),S=R({balance:0,recentTransactions:[],bills:[]}),Q={home:e(K,{}),transactions:e(q,{}),newpayment:e(V,{})};function ee(){const d=N(S),o=N(E),c=N(J);return p.useEffect(()=>{var m;z("Wallet")&&(v("Wallet",{action:"getBalance"},y.balance).then(a=>{if(typeof a!="number")return A("warning","Failed to fetch balance, got:",a);S.patch({balance:a})}),((m=P.APPS.WALLET.transactions)==null?void 0:m.value.length)===0&&v("Wallet",{action:"getTransactions",recent:!0},y.transactions.slice(0,5)).then(a=>{if(!a)return A("warning","Failed to fetch transactions");S.patch({recentTransactions:a})}))},[c==null?void 0:c.active]),_("wallet:addTransaction",m=>{A("info","Adding transaction",m);let a={...m,timestamp:new Date().getTime()};P.APPS.WALLET.transactions.set([a,...d.recentTransactions]),S.patch({recentTransactions:[a,...d.recentTransactions].slice(0,5)})},{waitUntilService:!0}),_("wallet:setBalance",m=>{if(typeof m!="number")return A("warning","Failed to set balance, received:",m);A("info","Setting balance",m),S.patch({balance:m})},{waitUntilService:!0}),e("div",{className:"wallet-app-container",children:e(Y,{children:Q[o]})})}export{E as View,S as WalletInfo,ee as default};
