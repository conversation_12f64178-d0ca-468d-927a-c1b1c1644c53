if not Config.<PERSON>Checker?.Enabled then
    return
end

local defaultTables = {
	phone_tiktok_accounts = {
		{
			column = "twitter",
			type = "VARCHAR",
			allowNull = true,
			isKey = false,
			default = "NULL",
			collation = "utf8mb4_unicode_ci",
			length = 20,
		},
		{
			column = "like_count",
			type = "INT",
			allowNull = false,
			isKey = false,
			default = "0",
		},
		{
			column = "show_likes",
			type = "TINYINT",
			allowNull = true,
			isKey = false,
			default = "1",
		},
		{
			column = "follower_count",
			type = "INT",
			allowNull = false,
			isKey = false,
			default = "0",
		},
		{
			column = "following_count",
			type = "INT",
			allowNull = false,
			isKey = false,
			default = "0",
		},
		{
			column = "phone_number",
			type = "VARCHAR",
			allowNull = false,
			isKey = true,
			collation = "utf8mb4_unicode_ci",
			length = 15,
		},
		{
			column = "date_joined",
			type = "TIMESTAMP",
			allowNull = false,
			isKey = false,
			default = "current_timestamp()",
		},
		{
			column = "instagram",
			type = "VARCHAR",
			allowNull = true,
			isKey = false,
			default = "NULL",
			collation = "utf8mb4_unicode_ci",
			length = 20,
		},
		{
			column = "video_count",
			type = "INT",
			allowNull = false,
			isKey = false,
			default = "0",
		},
		{
			column = "name",
			type = "VARCHAR",
			allowNull = false,
			isKey = false,
			collation = "utf8mb4_unicode_ci",
			length = 30,
		},
		{
			column = "verified",
			type = "TINYINT",
			allowNull = true,
			isKey = false,
			default = "0",
		},
		{
			column = "bio",
			type = "VARCHAR",
			allowNull = true,
			isKey = false,
			default = "NULL",
			collation = "utf8mb4_unicode_ci",
			length = 100,
		},
		{
			column = "avatar",
			type = "VARCHAR",
			allowNull = true,
			isKey = false,
			default = "NULL",
			collation = "utf8mb4_unicode_ci",
			length = 500,
		},
		{
			column = "password",
			type = "VARCHAR",
			allowNull = false,
			isKey = false,
			collation = "utf8mb4_unicode_ci",
			length = 100,
		},
		{
			column = "username",
			type = "VARCHAR",
			allowNull = false,
			isKey = true,
			collation = "utf8mb4_unicode_ci",
			length = 20,
		},
	},
	phone_twitter_follows = {
		{
			column = "follower",
			type = "VARCHAR",
			allowNull = false,
			isKey = true,
			collation = "utf8mb4_unicode_ci",
			length = 20,
		},
		{
			column = "followed",
			type = "VARCHAR",
			allowNull = false,
			isKey = true,
			collation = "utf8mb4_unicode_ci",
			length = 20,
		},
		{
			column = "notifications",
			type = "TINYINT",
			allowNull = false,
			isKey = false,
			default = "0",
		},
	},
	phone_tiktok_videos = {
		{
			column = "comments",
			type = "INT",
			allowNull = false,
			isKey = false,
			default = "0",
		},
		{
			column = "src",
			type = "VARCHAR",
			allowNull = false,
			isKey = false,
			collation = "utf8mb4_unicode_ci",
			length = 500,
		},
		{
			column = "pinned_comment",
			type = "VARCHAR",
			allowNull = true,
			isKey = false,
			default = "NULL",
			collation = "utf8mb4_unicode_ci",
			length = 10,
		},
		{
			column = "id",
			type = "VARCHAR",
			allowNull = false,
			isKey = true,
			collation = "utf8mb4_unicode_ci",
			length = 10,
		},
		{
			column = "timestamp",
			type = "TIMESTAMP",
			allowNull = false,
			isKey = false,
			default = "current_timestamp()",
		},
		{
			column = "saves",
			type = "INT",
			allowNull = false,
			isKey = false,
			default = "0",
		},
		{
			column = "views",
			type = "INT",
			allowNull = false,
			isKey = false,
			default = "0",
		},
		{
			column = "username",
			type = "VARCHAR",
			allowNull = false,
			isKey = true,
			collation = "utf8mb4_unicode_ci",
			length = 20,
		},
		{
			column = "likes",
			type = "INT",
			allowNull = false,
			isKey = false,
			default = "0",
		},
		{
			column = "caption",
			type = "VARCHAR",
			allowNull = true,
			isKey = false,
			default = "NULL",
			collation = "utf8mb4_unicode_ci",
			length = 100,
		},
		{
			column = "music",
			type = "TEXT",
			allowNull = true,
			isKey = false,
			default = "NULL",
			collation = "utf8mb4_unicode_ci",
		},
		{
			column = "metadata",
			type = "LONGTEXT",
			allowNull = true,
			isKey = false,
			default = "NULL",
			collation = "utf8mb4_unicode_ci",
		},
	},
	phone_photo_album_photos = {
		{
			column = "photo_id",
			type = "INT",
			allowNull = false,
			isKey = true,
		},
		{
			column = "album_id",
			type = "INT",
			allowNull = false,
			isKey = true,
		},
	},
	phone_instagram_stories = {
		{
			column = "image",
			type = "VARCHAR",
			allowNull = false,
			isKey = false,
			collation = "utf8mb4_unicode_ci",
			length = 500,
		},
		{
			column = "timestamp",
			type = "TIMESTAMP",
			allowNull = false,
			isKey = false,
			default = "current_timestamp()",
		},
		{
			column = "username",
			type = "VARCHAR",
			allowNull = false,
			isKey = true,
			collation = "utf8mb4_unicode_ci",
			length = 20,
		},
		{
			column = "id",
			type = "VARCHAR",
			allowNull = false,
			isKey = true,
			collation = "utf8mb4_unicode_ci",
			length = 10,
		},
		{
			column = "metadata",
			type = "LONGTEXT",
			allowNull = true,
			isKey = false,
			default = "NULL",
			collation = "utf8mb4_unicode_ci",
		},
	},
	phone_notifications = {
		{
			column = "content",
			type = "VARCHAR",
			allowNull = true,
			isKey = false,
			default = "NULL",
			collation = "utf8mb4_unicode_ci",
			length = 500,
		},
		{
			column = "title",
			type = "VARCHAR",
			allowNull = true,
			isKey = false,
			default = "NULL",
			collation = "utf8mb4_unicode_ci",
			length = 50,
		},
		{
			column = "show_avatar",
			type = "TINYINT",
			allowNull = true,
			isKey = false,
			default = "0",
		},
		{
			column = "id",
			type = "INT",
			allowNull = false,
			isKey = true,
		},
		{
			column = "phone_number",
			type = "VARCHAR",
			allowNull = false,
			isKey = true,
			collation = "utf8mb4_unicode_ci",
			length = 15,
		},
		{
			column = "custom_data",
			type = "TEXT",
			allowNull = true,
			isKey = false,
			default = "NULL",
			collation = "utf8mb4_unicode_ci",
		},
		{
			column = "timestamp",
			type = "TIMESTAMP",
			allowNull = false,
			isKey = false,
			default = "current_timestamp()",
		},
		{
			column = "avatar",
			type = "VARCHAR",
			allowNull = true,
			isKey = false,
			default = "NULL",
			collation = "utf8mb4_unicode_ci",
			length = 500,
		},
		{
			column = "app",
			type = "VARCHAR",
			allowNull = false,
			isKey = false,
			collation = "utf8mb4_unicode_ci",
			length = 50,
		},
		{
			column = "thumbnail",
			type = "VARCHAR",
			allowNull = true,
			isKey = false,
			default = "NULL",
			collation = "utf8mb4_unicode_ci",
			length = 500,
		},
	},
	phone_music_playlists = {
		{
			column = "cover",
			type = "VARCHAR",
			allowNull = true,
			isKey = false,
			default = "NULL",
			collation = "utf8mb4_unicode_ci",
			length = 500,
		},
		{
			column = "name",
			type = "VARCHAR",
			allowNull = false,
			isKey = false,
			collation = "utf8mb4_unicode_ci",
			length = 50,
		},
		{
			column = "phone_number",
			type = "VARCHAR",
			allowNull = false,
			isKey = true,
			collation = "utf8mb4_unicode_ci",
			length = 15,
		},
		{
			column = "id",
			type = "INT",
			allowNull = false,
			isKey = true,
		},
	},
	phone_tiktok_unread_messages = {
		{
			column = "amount",
			type = "INT",
			allowNull = false,
			isKey = false,
			default = "0",
		},
		{
			column = "username",
			type = "VARCHAR",
			allowNull = false,
			isKey = true,
			collation = "utf8mb4_unicode_ci",
			length = 20,
		},
		{
			column = "channel_id",
			type = "VARCHAR",
			allowNull = false,
			isKey = true,
			collation = "utf8mb4_unicode_ci",
			length = 10,
		},
	},
	phone_crypto = {
		{
			column = "invested",
			type = "INT",
			allowNull = false,
			isKey = false,
			default = "0",
		},
		{
			column = "amount",
			type = "DOUBLE",
			allowNull = false,
			isKey = false,
			default = "0",
		},
		{
			column = "coin",
			type = "VARCHAR",
			allowNull = false,
			isKey = true,
			collation = "utf8mb4_unicode_ci",
			length = 15,
		},
		{
			column = "id",
			type = "VARCHAR",
			allowNull = false,
			isKey = true,
			collation = "utf8mb4_unicode_ci",
			length = 100,
		},
	},
	phone_darkchat_messages = {
		{
			column = "content",
			type = "VARCHAR",
			allowNull = true,
			isKey = false,
			default = "NULL",
			collation = "utf8mb4_unicode_ci",
			length = 1000,
		},
		{
			column = "channel",
			type = "VARCHAR",
			allowNull = false,
			isKey = true,
			collation = "utf8mb4_unicode_ci",
			length = 50,
		},
		{
			column = "sender",
			type = "VARCHAR",
			allowNull = false,
			isKey = true,
			collation = "utf8mb4_unicode_ci",
			length = 20,
		},
		{
			column = "timestamp",
			type = "TIMESTAMP",
			allowNull = false,
			isKey = false,
			default = "current_timestamp()",
		},
		{
			column = "id",
			type = "INT",
			allowNull = false,
			isKey = true,
		},
	},
	phone_twitter_retweets = {
		{
			column = "timestamp",
			type = "TIMESTAMP",
			allowNull = false,
			isKey = false,
			default = "current_timestamp()",
		},
		{
			column = "tweet_id",
			type = "VARCHAR",
			allowNull = false,
			isKey = true,
			collation = "utf8mb4_unicode_ci",
			length = 50,
		},
		{
			column = "username",
			type = "VARCHAR",
			allowNull = false,
			isKey = true,
			collation = "utf8mb4_unicode_ci",
			length = 20,
		},
	},
	phone_message_channels = {
		{
			column = "name",
			type = "VARCHAR",
			allowNull = true,
			isKey = false,
			default = "NULL",
			collation = "utf8mb4_unicode_ci",
			length = 50,
		},
		{
			column = "is_group",
			type = "TINYINT",
			allowNull = false,
			isKey = false,
			default = "0",
		},
		{
			column = "last_message",
			type = "VARCHAR",
			allowNull = false,
			isKey = false,
			default = "''",
			collation = "utf8mb4_unicode_ci",
			length = 50,
		},
		{
			column = "last_message_timestamp",
			type = "TIMESTAMP",
			allowNull = false,
			isKey = false,
			default = "current_timestamp()",
		},
		{
			column = "id",
			type = "INT",
			allowNull = false,
			isKey = true,
		},
	},
	phone_instagram_accounts = {
		{
			column = "profile_image",
			type = "VARCHAR",
			allowNull = true,
			isKey = false,
			default = "NULL",
			collation = "utf8mb4_unicode_ci",
			length = 500,
		},
		{
			column = "follower_count",
			type = "INT",
			allowNull = false,
			isKey = false,
			default = "0",
		},
		{
			column = "following_count",
			type = "INT",
			allowNull = false,
			isKey = false,
			default = "0",
		},
		{
			column = "date_joined",
			type = "TIMESTAMP",
			allowNull = false,
			isKey = false,
			default = "current_timestamp()",
		},
		{
			column = "private",
			type = "TINYINT",
			allowNull = true,
			isKey = false,
			default = "0",
		},
		{
			column = "verified",
			type = "TINYINT",
			allowNull = true,
			isKey = false,
			default = "0",
		},
		{
			column = "display_name",
			type = "VARCHAR",
			allowNull = false,
			isKey = false,
			collation = "utf8mb4_unicode_ci",
			length = 30,
		},
		{
			column = "story_count",
			type = "INT",
			allowNull = false,
			isKey = false,
			default = "0",
		},
		{
			column = "phone_number",
			type = "VARCHAR",
			allowNull = false,
			isKey = true,
			collation = "utf8mb4_unicode_ci",
			length = 15,
		},
		{
			column = "password",
			type = "VARCHAR",
			allowNull = false,
			isKey = false,
			collation = "utf8mb4_unicode_ci",
			length = 100,
		},
		{
			column = "username",
			type = "VARCHAR",
			allowNull = false,
			isKey = true,
			collation = "utf8mb4_unicode_ci",
			length = 20,
		},
		{
			column = "bio",
			type = "VARCHAR",
			allowNull = true,
			isKey = false,
			default = "NULL",
			collation = "utf8mb4_unicode_ci",
			length = 100,
		},
		{
			column = "post_count",
			type = "INT",
			allowNull = false,
			isKey = false,
			default = "0",
		},
	},
	phone_phone_calls = {
		{
			column = "answered",
			type = "TINYINT",
			allowNull = true,
			isKey = false,
			default = "0",
		},
		{
			column = "hide_caller_id",
			type = "TINYINT",
			allowNull = true,
			isKey = false,
			default = "0",
		},
		{
			column = "duration",
			type = "INT",
			allowNull = false,
			isKey = false,
			default = "0",
		},
		{
			column = "timestamp",
			type = "TIMESTAMP",
			allowNull = false,
			isKey = false,
			default = "current_timestamp()",
		},
		{
			column = "caller",
			type = "VARCHAR",
			allowNull = false,
			isKey = false,
			collation = "utf8mb4_unicode_ci",
			length = 15,
		},
		{
			column = "callee",
			type = "VARCHAR",
			allowNull = false,
			isKey = false,
			collation = "utf8mb4_unicode_ci",
			length = 15,
		},
		{
			column = "id",
			type = "INT",
			allowNull = false,
			isKey = true,
		},
	},
	phone_tiktok_notifications = {
		{
			column = "type",
			type = "VARCHAR",
			allowNull = false,
			isKey = false,
			collation = "utf8mb4_unicode_ci",
			length = 20,
		},
		{
			column = "comment_id",
			type = "VARCHAR",
			allowNull = true,
			isKey = true,
			default = "NULL",
			collation = "utf8mb4_unicode_ci",
			length = 10,
		},
		{
			column = "from",
			type = "VARCHAR",
			allowNull = false,
			isKey = true,
			collation = "utf8mb4_unicode_ci",
			length = 20,
		},
		{
			column = "video_id",
			type = "VARCHAR",
			allowNull = true,
			isKey = true,
			default = "NULL",
			collation = "utf8mb4_unicode_ci",
			length = 10,
		},
		{
			column = "timestamp",
			type = "TIMESTAMP",
			allowNull = false,
			isKey = false,
			default = "current_timestamp()",
		},
		{
			column = "username",
			type = "VARCHAR",
			allowNull = false,
			isKey = true,
			collation = "utf8mb4_unicode_ci",
			length = 20,
		},
		{
			column = "id",
			type = "INT",
			allowNull = false,
			isKey = true,
		},
	},
	phone_tiktok_comments = {
		{
			column = "reply_to",
			type = "VARCHAR",
			allowNull = true,
			isKey = true,
			default = "NULL",
			collation = "utf8mb4_unicode_ci",
			length = 10,
		},
		{
			column = "timestamp",
			type = "TIMESTAMP",
			allowNull = false,
			isKey = false,
			default = "current_timestamp()",
		},
		{
			column = "replies",
			type = "INT",
			allowNull = false,
			isKey = false,
			default = "0",
		},
		{
			column = "likes",
			type = "INT",
			allowNull = false,
			isKey = false,
			default = "0",
		},
		{
			column = "video_id",
			type = "VARCHAR",
			allowNull = false,
			isKey = true,
			collation = "utf8mb4_unicode_ci",
			length = 10,
		},
		{
			column = "comment",
			type = "VARCHAR",
			allowNull = false,
			isKey = false,
			collation = "utf8mb4_unicode_ci",
			length = 550,
		},
		{
			column = "id",
			type = "VARCHAR",
			allowNull = false,
			isKey = true,
			collation = "utf8mb4_unicode_ci",
			length = 10,
		},
		{
			column = "username",
			type = "VARCHAR",
			allowNull = false,
			isKey = true,
			collation = "utf8mb4_unicode_ci",
			length = 20,
		},
	},
	phone_clock_alarms = {
		{
			column = "minutes",
			type = "INT",
			allowNull = false,
			isKey = false,
			default = "0",
		},
		{
			column = "phone_number",
			type = "VARCHAR",
			allowNull = false,
			isKey = true,
			collation = "utf8mb4_unicode_ci",
			length = 15,
		},
		{
			column = "hours",
			type = "INT",
			allowNull = false,
			isKey = false,
			default = "0",
		},
		{
			column = "label",
			type = "VARCHAR",
			allowNull = true,
			isKey = false,
			default = "NULL",
			collation = "utf8mb4_unicode_ci",
			length = 50,
		},
		{
			column = "enabled",
			type = "TINYINT",
			allowNull = true,
			isKey = false,
			default = "1",
		},
		{
			column = "id",
			type = "INT",
			allowNull = false,
			isKey = true,
		},
	},
	phone_wallet_transactions = {
		{
			column = "company",
			type = "VARCHAR",
			allowNull = false,
			isKey = false,
			collation = "utf8mb4_unicode_ci",
			length = 50,
		},
		{
			column = "phone_number",
			type = "VARCHAR",
			allowNull = false,
			isKey = true,
			collation = "utf8mb4_unicode_ci",
			length = 15,
		},
		{
			column = "timestamp",
			type = "TIMESTAMP",
			allowNull = false,
			isKey = false,
			default = "current_timestamp()",
		},
		{
			column = "amount",
			type = "INT",
			allowNull = false,
			isKey = false,
		},
		{
			column = "logo",
			type = "VARCHAR",
			allowNull = true,
			isKey = false,
			default = "NULL",
			collation = "utf8mb4_unicode_ci",
			length = 200,
		},
		{
			column = "id",
			type = "INT",
			allowNull = false,
			isKey = true,
		},
	},
	phone_instagram_notifications = {
		{
			column = "type",
			type = "VARCHAR",
			allowNull = false,
			isKey = false,
			collation = "utf8mb4_unicode_ci",
			length = 20,
		},
		{
			column = "from",
			type = "VARCHAR",
			allowNull = false,
			isKey = true,
			collation = "utf8mb4_unicode_ci",
			length = 20,
		},
		{
			column = "timestamp",
			type = "TIMESTAMP",
			allowNull = false,
			isKey = false,
			default = "current_timestamp()",
		},
		{
			column = "post_id",
			type = "VARCHAR",
			allowNull = true,
			isKey = false,
			default = "NULL",
			collation = "utf8mb4_unicode_ci",
			length = 50,
		},
		{
			column = "username",
			type = "VARCHAR",
			allowNull = false,
			isKey = true,
			collation = "utf8mb4_unicode_ci",
			length = 20,
		},
		{
			column = "id",
			type = "VARCHAR",
			allowNull = false,
			isKey = true,
			collation = "utf8mb4_unicode_ci",
			length = 10,
		},
	},
	phone_instagram_posts = {
		{
			column = "comment_count",
			type = "INT",
			allowNull = false,
			isKey = false,
			default = "0",
		},
		{
			column = "location",
			type = "VARCHAR",
			allowNull = true,
			isKey = false,
			default = "NULL",
			collation = "utf8mb4_unicode_ci",
			length = 50,
		},
		{
			column = "media",
			type = "TEXT",
			allowNull = true,
			isKey = false,
			default = "NULL",
			collation = "utf8mb4_unicode_ci",
		},
		{
			column = "username",
			type = "VARCHAR",
			allowNull = false,
			isKey = true,
			collation = "utf8mb4_unicode_ci",
			length = 20,
		},
		{
			column = "timestamp",
			type = "TIMESTAMP",
			allowNull = false,
			isKey = false,
			default = "current_timestamp()",
		},
		{
			column = "like_count",
			type = "INT",
			allowNull = false,
			isKey = false,
			default = "0",
		},
		{
			column = "caption",
			type = "VARCHAR",
			allowNull = false,
			isKey = false,
			default = "''",
			collation = "utf8mb4_unicode_ci",
			length = 500,
		},
		{
			column = "id",
			type = "VARCHAR",
			allowNull = false,
			isKey = true,
			collation = "utf8mb4_unicode_ci",
			length = 10,
		},
	},
	phone_phone_contacts = {
		{
			column = "email",
			type = "VARCHAR",
			allowNull = true,
			isKey = false,
			default = "NULL",
			collation = "utf8mb4_unicode_ci",
			length = 50,
		},
		{
			column = "firstname",
			type = "VARCHAR",
			allowNull = false,
			isKey = false,
			default = "''",
			collation = "utf8mb4_unicode_ci",
			length = 50,
		},
		{
			column = "profile_image",
			type = "VARCHAR",
			allowNull = true,
			isKey = false,
			default = "NULL",
			collation = "utf8mb4_unicode_ci",
			length = 500,
		},
		{
			column = "phone_number",
			type = "VARCHAR",
			allowNull = false,
			isKey = true,
			collation = "utf8mb4_unicode_ci",
			length = 15,
		},
		{
			column = "lastname",
			type = "VARCHAR",
			allowNull = false,
			isKey = false,
			default = "''",
			collation = "utf8mb4_unicode_ci",
			length = 50,
		},
		{
			column = "favourite",
			type = "TINYINT",
			allowNull = true,
			isKey = false,
			default = "0",
		},
		{
			column = "contact_phone_number",
			type = "VARCHAR",
			allowNull = false,
			isKey = true,
			collation = "utf8mb4_unicode_ci",
			length = 15,
		},
		{
			column = "address",
			type = "VARCHAR",
			allowNull = true,
			isKey = false,
			default = "NULL",
			collation = "utf8mb4_unicode_ci",
			length = 50,
		},
	},
	phone_voice_memos_recordings = {
		{
			column = "created_at",
			type = "TIMESTAMP",
			allowNull = false,
			isKey = false,
			default = "current_timestamp()",
		},
		{
			column = "file_name",
			type = "VARCHAR",
			allowNull = false,
			isKey = false,
			collation = "utf8mb4_unicode_ci",
			length = 50,
		},
		{
			column = "phone_number",
			type = "VARCHAR",
			allowNull = false,
			isKey = true,
			collation = "utf8mb4_unicode_ci",
			length = 15,
		},
		{
			column = "file_length",
			type = "INT",
			allowNull = false,
			isKey = false,
		},
		{
			column = "id",
			type = "INT",
			allowNull = false,
			isKey = true,
		},
		{
			column = "file_url",
			type = "VARCHAR",
			allowNull = false,
			isKey = false,
			collation = "utf8mb4_unicode_ci",
			length = 500,
		},
	},
	phone_tinder_accounts = {
		{
			column = "active",
			type = "TINYINT",
			allowNull = false,
			isKey = false,
			default = "1",
		},
		{
			column = "interested_women",
			type = "TINYINT",
			allowNull = false,
			isKey = false,
		},
		{
			column = "is_male",
			type = "TINYINT",
			allowNull = false,
			isKey = false,
		},
		{
			column = "name",
			type = "VARCHAR",
			allowNull = false,
			isKey = false,
			collation = "utf8mb4_unicode_ci",
			length = 50,
		},
		{
			column = "phone_number",
			type = "VARCHAR",
			allowNull = false,
			isKey = true,
			collation = "utf8mb4_unicode_ci",
			length = 15,
		},
		{
			column = "last_seen",
			type = "DATETIME",
			allowNull = false,
			isKey = false,
			default = "current_timestamp()",
		},
		{
			column = "dob",
			type = "DATE",
			allowNull = false,
			isKey = false,
		},
		{
			column = "bio",
			type = "VARCHAR",
			allowNull = true,
			isKey = false,
			default = "NULL",
			collation = "utf8mb4_unicode_ci",
			length = 500,
		},
		{
			column = "interested_men",
			type = "TINYINT",
			allowNull = false,
			isKey = false,
		},
		{
			column = "photos",
			type = "TEXT",
			allowNull = true,
			isKey = false,
			default = "NULL",
			collation = "utf8mb4_unicode_ci",
		},
	},
	phone_message_messages = {
		{
			column = "content",
			type = "VARCHAR",
			allowNull = true,
			isKey = false,
			default = "NULL",
			collation = "utf8mb4_unicode_ci",
			length = 1000,
		},
		{
			column = "attachments",
			type = "TEXT",
			allowNull = true,
			isKey = false,
			default = "NULL",
			collation = "utf8mb4_unicode_ci",
		},
		{
			column = "sender",
			type = "VARCHAR",
			allowNull = false,
			isKey = false,
			collation = "utf8mb4_unicode_ci",
			length = 15,
		},
		{
			column = "timestamp",
			type = "TIMESTAMP",
			allowNull = false,
			isKey = false,
			default = "current_timestamp()",
		},
		{
			column = "channel_id",
			type = "INT",
			allowNull = false,
			isKey = true,
		},
		{
			column = "id",
			type = "INT",
			allowNull = false,
			isKey = true,
		},
	},
	phone_twitter_notifications = {
		{
			column = "type",
			type = "VARCHAR",
			allowNull = false,
			isKey = false,
			collation = "utf8mb4_unicode_ci",
			length = 20,
		},
		{
			column = "from",
			type = "VARCHAR",
			allowNull = false,
			isKey = true,
			collation = "utf8mb4_unicode_ci",
			length = 20,
		},
		{
			column = "timestamp",
			type = "TIMESTAMP",
			allowNull = false,
			isKey = false,
			default = "current_timestamp()",
		},
		{
			column = "tweet_id",
			type = "VARCHAR",
			allowNull = true,
			isKey = false,
			default = "NULL",
			collation = "utf8mb4_unicode_ci",
			length = 50,
		},
		{
			column = "username",
			type = "VARCHAR",
			allowNull = false,
			isKey = true,
			collation = "utf8mb4_unicode_ci",
			length = 20,
		},
		{
			column = "id",
			type = "VARCHAR",
			allowNull = false,
			isKey = true,
			collation = "utf8mb4_unicode_ci",
			length = 10,
		},
	},
	phone_tiktok_pinned_videos = {
		{
			column = "video_id",
			type = "VARCHAR",
			allowNull = false,
			isKey = true,
			collation = "utf8mb4_unicode_ci",
			length = 10,
		},
		{
			column = "username",
			type = "VARCHAR",
			allowNull = false,
			isKey = true,
			collation = "utf8mb4_unicode_ci",
			length = 20,
		},
	},
	phone_phones = {
		{
			column = "settings",
			type = "LONGTEXT",
			allowNull = true,
			isKey = false,
			default = "NULL",
			collation = "utf8mb4_unicode_ci",
		},
		{
			column = "is_setup",
			type = "TINYINT",
			allowNull = true,
			isKey = false,
			default = "0",
		},
		{
			column = "pin",
			type = "VARCHAR",
			allowNull = true,
			isKey = false,
			default = "NULL",
			collation = "utf8mb4_unicode_ci",
			length = 4,
		},
		{
			column = "id",
			type = "VARCHAR",
			allowNull = false,
			isKey = true,
			collation = "utf8mb4_unicode_ci",
			length = 100,
		},
		{
			column = "assigned",
			type = "TINYINT",
			allowNull = true,
			isKey = false,
			default = "0",
		},
		{
			column = "name",
			type = "VARCHAR",
			allowNull = true,
			isKey = false,
			default = "NULL",
			collation = "utf8mb4_unicode_ci",
			length = 50,
		},
		{
			column = "last_seen",
			type = "TIMESTAMP",
			allowNull = false,
			isKey = false,
			default = "current_timestamp()",
		},
		{
			column = "battery",
			type = "INT",
			allowNull = false,
			isKey = false,
			default = "100",
		},
		{
			column = "face_id",
			type = "VARCHAR",
			allowNull = true,
			isKey = false,
			default = "NULL",
			collation = "utf8mb4_unicode_ci",
			length = 100,
		},
		{
			column = "owner_id",
			type = "VARCHAR",
			allowNull = false,
			isKey = false,
			collation = "utf8mb4_unicode_ci",
			length = 100,
		},
		{
			column = "phone_number",
			type = "VARCHAR",
			allowNull = false,
			isKey = true,
			collation = "utf8mb4_unicode_ci",
			length = 15,
		},
	},
	phone_phone_blocked_numbers = {
		{
			column = "phone_number",
			type = "VARCHAR",
			allowNull = false,
			isKey = true,
			collation = "utf8mb4_unicode_ci",
			length = 15,
		},
		{
			column = "blocked_number",
			type = "VARCHAR",
			allowNull = false,
			isKey = true,
			collation = "utf8mb4_unicode_ci",
			length = 15,
		},
	},
	phone_tiktok_saves = {
		{
			column = "video_id",
			type = "VARCHAR",
			allowNull = false,
			isKey = true,
			collation = "utf8mb4_unicode_ci",
			length = 10,
		},
		{
			column = "username",
			type = "VARCHAR",
			allowNull = false,
			isKey = true,
			collation = "utf8mb4_unicode_ci",
			length = 20,
		},
	},
	phone_darkchat_accounts = {
		{
			column = "password",
			type = "VARCHAR",
			allowNull = false,
			isKey = false,
			collation = "utf8mb4_unicode_ci",
			length = 100,
		},
		{
			column = "phone_number",
			type = "VARCHAR",
			allowNull = false,
			isKey = true,
			collation = "utf8mb4_unicode_ci",
			length = 15,
		},
		{
			column = "username",
			type = "VARCHAR",
			allowNull = false,
			isKey = true,
			collation = "utf8mb4_unicode_ci",
			length = 20,
		},
	},
	phone_tiktok_messages = {
		{
			column = "content",
			type = "VARCHAR",
			allowNull = false,
			isKey = false,
			collation = "utf8mb4_unicode_ci",
			length = 500,
		},
		{
			column = "sender",
			type = "VARCHAR",
			allowNull = false,
			isKey = true,
			collation = "utf8mb4_unicode_ci",
			length = 20,
		},
		{
			column = "timestamp",
			type = "TIMESTAMP",
			allowNull = false,
			isKey = false,
			default = "current_timestamp()",
		},
		{
			column = "channel_id",
			type = "VARCHAR",
			allowNull = false,
			isKey = true,
			collation = "utf8mb4_unicode_ci",
			length = 10,
		},
		{
			column = "id",
			type = "VARCHAR",
			allowNull = false,
			isKey = true,
			collation = "utf8mb4_unicode_ci",
			length = 10,
		},
	},
	phone_tinder_swipes = {
		{
			column = "swipee",
			type = "VARCHAR",
			allowNull = false,
			isKey = true,
			collation = "utf8mb4_unicode_ci",
			length = 15,
		},
		{
			column = "swiper",
			type = "VARCHAR",
			allowNull = false,
			isKey = true,
			collation = "utf8mb4_unicode_ci",
			length = 15,
		},
		{
			column = "liked",
			type = "TINYINT",
			allowNull = false,
			isKey = false,
			default = "0",
		},
	},
	phone_twitter_accounts = {
		{
			column = "profile_image",
			type = "VARCHAR",
			allowNull = true,
			isKey = false,
			default = "NULL",
			collation = "utf8mb4_unicode_ci",
			length = 500,
		},
		{
			column = "profile_header",
			type = "VARCHAR",
			allowNull = true,
			isKey = false,
			default = "NULL",
			collation = "utf8mb4_unicode_ci",
			length = 500,
		},
		{
			column = "pinned_tweet",
			type = "VARCHAR",
			allowNull = true,
			isKey = false,
			default = "NULL",
			collation = "utf8mb4_unicode_ci",
			length = 50,
		},
		{
			column = "following_count",
			type = "INT",
			allowNull = false,
			isKey = false,
			default = "0",
		},
		{
			column = "private",
			type = "TINYINT",
			allowNull = true,
			isKey = false,
			default = "0",
		},
		{
			column = "date_joined",
			type = "TIMESTAMP",
			allowNull = false,
			isKey = false,
			default = "current_timestamp()",
		},
		{
			column = "display_name",
			type = "VARCHAR",
			allowNull = false,
			isKey = false,
			collation = "utf8mb4_unicode_ci",
			length = 30,
		},
		{
			column = "phone_number",
			type = "VARCHAR",
			allowNull = false,
			isKey = true,
			collation = "utf8mb4_unicode_ci",
			length = 15,
		},
		{
			column = "follower_count",
			type = "INT",
			allowNull = false,
			isKey = false,
			default = "0",
		},
		{
			column = "password",
			type = "VARCHAR",
			allowNull = false,
			isKey = false,
			collation = "utf8mb4_unicode_ci",
			length = 100,
		},
		{
			column = "verified",
			type = "TINYINT",
			allowNull = true,
			isKey = false,
			default = "0",
		},
		{
			column = "bio",
			type = "VARCHAR",
			allowNull = true,
			isKey = false,
			default = "NULL",
			collation = "utf8mb4_unicode_ci",
			length = 100,
		},
		{
			column = "username",
			type = "VARCHAR",
			allowNull = false,
			isKey = true,
			collation = "utf8mb4_unicode_ci",
			length = 20,
		},
	},
	phone_phone_voicemail = {
		{
			column = "hide_caller_id",
			type = "TINYINT",
			allowNull = true,
			isKey = false,
			default = "0",
		},
		{
			column = "caller",
			type = "VARCHAR",
			allowNull = false,
			isKey = false,
			collation = "utf8mb4_unicode_ci",
			length = 15,
		},
		{
			column = "duration",
			type = "INT",
			allowNull = false,
			isKey = false,
		},
		{
			column = "timestamp",
			type = "TIMESTAMP",
			allowNull = false,
			isKey = false,
			default = "current_timestamp()",
		},
		{
			column = "callee",
			type = "VARCHAR",
			allowNull = false,
			isKey = false,
			collation = "utf8mb4_unicode_ci",
			length = 15,
		},
		{
			column = "url",
			type = "VARCHAR",
			allowNull = false,
			isKey = false,
			collation = "utf8mb4_unicode_ci",
			length = 500,
		},
		{
			column = "id",
			type = "INT",
			allowNull = false,
			isKey = true,
		},
	},
	phone_instagram_messages = {
		{
			column = "content",
			type = "VARCHAR",
			allowNull = true,
			isKey = false,
			default = "NULL",
			collation = "utf8mb4_unicode_ci",
			length = 1000,
		},
		{
			column = "attachments",
			type = "TEXT",
			allowNull = true,
			isKey = false,
			default = "NULL",
			collation = "utf8mb4_unicode_ci",
		},
		{
			column = "sender",
			type = "VARCHAR",
			allowNull = false,
			isKey = true,
			collation = "utf8mb4_unicode_ci",
			length = 20,
		},
		{
			column = "timestamp",
			type = "TIMESTAMP",
			allowNull = false,
			isKey = false,
			default = "current_timestamp()",
		},
		{
			column = "id",
			type = "VARCHAR",
			allowNull = false,
			isKey = true,
			collation = "utf8mb4_unicode_ci",
			length = 10,
		},
		{
			column = "recipient",
			type = "VARCHAR",
			allowNull = false,
			isKey = true,
			collation = "utf8mb4_unicode_ci",
			length = 20,
		},
	},
	phone_yellow_pages_posts = {
		{
			column = "description",
			type = "VARCHAR",
			allowNull = false,
			isKey = false,
			collation = "utf8mb4_unicode_ci",
			length = 1000,
		},
		{
			column = "phone_number",
			type = "VARCHAR",
			allowNull = false,
			isKey = false,
			collation = "utf8mb4_unicode_ci",
			length = 15,
		},
		{
			column = "price",
			type = "INT",
			allowNull = true,
			isKey = false,
			default = "NULL",
		},
		{
			column = "timestamp",
			type = "TIMESTAMP",
			allowNull = false,
			isKey = false,
			default = "current_timestamp()",
		},
		{
			column = "title",
			type = "VARCHAR",
			allowNull = false,
			isKey = false,
			collation = "utf8mb4_unicode_ci",
			length = 50,
		},
		{
			column = "attachment",
			type = "VARCHAR",
			allowNull = true,
			isKey = false,
			default = "NULL",
			collation = "utf8mb4_unicode_ci",
			length = 500,
		},
		{
			column = "id",
			type = "INT",
			allowNull = false,
			isKey = true,
		},
	},
	phone_marketplace_posts = {
		{
			column = "description",
			type = "VARCHAR",
			allowNull = false,
			isKey = false,
			collation = "utf8mb4_unicode_ci",
			length = 1000,
		},
		{
			column = "phone_number",
			type = "VARCHAR",
			allowNull = false,
			isKey = true,
			collation = "utf8mb4_unicode_ci",
			length = 15,
		},
		{
			column = "price",
			type = "INT",
			allowNull = false,
			isKey = false,
		},
		{
			column = "timestamp",
			type = "TIMESTAMP",
			allowNull = false,
			isKey = false,
			default = "current_timestamp()",
		},
		{
			column = "attachments",
			type = "TEXT",
			allowNull = true,
			isKey = false,
			default = "NULL",
			collation = "utf8mb4_unicode_ci",
		},
		{
			column = "title",
			type = "VARCHAR",
			allowNull = false,
			isKey = false,
			collation = "utf8mb4_unicode_ci",
			length = 50,
		},
		{
			column = "id",
			type = "INT",
			allowNull = false,
			isKey = true,
		},
	},
	phone_backups = {
		{
			column = "phone_number",
			type = "VARCHAR",
			allowNull = false,
			isKey = true,
			collation = "utf8mb4_unicode_ci",
			length = 15,
		},
		{
			column = "id",
			type = "VARCHAR",
			allowNull = false,
			isKey = true,
			collation = "utf8mb4_unicode_ci",
			length = 100,
		},
	},
	phone_twitter_hashtags = {
		{
			column = "amount",
			type = "INT",
			allowNull = false,
			isKey = false,
			default = "0",
		},
		{
			column = "last_used",
			type = "TIMESTAMP",
			allowNull = false,
			isKey = false,
			default = "current_timestamp()",
		},
		{
			column = "hashtag",
			type = "VARCHAR",
			allowNull = false,
			isKey = true,
			collation = "utf8mb4_unicode_ci",
			length = 50,
		},
	},
	phone_message_members = {
		{
			column = "phone_number",
			type = "VARCHAR",
			allowNull = false,
			isKey = true,
			collation = "utf8mb4_unicode_ci",
			length = 15,
		},
		{
			column = "deleted",
			type = "TINYINT",
			allowNull = false,
			isKey = false,
			default = "0",
		},
		{
			column = "channel_id",
			type = "INT",
			allowNull = false,
			isKey = true,
		},
		{
			column = "unread",
			type = "INT",
			allowNull = false,
			isKey = false,
			default = "0",
		},
		{
			column = "is_owner",
			type = "TINYINT",
			allowNull = false,
			isKey = false,
			default = "0",
		},
	},
	phone_services_channels = {
		{
			column = "company",
			type = "VARCHAR",
			allowNull = false,
			isKey = false,
			collation = "utf8mb4_unicode_ci",
			length = 50,
		},
		{
			column = "phone_number",
			type = "VARCHAR",
			allowNull = false,
			isKey = false,
			collation = "utf8mb4_unicode_ci",
			length = 15,
		},
		{
			column = "timestamp",
			type = "TIMESTAMP",
			allowNull = false,
			isKey = false,
			default = "current_timestamp()",
		},
		{
			column = "last_message",
			type = "VARCHAR",
			allowNull = true,
			isKey = false,
			default = "NULL",
			collation = "utf8mb4_unicode_ci",
			length = 100,
		},
		{
			column = "id",
			type = "INT",
			allowNull = false,
			isKey = true,
		},
	},
	phone_music_saved_playlists = {
		{
			column = "playlist_id",
			type = "INT",
			allowNull = false,
			isKey = true,
		},
		{
			column = "phone_number",
			type = "VARCHAR",
			allowNull = false,
			isKey = true,
			collation = "utf8mb4_unicode_ci",
			length = 15,
		},
	},
	phone_instagram_stories_views = {
		{
			column = "story_id",
			type = "VARCHAR",
			allowNull = false,
			isKey = true,
			collation = "utf8mb4_unicode_ci",
			length = 50,
		},
		{
			column = "timestamp",
			type = "TIMESTAMP",
			allowNull = false,
			isKey = false,
			default = "current_timestamp()",
		},
		{
			column = "viewer",
			type = "VARCHAR",
			allowNull = false,
			isKey = true,
			collation = "utf8mb4_unicode_ci",
			length = 20,
		},
	},
	phone_instagram_follows = {
		{
			column = "followed",
			type = "VARCHAR",
			allowNull = false,
			isKey = true,
			collation = "utf8mb4_unicode_ci",
			length = 20,
		},
		{
			column = "follower",
			type = "VARCHAR",
			allowNull = false,
			isKey = true,
			collation = "utf8mb4_unicode_ci",
			length = 20,
		},
	},
	phone_instagram_comments = {
		{
			column = "post_id",
			type = "VARCHAR",
			allowNull = false,
			isKey = true,
			collation = "utf8mb4_unicode_ci",
			length = 50,
		},
		{
			column = "like_count",
			type = "INT",
			allowNull = false,
			isKey = false,
			default = "0",
		},
		{
			column = "comment",
			type = "VARCHAR",
			allowNull = false,
			isKey = false,
			default = "''",
			collation = "utf8mb4_unicode_ci",
			length = 500,
		},
		{
			column = "timestamp",
			type = "TIMESTAMP",
			allowNull = false,
			isKey = false,
			default = "current_timestamp()",
		},
		{
			column = "id",
			type = "VARCHAR",
			allowNull = false,
			isKey = true,
			collation = "utf8mb4_unicode_ci",
			length = 10,
		},
		{
			column = "username",
			type = "VARCHAR",
			allowNull = false,
			isKey = true,
			collation = "utf8mb4_unicode_ci",
			length = 20,
		},
	},
	phone_mail_messages = {
		{
			column = "content",
			type = "TEXT",
			allowNull = false,
			isKey = false,
			collation = "utf8mb4_unicode_ci",
		},
		{
			column = "subject",
			type = "VARCHAR",
			allowNull = false,
			isKey = false,
			collation = "utf8mb4_unicode_ci",
			length = 100,
		},
		{
			column = "recipient",
			type = "VARCHAR",
			allowNull = false,
			isKey = false,
			collation = "utf8mb4_unicode_ci",
			length = 100,
		},
		{
			column = "actions",
			type = "LONGTEXT",
			allowNull = true,
			isKey = false,
			default = "NULL",
			collation = "utf8mb4_unicode_ci",
		},
		{
			column = "attachments",
			type = "LONGTEXT",
			allowNull = true,
			isKey = false,
			default = "NULL",
			collation = "utf8mb4_unicode_ci",
		},
		{
			column = "sender",
			type = "VARCHAR",
			allowNull = false,
			isKey = false,
			collation = "utf8mb4_unicode_ci",
			length = 100,
		},
		{
			column = "timestamp",
			type = "TIMESTAMP",
			allowNull = false,
			isKey = false,
			default = "current_timestamp()",
		},
		{
			column = "id",
			type = "INT",
			allowNull = false,
			isKey = true,
		},
		{
			column = "read",
			type = "TINYINT",
			allowNull = false,
			isKey = false,
			default = "0",
		},
	},
	phone_mail_deleted = {
		{
			column = "address",
			type = "VARCHAR",
			allowNull = false,
			isKey = true,
			collation = "utf8mb4_unicode_ci",
			length = 100,
		},
		{
			column = "message_id",
			type = "INT",
			allowNull = false,
			isKey = true,
		},
	},
	phone_twitter_follow_requests = {
		{
			column = "requester",
			type = "VARCHAR",
			allowNull = false,
			isKey = true,
			collation = "utf8mb4_unicode_ci",
			length = 20,
		},
		{
			column = "requestee",
			type = "VARCHAR",
			allowNull = false,
			isKey = true,
			collation = "utf8mb4_unicode_ci",
			length = 20,
		},
		{
			column = "timestamp",
			type = "TIMESTAMP",
			allowNull = false,
			isKey = false,
			default = "current_timestamp()",
		},
	},
	phone_twitter_promoted = {
		{
			column = "views",
			type = "INT",
			allowNull = false,
			isKey = false,
			default = "0",
		},
		{
			column = "promotions",
			type = "INT",
			allowNull = false,
			isKey = false,
			default = "0",
		},
		{
			column = "tweet_id",
			type = "VARCHAR",
			allowNull = false,
			isKey = true,
			collation = "utf8mb4_unicode_ci",
			length = 50,
		},
	},
	phone_logged_in_accounts = {
		{
			column = "active",
			type = "TINYINT",
			allowNull = false,
			isKey = false,
			default = "0",
		},
		{
			column = "app",
			type = "VARCHAR",
			allowNull = false,
			isKey = true,
			collation = "utf8mb4_unicode_ci",
			length = 50,
		},
		{
			column = "phone_number",
			type = "VARCHAR",
			allowNull = false,
			isKey = true,
			collation = "utf8mb4_unicode_ci",
			length = 15,
		},
		{
			column = "username",
			type = "VARCHAR",
			allowNull = false,
			isKey = true,
			collation = "utf8mb4_unicode_ci",
			length = 100,
		},
	},
	phone_twitter_likes = {
		{
			column = "timestamp",
			type = "TIMESTAMP",
			allowNull = false,
			isKey = false,
			default = "current_timestamp()",
		},
		{
			column = "tweet_id",
			type = "VARCHAR",
			allowNull = false,
			isKey = true,
			collation = "utf8mb4_unicode_ci",
			length = 50,
		},
		{
			column = "username",
			type = "VARCHAR",
			allowNull = false,
			isKey = true,
			collation = "utf8mb4_unicode_ci",
			length = 20,
		},
	},
	phone_notes = {
		{
			column = "content",
			type = "LONGTEXT",
			allowNull = true,
			isKey = false,
			default = "NULL",
			collation = "utf8mb4_unicode_ci",
		},
		{
			column = "title",
			type = "VARCHAR",
			allowNull = false,
			isKey = false,
			collation = "utf8mb4_unicode_ci",
			length = 50,
		},
		{
			column = "phone_number",
			type = "VARCHAR",
			allowNull = false,
			isKey = true,
			collation = "utf8mb4_unicode_ci",
			length = 15,
		},
		{
			column = "timestamp",
			type = "TIMESTAMP",
			allowNull = false,
			isKey = false,
			default = "current_timestamp()",
		},
		{
			column = "id",
			type = "INT",
			allowNull = false,
			isKey = true,
		},
	},
	phone_tiktok_likes = {
		{
			column = "video_id",
			type = "VARCHAR",
			allowNull = false,
			isKey = true,
			collation = "utf8mb4_unicode_ci",
			length = 10,
		},
		{
			column = "username",
			type = "VARCHAR",
			allowNull = false,
			isKey = true,
			collation = "utf8mb4_unicode_ci",
			length = 20,
		},
	},
	phone_tiktok_comments_likes = {
		{
			column = "comment_id",
			type = "VARCHAR",
			allowNull = false,
			isKey = true,
			collation = "utf8mb4_unicode_ci",
			length = 10,
		},
		{
			column = "username",
			type = "VARCHAR",
			allowNull = false,
			isKey = true,
			collation = "utf8mb4_unicode_ci",
			length = 20,
		},
	},
	phone_last_phone = {
		{
			column = "phone_number",
			type = "VARCHAR",
			allowNull = false,
			isKey = true,
			collation = "utf8mb4_unicode_ci",
			length = 15,
		},
		{
			column = "id",
			type = "VARCHAR",
			allowNull = false,
			isKey = true,
			collation = "utf8mb4_unicode_ci",
			length = 100,
		},
	},
	phone_instagram_follow_requests = {
		{
			column = "requester",
			type = "VARCHAR",
			allowNull = false,
			isKey = true,
			collation = "utf8mb4_unicode_ci",
			length = 20,
		},
		{
			column = "requestee",
			type = "VARCHAR",
			allowNull = false,
			isKey = true,
			collation = "utf8mb4_unicode_ci",
			length = 20,
		},
		{
			column = "timestamp",
			type = "TIMESTAMP",
			allowNull = false,
			isKey = false,
			default = "current_timestamp()",
		},
	},
	phone_music_songs = {
		{
			column = "playlist_id",
			type = "INT",
			allowNull = false,
			isKey = true,
		},
		{
			column = "song_id",
			type = "VARCHAR",
			allowNull = false,
			isKey = true,
			collation = "utf8mb4_unicode_ci",
			length = 100,
		},
	},
	phone_instagram_likes = {
		{
			column = "is_comment",
			type = "TINYINT",
			allowNull = false,
			isKey = false,
			default = "0",
		},
		{
			column = "id",
			type = "VARCHAR",
			allowNull = false,
			isKey = true,
			collation = "utf8mb4_unicode_ci",
			length = 10,
		},
		{
			column = "username",
			type = "VARCHAR",
			allowNull = false,
			isKey = true,
			collation = "utf8mb4_unicode_ci",
			length = 20,
		},
	},
	phone_twitter_tweets = {
		{
			column = "content",
			type = "VARCHAR",
			allowNull = true,
			isKey = false,
			default = "NULL",
			collation = "utf8mb4_unicode_ci",
			length = 280,
		},
		{
			column = "like_count",
			type = "INT",
			allowNull = true,
			isKey = false,
			default = "0",
		},
		{
			column = "id",
			type = "VARCHAR",
			allowNull = false,
			isKey = true,
			collation = "utf8mb4_unicode_ci",
			length = 10,
		},
		{
			column = "attachments",
			type = "TEXT",
			allowNull = true,
			isKey = false,
			default = "NULL",
			collation = "utf8mb4_unicode_ci",
		},
		{
			column = "reply_count",
			type = "INT",
			allowNull = true,
			isKey = false,
			default = "0",
		},
		{
			column = "timestamp",
			type = "TIMESTAMP",
			allowNull = false,
			isKey = false,
			default = "current_timestamp()",
		},
		{
			column = "reply_to",
			type = "VARCHAR",
			allowNull = true,
			isKey = false,
			default = "NULL",
			collation = "utf8mb4_unicode_ci",
			length = 50,
		},
		{
			column = "retweet_count",
			type = "INT",
			allowNull = true,
			isKey = false,
			default = "0",
		},
		{
			column = "username",
			type = "VARCHAR",
			allowNull = false,
			isKey = true,
			collation = "utf8mb4_unicode_ci",
			length = 20,
		},
	},
	phone_photo_albums = {
		{
			column = "shared",
			type = "TINYINT",
			allowNull = false,
			isKey = false,
			default = "0",
		},
		{
			column = "title",
			type = "VARCHAR",
			allowNull = false,
			isKey = false,
			collation = "utf8mb4_unicode_ci",
			length = 100,
		},
		{
			column = "phone_number",
			type = "VARCHAR",
			allowNull = false,
			isKey = true,
			collation = "utf8mb4_unicode_ci",
			length = 15,
		},
		{
			column = "id",
			type = "INT",
			allowNull = false,
			isKey = true,
		},
	},
	phone_darkchat_channels = {
		{
			column = "name",
			type = "VARCHAR",
			allowNull = false,
			isKey = true,
			collation = "utf8mb4_unicode_ci",
			length = 50,
		},
	},
	phone_tiktok_channels = {
		{
			column = "member_1",
			type = "VARCHAR",
			allowNull = false,
			isKey = true,
			collation = "utf8mb4_unicode_ci",
			length = 20,
		},
		{
			column = "timestamp",
			type = "TIMESTAMP",
			allowNull = false,
			isKey = false,
			default = "current_timestamp()",
		},
		{
			column = "last_message",
			type = "VARCHAR",
			allowNull = false,
			isKey = false,
			collation = "utf8mb4_unicode_ci",
			length = 50,
		},
		{
			column = "member_2",
			type = "VARCHAR",
			allowNull = false,
			isKey = true,
			collation = "utf8mb4_unicode_ci",
			length = 20,
		},
		{
			column = "id",
			type = "VARCHAR",
			allowNull = false,
			isKey = true,
			collation = "utf8mb4_unicode_ci",
			length = 10,
		},
	},
	phone_mail_accounts = {
		{
			column = "address",
			type = "VARCHAR",
			allowNull = false,
			isKey = true,
			collation = "utf8mb4_unicode_ci",
			length = 100,
		},
		{
			column = "password",
			type = "VARCHAR",
			allowNull = false,
			isKey = false,
			collation = "utf8mb4_unicode_ci",
			length = 100,
		},
	},
	phone_photos = {
		{
			column = "link",
			type = "VARCHAR",
			allowNull = false,
			isKey = false,
			collation = "utf8mb4_unicode_ci",
			length = 500,
		},
		{
			column = "metadata",
			type = "VARCHAR",
			allowNull = true,
			isKey = false,
			default = "NULL",
			collation = "utf8mb4_unicode_ci",
			length = 20,
		},
		{
			column = "phone_number",
			type = "VARCHAR",
			allowNull = false,
			isKey = true,
			collation = "utf8mb4_unicode_ci",
			length = 15,
		},
		{
			column = "size",
			type = "FLOAT",
			allowNull = false,
			isKey = false,
			default = "0",
		},
		{
			column = "timestamp",
			type = "TIMESTAMP",
			allowNull = false,
			isKey = false,
			default = "current_timestamp()",
		},
		{
			column = "is_favourite",
			type = "TINYINT",
			allowNull = true,
			isKey = false,
			default = "0",
		},
		{
			column = "is_video",
			type = "TINYINT",
			allowNull = true,
			isKey = false,
			default = "0",
		},
		{
			column = "id",
			type = "INT",
			allowNull = false,
			isKey = true,
		},
	},
	phone_maps_locations = {
		{
			column = "y_pos",
			type = "FLOAT",
			allowNull = false,
			isKey = false,
		},
		{
			column = "name",
			type = "VARCHAR",
			allowNull = false,
			isKey = false,
			collation = "utf8mb4_unicode_ci",
			length = 50,
		},
		{
			column = "id",
			type = "INT",
			allowNull = false,
			isKey = true,
		},
		{
			column = "phone_number",
			type = "VARCHAR",
			allowNull = false,
			isKey = true,
			collation = "utf8mb4_unicode_ci",
			length = 15,
		},
		{
			column = "x_pos",
			type = "FLOAT",
			allowNull = false,
			isKey = false,
		},
	},
	phone_tiktok_views = {
		{
			column = "video_id",
			type = "VARCHAR",
			allowNull = false,
			isKey = true,
			collation = "utf8mb4_unicode_ci",
			length = 10,
		},
		{
			column = "username",
			type = "VARCHAR",
			allowNull = false,
			isKey = true,
			collation = "utf8mb4_unicode_ci",
			length = 20,
		},
	},
	phone_photo_album_members = {
		{
			column = "phone_number",
			type = "VARCHAR",
			allowNull = false,
			isKey = true,
			collation = "utf8mb4_unicode_ci",
			length = 15,
		},
		{
			column = "album_id",
			type = "INT",
			allowNull = false,
			isKey = true,
		},
	},
	phone_services_messages = {
		{
			column = "y_pos",
			type = "INT",
			allowNull = true,
			isKey = false,
			default = "NULL",
		},
		{
			column = "x_pos",
			type = "INT",
			allowNull = true,
			isKey = false,
			default = "NULL",
		},
		{
			column = "sender",
			type = "VARCHAR",
			allowNull = false,
			isKey = false,
			collation = "utf8mb4_unicode_ci",
			length = 15,
		},
		{
			column = "timestamp",
			type = "TIMESTAMP",
			allowNull = false,
			isKey = false,
			default = "current_timestamp()",
		},
		{
			column = "channel_id",
			type = "INT",
			allowNull = false,
			isKey = true,
		},
		{
			column = "message",
			type = "VARCHAR",
			allowNull = false,
			isKey = false,
			collation = "utf8mb4_unicode_ci",
			length = 1000,
		},
		{
			column = "id",
			type = "INT",
			allowNull = false,
			isKey = true,
		},
	},
	phone_tiktok_follows = {
		{
			column = "followed",
			type = "VARCHAR",
			allowNull = false,
			isKey = true,
			collation = "utf8mb4_unicode_ci",
			length = 20,
		},
		{
			column = "follower",
			type = "VARCHAR",
			allowNull = false,
			isKey = true,
			collation = "utf8mb4_unicode_ci",
			length = 20,
		},
	},
	phone_twitter_messages = {
		{
			column = "content",
			type = "VARCHAR",
			allowNull = true,
			isKey = false,
			default = "NULL",
			collation = "utf8mb4_unicode_ci",
			length = 1000,
		},
		{
			column = "attachments",
			type = "TEXT",
			allowNull = true,
			isKey = false,
			default = "NULL",
			collation = "utf8mb4_unicode_ci",
		},
		{
			column = "sender",
			type = "VARCHAR",
			allowNull = false,
			isKey = true,
			collation = "utf8mb4_unicode_ci",
			length = 20,
		},
		{
			column = "timestamp",
			type = "TIMESTAMP",
			allowNull = false,
			isKey = false,
			default = "current_timestamp()",
		},
		{
			column = "id",
			type = "VARCHAR",
			allowNull = false,
			isKey = true,
			collation = "utf8mb4_unicode_ci",
			length = 10,
		},
		{
			column = "recipient",
			type = "VARCHAR",
			allowNull = false,
			isKey = true,
			collation = "utf8mb4_unicode_ci",
			length = 20,
		},
	},
	phone_darkchat_members = {
		{
			column = "channel_name",
			type = "VARCHAR",
			allowNull = false,
			isKey = true,
			collation = "utf8mb4_unicode_ci",
			length = 50,
		},
		{
			column = "username",
			type = "VARCHAR",
			allowNull = false,
			isKey = true,
			collation = "utf8mb4_unicode_ci",
			length = 20,
		},
	},
	phone_tinder_matches = {
		{
			column = "phone_number_2",
			type = "VARCHAR",
			allowNull = false,
			isKey = true,
			collation = "utf8mb4_unicode_ci",
			length = 15,
		},
		{
			column = "latest_message_timestamp",
			type = "TIMESTAMP",
			allowNull = true,
			isKey = false,
			default = "NULL",
		},
		{
			column = "latest_message",
			type = "VARCHAR",
			allowNull = true,
			isKey = false,
			default = "NULL",
			collation = "utf8mb4_unicode_ci",
			length = 1000,
		},
		{
			column = "phone_number_1",
			type = "VARCHAR",
			allowNull = false,
			isKey = true,
			collation = "utf8mb4_unicode_ci",
			length = 15,
		},
	},
	phone_tinder_messages = {
		{
			column = "content",
			type = "VARCHAR",
			allowNull = true,
			isKey = false,
			default = "NULL",
			collation = "utf8mb4_unicode_ci",
			length = 1000,
		},
		{
			column = "attachments",
			type = "TEXT",
			allowNull = true,
			isKey = false,
			default = "NULL",
			collation = "utf8mb4_unicode_ci",
		},
		{
			column = "sender",
			type = "VARCHAR",
			allowNull = false,
			isKey = true,
			collation = "utf8mb4_unicode_ci",
			length = 15,
		},
		{
			column = "timestamp",
			type = "TIMESTAMP",
			allowNull = false,
			isKey = false,
			default = "current_timestamp()",
		},
		{
			column = "id",
			type = "INT",
			allowNull = false,
			isKey = true,
		},
		{
			column = "recipient",
			type = "VARCHAR",
			allowNull = false,
			isKey = true,
			collation = "utf8mb4_unicode_ci",
			length = 15,
		},
	},
}

function GetDefaultDatabaseTables()
    return defaultTables
end
