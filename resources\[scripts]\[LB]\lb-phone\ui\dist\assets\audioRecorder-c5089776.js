var T=Object.defineProperty;var U=(i,t,e)=>t in i?T(i,t,{enumerable:!0,configurable:!0,writable:!0,value:e}):i[t]=e;var s=(i,t,e)=>(U(i,typeof t!="symbol"?t+"":t,e),e),y=(i,t,e)=>{if(!t.has(i))throw TypeError("Cannot "+e)};var v=(i,t,e)=>(y(i,t,"read from private field"),e?e.call(i):t.get(i)),M=(i,t,e)=>{if(t.has(i))throw TypeError("Cannot add the same private member more than once");t instanceof WeakSet?t.add(i):t.set(i,e)},D=(i,t,e,r)=>(y(i,t,"write to private field"),r?r.call(i,e):t.set(i,e),e);import{by as W,s as h,bz as P,bA as E,bB as F,bC as j}from"./index-a04bc7c5.js";const k=async(i,t=25,e=7,r=360,a=120,l)=>{let d=new AudioContext,o=document.createElement("canvas");o.width=r,o.height=a;const n=await d.decodeAudioData(await i.arrayBuffer());let u=(o.width-(t-1)*e)/t,x=o.height/2,R=l?Math.max(0,n.length-d.sampleRate*l):0,C=Math.floor((n.length-R)/t),B=n.getChannelData(0),m=o.getContext("2d");m.fillStyle="#000";for(let c=0;c<t;c++){let g=1,b=.001;for(let A=0;A<C;A++){let w=B[R+c*C+A]*15;w<g&&(g=w),w>b&&(b=w)}let p=Math.min(Math.max(6,(b-g)*x),o.height),L=x-p/2;m.beginPath(),m.roundRect(c*(u+e),L,u,p,5),m.fill()}const S=await new Promise(c=>o.toBlob(c)),N=o.toDataURL();return o.remove(),{blob:S,base64:N}},I=async(i,t=.01)=>{const e=new AudioContext,r=await i.arrayBuffer(),d=(await e.decodeAudioData(r)).getChannelData(0).every(o=>Math.abs(o)<t);return e.close(),d};var f;class Q{constructor(){M(this,f,void 0);s(this,"audioCtx");s(this,"recorder");s(this,"destination");s(this,"chunks");s(this,"stream");s(this,"start",async(t,e)=>{if(!this.stream){const a=await W("AudioRecorder");if(!a)return h("error","AudioRecorder: Failed to get audio stream");this.stream=a}this.recorder&&(this.recorder.state!=="inactive"&&this.recorder.stop(),this.recorder=null,this.chunks=[]),this.audioCtx&&this.audioCtx.close(),this.audioCtx=new AudioContext,this.destination=new MediaStreamAudioDestinationNode(this.audioCtx),this.audioCtx.createMediaStreamSource(this.stream).connect(this.destination),P("AudioRecorder",this.audioCtx,this.destination),this.recorder=new MediaRecorder(this.destination.stream),this.recorder.ondataavailable=a=>{this.chunks.push(a.data),e&&e(this.chunks)},D(this,f,Date.now()),this.chunks=[],this.recorder.start(t)});s(this,"stop",async()=>{const{recorder:t,chunks:e}=this;if(E("AudioRecorder"),this.audioCtx&&this.audioCtx.close(),F("AudioRecorder"),!t)return h("error","AudioRecorder: Failed to end recording: not recording"),!1;if(t.state==="inactive")return h("error","AudioRecorder: Failed to end recording: recorder is inactive"),!1;const r=await new Promise(u=>{t.onstop=()=>{u(Date.now()-v(this,f))},t.stop()}),a=await j(new Blob(e,{type:t.mimeType}),r,{logger:!1});if(this.stream=null,this.audioCtx=null,await I(a))return h("info","AudioRecorder: Audio is quiet, not sending message"),!1;const l=await k(a),d=await k(a,60,7,960,200),o=new Audio;o.src=URL.createObjectURL(a);let n={blob:a};return n.waveform={message:l.blob,placeholder:d.base64},o.duration!==1/0&&!isNaN(o.duration)?{...n,duration:Math.round(o.duration)}:(o.currentTime=Number.MAX_SAFE_INTEGER,await new Promise(u=>{h("info","AudioRecorder: Waiting for audio duration"),o.ontimeupdate=()=>{URL.revokeObjectURL(o.src),h("info","AudioRecorder: Got audio duration, revoked URL"),u({...n,duration:Math.round(o.duration)})}}))});this.recorder=null,this.chunks=[]}}f=new WeakMap;export{Q as A,k as g};
