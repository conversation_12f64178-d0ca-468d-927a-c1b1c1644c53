:root{--tooltip-margin: 30px;--tooltip-arrow-size: 6px}.tooltip-wrapper{display:inline-block;position:relative}.tooltip{position:absolute;border-radius:8px;left:50%;transform:translate(-50%);padding:.65rem 1rem;color:#fff;background:#000000;font-size:16px;font-family:Roboto,sans-serif;text-align:center;max-width:10rem;z-index:2}.tooltip>div{width:100%;height:100%}.tooltip:before{content:" ";left:50%;border:solid transparent;height:0;width:0;position:absolute;pointer-events:none;border-width:var(--tooltip-arrow-size);margin-left:calc(var(--tooltip-arrow-size) * -1)}.tooltip.top{top:calc(var(--tooltip-margin) * -1)}.tooltip.top:before{top:100%;border-top-color:#000}.tooltip.right{left:calc(100% + var(--tooltip-margin));top:50%;transform:translate(0) translateY(-50%)}.tooltip.right:before{left:calc(var(--tooltip-arrow-size) * -1);top:50%;transform:translate(0) translateY(-50%);border-right-color:#000}.tooltip.bottom{bottom:calc(var(--tooltip-margin) * -1)}.tooltip.bottom:before{bottom:100%;border-bottom-color:#000}.tooltip.left{left:auto;right:calc(100% + var(--tooltip-margin));top:50%;transform:translate(0) translateY(-50%)}.tooltip.left:before{left:auto;right:calc(var(--tooltip-arrow-size) * -2);top:50%;transform:translate(0) translateY(-50%);border-left-color:#000}:root{--phone-color-primary: rgb(255, 255, 255);--phone-color-opacity: rgba(242, 242, 242, .4);--phone-color-opacity2: rgb(30, 30, 30, .5);--phone-color-highlight: rgb(250, 250, 250);--phone-color-highlight2: rgb(240, 240, 240);--phone-color-highlight3: rgb(220, 220, 220);--phone-highlight-opacity15: rgba(145, 145, 145, .15);--phone-highlight-opacity35: rgba(145, 145, 145, .35);--phone-highlight-opacity45: rgba(145, 145, 145, .45);--phone-highlight-opacity55: rgba(145, 145, 145, .55);--phone-color-input: rgba(241, 241, 241, .656);--phone-text-primary: rgb(0, 0, 0);--phone-text-secondary: rgb(142, 142, 147);--phone-color-hover: rgb(240, 240, 240);--phone-color-border: rgba(200, 200, 200, .4);--phone-color-grey: #8e8e93;--phone-color-blue: #0a84ff;--phone-color-green: #32d74b;--phone-color-green-secondary: #092911;--phone-color-red: #ff3b30;--phone-color-orange: rgb(255, 157, 10);--phone-color-yellow: #cca250;--phone-color-pink: #ff3b30;--instagram-primary: #ffffff;--instagram-blue: rgb(0, 149, 246);--instagram-red: rgb(237, 73, 86);--instagram-primary-text: rgb(38, 38, 38);--instagram-secondary-text: rgb(142, 142, 142);--instagram-stroke: rgb(219, 219, 219);--instagram-border: rgb(219, 219, 219);--instagram-highlight: rgb(239, 239, 239);--tinder-color-pink: #ff4573;--tinder-color-orange: #ff5f65;--tinder-color-mix: #f5547c;--twitter-primary: #f5f8fa;--twitter-secondary: #14171a;--twitter-background-highlight: rgb(239, 243, 244);--twitter-primary-text: #14171a;--twitter-secondary-text: #657786;--twitter-alt-text: #657786;--twitter-border: #bdc5cd75;--twitter-border-secondary: #1d9bf0;--twitter-highlight: #1d9bf0;--twitter-hover: rgba(15, 20, 25, .1);--twitter-action: #14171a;--twitter-blue: #1d9bf0;--tiktok-primary: #ffffff;--tiktok-secondary: #000000;--tiktok-text-primary: #000000;--tiktok-text-secondary: #86878b;--tiktok-color-border: #d0d1d3;--tiktok-color-pink: #fe2c55;--tiktok-color-aqua: #00f2ea;--tiktok-color-yellow: #f8cd14;--tiktok-color-blue: #479fc5;--tiktok-color-unread: rgba(254, 44, 86, .2);--crypto-color-primary: rgb(255, 255, 255);--browser-primary: rgb(245, 245, 245);--browser-secondary: rgba(153, 153, 153, .15);--browser-gradient: linear-gradient(230deg, #f4d6ff, #c5f1ff);--browser-footer: rgba(255, 255, 255, .75);--browser-border: rgba(102, 102, 102, .75);--browser-text-secondary: #696969;--controlcentre-opacity: rgba(255, 255, 255, .15);--controlcentre-opacity2: rgba(255, 255, 255, .2);--controlcentre-active: rgba(255, 255, 255, .5);--notification-primary: rgba(215, 215, 215, .5);--notification-secondary: rgba(215, 215, 215, .1);--lockscreeneditor-background: rgba(255, 255, 255, .75);--lockscreeneditor-secondary: #d9d9d9;--app-bg: #ececec;--app-bg2: #ffffff;--app-secondary: #ffffff;--app-secondary2: #ececec;--app-highlight: #cccccc;--app-highlight2: #999999;--app-highlight3: #ffffff;--app-border: #666666;--app-slider: #cccccc;--app-slider-active: #333333;--app-button: #ffffff;--components-bg: #eeeeee;--components-secondary: #ffffff;--components-highlight: #cccccc}[data-theme=dark]{--phone-color-primary: #000000;--phone-color-opacity: rgb(30, 30, 30, .5);--phone-color-opacity2: rgba(242, 242, 242, .4);--phone-color-highlight: rgb(15, 15, 15);--phone-color-highlight2: rgb(20, 20, 20);--phone-color-highlight3: rgb(25, 25, 25);--phone-highlight-opacity35: rgba(145, 145, 145, .35);--phone-highlight-opacity45: rgba(50, 50, 50, .6);--phone-highlight-opacity55: rgb(60, 60, 60, .8);--phone-color-input: rgba(60, 60, 67, .6);--phone-text-primary: #f2f2f7;--phone-text-secondary: #6f6f6f;--phone-color-grey: #636366;--phone-color-hover: rgb(30, 30, 30);--phone-color-border: rgba(150, 150, 150, .2);--phone-color-blue: #076bcf;--instagram-primary: #000000;--instagram-blue: rgb(0, 149, 246);--instagram-red: rgb(237, 73, 86);--instagram-primary-text: rgb(250, 250, 250);--instagram-secondary-text: rgb(142, 142, 142);--instagram-stroke: rgb(219, 219, 219);--instagram-border: rgb(54, 54, 54);--instagram-highlight: rgb(38, 38, 38);--twitter-primary: #000000;--twitter-secondary: #f5f8fa;--twitter-background-highlight: rgb(20, 20, 20);--twitter-primary-text: #f5f8fa;--twitter-secondary-text: #aab8c2;--twitter-alt-text: #657786;--twitter-border: #38444d;--twitter-border-secondary: #38444d;--twitter-hover: rgba(150, 150, 150, .1);--twitter-highlight: #dcdcdc;--twitter-action: #1d9bf0;--twitter-blue: #1d9bf0;--crypto-color-primary: rgb(24, 26, 32);--tiktok-text-primary: #f2f2f7;--tiktok-text-secondary: #6f6f6f;--tiktok-color-border: #96969633;--browser-primary: rgb(15, 15, 15);--browser-secondary: rgba(153, 153, 153, .15);--browser-gradient: linear-gradient(230deg, #453b48, #2f393d);--browser-footer: rgba(51, 51, 51, .75);--browser-border: rgba(102, 102, 102, .75);--browser-text-secondary: #999999;--controlcentre-opacity: rgba(0, 0, 0, .15);--controlcentre-opacity2: rgba(0, 0, 0, .2);--controlcentre-active: rgba(0, 0, 0, .5);--notification-primary: rgba(0, 0, 0, .1);--notification-secondary: rgba(0, 0, 0, .12);--lockscreeneditor-background: rgba(0, 0, 0, .8);--lockscreeneditor-secondary: #333333;--app-bg: #000000;--app-bg2: #000000;--app-secondary: #141414;--app-secondary2: #141414;--app-highlight: #cccccc;--app-highlight2: #696969;--app-highlight3: #212121;--app-border: #cccccc;--app-slider: #999999;--app-slider-active: #ffffff;--app-button: #333333;--components-bg: #000000;--components-secondary: #141414;--components-highlight: #696969}.crypto-container{height:100%;max-height:100%;width:100%;background-color:var(--app-bg);display:flex;flex-direction:column;align-items:center;gap:1rem;font-family:Inter,sans-serif}.crypto-container .crypto-header{display:flex;flex-direction:row;align-items:center;gap:.75rem;width:88%;margin-top:5rem;position:relative;color:var(--phone-text-primary);font-size:20px;font-weight:600}.crypto-container .crypto-header img{aspect-ratio:1;width:3rem;border-radius:50%;margin-bottom:.2rem}.crypto-container .crypto-wrapper{display:flex;flex-direction:column;align-items:center;gap:1rem;width:100%;height:100%;overflow:auto;padding-bottom:5rem}.crypto-container .crypto-wrapper::-webkit-scrollbar{display:none}.crypto-container .search{width:90%!important;height:2rem}.crypto-container .search .searchbox{padding:.5rem 1rem;border-radius:12px;background-color:var(--app-secondary)}.crypto-container .balance{width:90%;display:flex;flex-direction:column;justify-content:inherit;gap:2.5rem;box-sizing:border-box;padding:1.25rem 1.5rem;border-radius:22px;background-position:center;background-size:stretch;background-image:url(/ui/dist/assets/img/cryptogradient.png);margin:1rem 0}.crypto-container .balance .title{display:flex;align-items:center;gap:.5rem;font-size:18px;font-weight:600;color:#fff}.crypto-container .balance .title svg{font-size:22px;cursor:pointer}.crypto-container .balance .amount{font-family:Crypto,sans-serif;font-size:50px;font-weight:700;font-variant-numeric:tabular-nums;color:#fff;cursor:pointer}.crypto-container .holdings{display:flex;flex-direction:column;gap:1rem;width:100%}.crypto-container .holdings .title{margin-left:10%;display:flex;flex-direction:row;align-items:center;justify-content:space-between;width:80%}.crypto-container .holdings .title .text{font-size:22px;font-weight:600;color:var(--phone-text-primary)}.crypto-container .holdings .title .action{font-size:18px;font-weight:500;color:#fff;text-decoration:underline;cursor:pointer}.crypto-container .holdings .items{display:flex;flex-direction:column}.crypto-container .holdings .items .item{display:flex;flex-direction:column;align-items:center;gap:1.5rem;padding:1rem 8%;cursor:pointer;transition:all .2s ease-in-out}.crypto-container .holdings .items .item.filters{display:flex;flex-direction:row;align-items:center}.crypto-container .holdings .items .item.filters :nth-child(2){margin-left:5rem}.crypto-container .holdings .items .item.filters :nth-child(3){margin-left:.5rem}.crypto-container .holdings .items .item.filters .filter{display:flex;flex-direction:row;align-items:center;gap:.2rem;cursor:pointer;color:var(--phone-text-primary);opacity:.9;font-weight:500}.crypto-container .holdings .items .item.filters .filter svg{font-size:18px}.crypto-container .holdings .items .item:hover:not(.filters){background-color:var(--phone-color-hover)}.crypto-container .holdings .items .item .top{display:flex;flex-direction:row;align-items:center;justify-content:space-between;width:100%}.crypto-container .holdings .items .item .top canvas{height:3.5rem}.crypto-container .holdings .items .item .bottom{display:flex;flex-direction:row;align-items:center;gap:1rem;width:100%;box-sizing:border-box}.crypto-container .holdings .items .item .bottom .button{box-sizing:border-box;display:flex;align-items:center;justify-content:center;width:100%;height:2.5rem;color:var(--phone-text-secondary);font-size:16px;font-weight:500;border:2px solid var(--phone-text-secondary);border-radius:16px;cursor:pointer;transition:all .2s ease-in-out}.crypto-container .holdings .items .item .bottom .button.fill{background-color:#15d190;border:none;color:#fff}.crypto-container .holdings .items .item .bottom .button.blue{border:2px solid var(--phone-color-blue);color:var(--phone-color-blue)}.crypto-container .holdings .items .item .bottom .button.red{border:2px solid var(--phone-color-red);color:var(--phone-color-red)}.crypto-container .holdings .items .item .bottom .button:hover{filter:brightness(.7)}.crypto-container .holdings .items .item .info{display:flex;flex-direction:row;align-items:center;gap:1rem}.crypto-container .holdings .items .item .info .icon{width:2.75rem;height:2.75rem;border-radius:50%}.crypto-container .holdings .items .item .info .coin .name{font-size:18px;font-weight:600;color:var(--phone-text-primary)}.crypto-container .holdings .items .item .info .coin .symbol{font-size:15px;font-weight:400;color:var(--phone-text-secondary)}.crypto-container .holdings .items .item .chart{width:5rem}.crypto-container .holdings .items .item .amount{display:flex;flex-direction:column;align-items:flex-end}.crypto-container .holdings .items .item .amount .value{font-size:18px;font-weight:600;color:var(--phone-text-primary)}.crypto-container .holdings .items .item .amount .asset{font-size:14px;font-weight:400;color:var(--phone-text-secondary)}.crypto-container .credits{margin-top:1rem;font-size:"Roboto",sans-serif;font-weight:400;font-size:14px;color:var(--phone-text-secondary)}
