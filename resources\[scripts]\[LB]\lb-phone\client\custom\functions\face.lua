local obstructingHelmets = {
    female = {
        [16] = true,
        [17] = true,
        [18] = true,
        [19] = true,
        [37] = true,
        [46] = true,
        [47] = true,
        [48] = true,
        [49] = true,
        [50] = true,
        [51] = true,
        [52] = true,
        [59] = true,
        [62] = true,
        [66] = true,
        [67] = true,
        [68] = true,
        [69] = true,
        [70] = true,
        [71] = true,
        [72] = true,
        [73] = true,
        [77] = true,
        [78] = true,
        [79] = true,
        [80] = true,
        [81] = true,
        [90] = true,
        [91] = true,
        [110] = true,
        [114] = true,
        [115] = true,
        [116] = true,
        [117] = true,
        [118] = true,
        [122] = true,
        [123] = true,
        [124] = true,
        [126] = true,
        [127] = true,
        [128] = true,
        [132] = true,
        [133] = true,
        [143] = true,
    },
    male = {
        [16] = true,
        [17] = true,
        [18] = true,
        [19] = true,
        [38] = true,
        [47] = true,
        [48] = true,
        [49] = true,
        [50] = true,
        [51] = true,
        [52] = true,
        [53] = true,
        [57] = true,
        [62] = true,
        [67] = true,
        [68] = true,
        [69] = true,
        [70] = true,
        [71] = true,
        [72] = true,
        [73] = true,
        [74] = true,
        [78] = true,
        [79] = true,
        [80] = true,
        [81] = true,
        [82] = true,
        [91] = true,
        [92] = true,
        [111] = true,
        [115] = true,
        [116] = true,
        [117] = true,
        [118] = true,
        [119] = true,
        [123] = true,
        [124] = true,
        [125] = true,
        [126] = true,
        [127] = true,
        [128] = true,
        [129] = true,
        [133] = true,
        [134] = true,
        [144] = true,
    }
}

---Check if the player is wearing a helmet or mask that would obstruct the face id
---@return boolean
function IsFaceObstructed()
    if GetPedDrawableVariation(PlayerPedId(), 1) > 0 then
        return true
    end

    local gender = GetEntityModel(PlayerPedId()) == `mp_m_freemode_01` and "male" or "female"
    return obstructingHelmets[gender][GetPedPropIndex(PlayerPedId(), 0)] == true
end