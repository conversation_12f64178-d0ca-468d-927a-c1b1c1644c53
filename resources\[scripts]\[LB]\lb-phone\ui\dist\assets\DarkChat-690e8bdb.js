import{u as v,r as N,G as U,t as o,aa as w,s as d,q as D,j as i,m as M,S as k,a as t,L as e,ax as q,C as g,cB as B,b,an as J,ao as z,x as Q,a1 as Z,c2 as ee,ak as te,y as ne,A as j,P as y,K as ae,a0 as X,I as se,ah as re,V as ie,ab as oe,o as F,d as K,cw as ce,a7 as le,v as W,J as x}from"./index-a04bc7c5.js";const _={username:"lb",channels:[{id:1,name:"guns",members:7,lastMessage:"i have a draco, 2k",sender:"kevin",timestamp:Date.now()-1e3*60*60*4},{id:2,name:"comics",members:134,lastMessage:"whats up, i found this really great comic the other day",sender:"anonymous943",timestamp:Date.now()-1e3*60*60*8},{id:3,name:"cars",members:23,lastMessage:"i have a 2019 mustang for sale",sender:"pirateking32",timestamp:Date.now()-1e3*60*60*12}],messages:{guns:[{content:"i have a draco, 2k",sender:"kevin",timestamp:Date.now()-1e3*60*60*4},{content:"yo, does anyone have any weapons for sale?",sender:"rya2n",timestamp:Date.now()-1e3*60*60*6},{content:"sup",sender:"lb",timestamp:Date.now()-1e3*60*60*17}],comics:[{content:"whats up, i found this really great comic the other day",sender:"anonymous943",timestamp:Date.now()-1e3*60*60*8},{content:"any good cominc recommendations?",sender:"yungblood",timestamp:Date.now()-1e3*60*60*10},{content:"just finished reading the new batman comic, its amazing",sender:"batmanfan123",timestamp:Date.now()-1e3*60*60*24}],cars:[{content:"i have a 2019 mustang for sale",sender:"pirateking32",timestamp:Date.now()-1e3*60*60*12},{content:"anyone know where i can get a good deal on a car?",sender:"anonymous",timestamp:Date.now()-1e3*60*60*14},{content:"i have a 2018 camaro for sale, 15k",sender:"camaroguy",timestamp:Date.now()-1e3*60*60*18},{content:"i have a 2017 mustang for sale, 10k",sender:"mustangguy",timestamp:Date.now()-1e3*60*60*20},{content:"looking for a quite new car in good condition for around 10k, anyone know where i can find one?",sender:"lb",timestamp:Date.now()-1e3*60*60*22}]}};function Ae(){var L,H;const a=v(u),l=v(p),h=v(j),[r,A]=N.useState(""),[P,m]=N.useState(""),T=v(y.Settings),E=v(o.APPS.DARKCHAT.channels);N.useEffect(()=>{if(U("DarkChat")&&!o.APPS.DARKCHAT.channels.value){if(!w())return d("warning","No service, not fetching channels");D("DarkChat",{action:"getChannels"},_.channels).then(c=>{if(!c)return d("warning","Failed to fetch channels");o.APPS.DARKCHAT.channels.set(c)})}},[h==null?void 0:h.active,l]);const I=()=>{D("AccountSwitcher",{action:"getAccounts",app:"DarkChat"},null).then(c=>{var n;if(!c)return d("info","No accounts found");let S=(n=c==null?void 0:c.filter(s=>s!==l))==null?void 0:n.map(s=>({title:s,cb:()=>{D("AccountSwitcher",{action:"switch",app:"DarkChat",account:s}).then(()=>{p.set(s),o.APPS.DARKCHAT.username.set(s),o.APPS.DARKCHAT.channels.reset(),u.set("channels")})}}));g.ContextMenu.set({buttons:[{title:l},...S,{title:e("APPS.DARKCHAT.CHANGE_ACCOUNT"),cb:()=>{u.set("signup"),p.reset(),o.APPS.DARKCHAT.username.reset(),o.APPS.DARKCHAT.channels.reset()}},{title:e("APPS.MAIL.SIGN_OUT_POPUP.TITLE"),color:"red",cb:()=>{g.PopUp.set({title:e("APPS.MAIL.SIGN_OUT_POPUP.TITLE"),description:e("APPS.MAIL.SIGN_OUT_POPUP.DESCRIPTION"),buttons:[{title:e("APPS.MAIL.SIGN_OUT_POPUP.CANCEL")},{title:e("APPS.MAIL.SIGN_OUT_POPUP.PROCEED"),color:"red",cb:()=>{D("DarkChat",{action:"logout"},!0).then(s=>{if(!s)return d("warning","Failed to logout, success returned false");p.reset(),o.APPS.DARKCHAT.username.reset(),o.APPS.DARKCHAT.channels.reset(),u.set("signin")})}}]})}}]})})};return i(M.div,{...k("left",a,.2),className:"animation-container",children:[i("div",{className:"darkchat-header",children:[i("div",{className:"top",children:[t("div",{className:"title",children:e("APPS.DARKCHAT.TITLE")}),i("div",{className:"buttons",children:[t(q,{onClick:()=>{l&&g.PopUp.set({title:e("APPS.DARKCHAT.NEW_ROOM"),description:e("APPS.DARKCHAT.NEW_ROOM_TEXT"),input:{placeholder:e("APPS.DARKCHAT.ROOM_CODE"),type:T!=null&&T.streamerMode?"password":"text",minCharacters:3,onChange:c=>m(c)},buttons:[{title:e("APPS.DARKCHAT.CANCEL")},{title:e("APPS.DARKCHAT.JOIN"),color:"blue",cb:()=>{if(!w()){d("warning","No service, not joining channel"),setTimeout(()=>{g.PopUp.set({title:e("MISC.AIRPLANE_MODE_POPUP.TITLE"),description:e("MISC.AIRPLANE_MODE_POPUP.DESCRIPTION"),buttons:[{title:e("MISC.AIRPLANE_MODE_POPUP.OK")}]})},250);return}m(c=>(D("DarkChat",{action:"joinChannel",channel:c},!_.channels.find(S=>S.name===c)&&{timestamp:Date.now(),name:c,members:Math.floor(Math.random()*100),lastMessage:"Welcome to the channel!",sender:"admin"}).then(S=>{if(!S)return d("warning","Failed to join channel");O.set(S),u.set("chat"),o.APPS.DARKCHAT.channels.set([S,...E])}),c))}}]})}}),t(B,{className:"elipse",onClick:()=>{var c,S,n,s;g.ContextMenu.set({buttons:[{title:e("APPS.DARKCHAT.CHANGE_ACCOUNT"),cb:I},((S=(c=b)==null?void 0:c.value)==null?void 0:S.ChangePassword.DarkChat)&&{title:e("APPS.DARKCHAT.CHANGE_PASSWORD"),cb:()=>{setTimeout(()=>{J("DarkChat",()=>{})},250)}},((s=(n=b)==null?void 0:n.value)==null?void 0:s.DeleteAccount.DarkChat)&&{title:e("APPS.DARKCHAT.DELETE_ACCOUNT"),color:"red",cb:()=>{setTimeout(()=>{z("DarkChat",()=>{p.reset(),o.APPS.DARKCHAT.username.set(null),o.APPS.DARKCHAT.channels.set(null),u.set("signin")})},250)}}]})}})]})]}),t(Q,{placeholder:e("APPS.DARKCHAT.SEARCH"),theme:"dark",onChange:c=>A(c.target.value)})]}),(E==null?void 0:E.length)===0&&i("div",{className:"no-channels",children:[t("div",{className:"title",children:e("APPS.DARKCHAT.NO_CHATS")}),t("div",{className:"description",children:e("APPS.DARKCHAT.NO_CHATS_TEXT")})]}),t("div",{className:"channels-body",children:(H=(L=E==null?void 0:E.sort((c,S)=>S.timestamp-c.timestamp))==null?void 0:L.filter(c=>{var S;return(S=c.name)==null?void 0:S.toLowerCase().includes(r==null?void 0:r.toLowerCase())}))==null?void 0:H.map(c=>t(Pe,{channel:c},c.id))})]})}const Pe=({channel:a})=>{const l=v(p),h=v(y.Settings),r=P=>/<!SENT-LOCATION-X=(-?\d*\.?\d*)Y=(-?\d*\.?\d*)!>/.test(P),A=Z(P=>{g.ContextMenu.set({buttons:[{title:e("APPS.DARKCHAT.LEAVE_CHANNEL.TITLE"),color:"red",cb:()=>{g.PopUp.set({title:e("APPS.DARKCHAT.LEAVE_CHANNEL.TITLE"),description:e("APPS.DARKCHAT.LEAVE_CHANNEL.DESCRIPTION"),buttons:[{title:e("APPS.DARKCHAT.LEAVE_CHANNEL.CANCEL")},{title:e("APPS.DARKCHAT.LEAVE_CHANNEL.PROCEED"),cb:()=>{D("DarkChat",{action:"leaveChannel",channel:a.name},!0).then(m=>{if(!m)return d("warning","Failed to leave channel");u.set("channels"),O.set(null);let T=o.APPS.DARKCHAT.channels.value.filter(E=>E.name!==a.name);o.APPS.DARKCHAT.channels.set(T)})}}]})}}]})});return i("div",{...A,className:"channel",onClick:()=>{l&&(O.set(a),u.set("chat"))},children:[i("div",{className:"channel-info",children:[i("div",{className:"members",children:[t(ee,{}),t("span",{children:a.members})]}),i("div",{className:"channel-content",children:[t("div",{className:"channel-name",children:h.streamerMode?"*****":a.name}),a.lastMessage&&i("div",{className:"recent-message",children:[i("b",{children:[a.sender,":"]})," ",r(a.lastMessage)?e("APPS.MESSAGES.SENT_LOCATION_SHORT"):a.lastMessage]})]})]}),i("div",{className:"right",children:[t("div",{className:"timestamp",children:te(a.timestamp)}),t(ne,{})]})]})};function de(){const a=v(y.Settings),l=v(O),h=v(p),r=v(u),[A,P]=N.useState([]),[m,T]=N.useState(""),E=N.useRef(null),I=N.useRef(0);N.useEffect(()=>{var n;if(U("DarkChat")){if(!w())return d("warning","No service, not fetching messages");D("DarkChat",{action:"getMessages",channel:l.name,page:0},(n=_.messages)==null?void 0:n[l.name]).then(s=>{if(!s)return d("error","Failed to fetch messages");P(s.reverse())})}},[]);const{handleScroll:L}=ae({fetchData:n=>D("DarkChat",{action:"getMessages",channel:l.name,page:n}),onDataFetched:n=>{let s=document.querySelector(".chat-wrapper");I.current=s.scrollHeight,P([...n.reverse(),...A])},isReversed:!0,perPage:15});N.useEffect(()=>{let n=document.querySelector(".chat-wrapper");const s=n.scrollHeight;n.scrollTop+=s-I.current,n.scroll},[A]);const H=()=>{if(!w()){g.PopUp.set({title:e("MISC.AIRPLANE_MODE_POPUP.TITLE"),description:e("MISC.AIRPLANE_MODE_POPUP.DESCRIPTION"),buttons:[{title:e("MISC.AIRPLANE_MODE_POPUP.OK")}]}),d("warning","No service, not sending location");return}if(m.length>0){let n={sender:h,content:m,timestamp:new Date().getTime()};D("DarkChat",{action:"sendMessage",content:m,channel:l.name},!0).then(()=>{T(""),E.current.value="",P([...A,n]);let s=o.APPS.DARKCHAT.channels.value.map(C=>(C.name===l.name&&(C.lastMessage=n.content,C.sender=h,C.timestamp=new Date().getTime()),C));o.APPS.DARKCHAT.channels.set(s)})}},c=()=>{g.PopUp.set({title:e("APPS.MESSAGES.SEND_LOCATION_POPUP.TITLE"),description:e("APPS.MESSAGES.SEND_LOCATION_POPUP.TEXT"),buttons:[{title:e("APPS.MESSAGES.SEND_LOCATION_POPUP.CANCEL")},{title:e("APPS.MESSAGES.SEND_LOCATION_POPUP.SEND"),cb:()=>{if(!w()){g.PopUp.set({title:e("MISC.AIRPLANE_MODE_POPUP.TITLE"),description:e("MISC.AIRPLANE_MODE_POPUP.DESCRIPTION"),buttons:[{title:e("MISC.AIRPLANE_MODE_POPUP.OK")}]}),d("warning","No service, not sending location");return}D("Maps",{action:"getCurrentLocation"},{x:"0",y:"0"}).then(n=>{if(!n)return;let s={content:`<!SENT-LOCATION-X=${W(n.x,2)}Y=${W(n.y,2)}!>`,sender:h,timestamp:new Date().getTime()};D("DarkChat",{action:"sendMessage",...s,channel:l.name},!0).then(C=>{if(C){P(R=>[...R,s]),T(""),E.current.value="";let f=o.APPS.DARKCHAT.channels.value.map(R=>(R.name===l.name&&(R.lastMessage=s.content,R.sender=h,R.timestamp=new Date().getTime()),R));o.APPS.DARKCHAT.channels.set(f)}else P(f=>[...f,{...s,delivered:!1}])})})}}]})};X("darkChat:newMessage",n=>{if(l.name!==n.channel||!U("DarkChat"))return;let s=o.APPS.DARKCHAT.channels.value.map(C=>(C.name===n.channel&&(C.lastMessage=n.content,C.sender=n.sender,C.timestamp=new Date().getTime()),C));o.APPS.DARKCHAT.channels.set(s),P([...A,{...n,timestamp:new Date().getTime()}])},{waitUntilService:!0});const S=n=>{if(n)return/<!SENT-LOCATION-X=(-?\d*\.?\d*)Y=(-?\d*\.?\d*)!>/.test(n)};return i(M.div,{...k("right",r,.2),className:"animation-container",children:[t("div",{className:"darkchat-header chat",children:i("div",{className:"top",children:[t(se,{onClick:()=>{u.set("channels"),O.set(null)}}),i("div",{className:"content",children:[t("div",{className:"title",children:a.streamerMode?"*****":l.name}),i("div",{className:"members",children:[l.members," ",l.members===1?e("APPS.DARKCHAT.MEMBER"):e("APPS.DARKCHAT.MEMBERS")]})]}),t(re,{onClick:()=>{g.PopUp.set({title:e("APPS.DARKCHAT.LEAVE_ROOM"),description:e("APPS.DARKCHAT.LEAVE_ROOM_TEXT"),buttons:[{title:e("APPS.DARKCHAT.CANCEL")},{title:e("APPS.DARKCHAT.LEAVE"),color:"red",cb:()=>{D("DarkChat",{action:"leaveChannel",channel:l.name},!0).then(n=>{if(!n)return d("warning","Failed to leave channel");u.set("channels"),O.set(null);let s=o.APPS.DARKCHAT.channels.value.filter(C=>C.name!==l.name);o.APPS.DARKCHAT.channels.set(s)})}}]})}})]})}),t("div",{className:"chat-wrapper",onScroll:L,children:t("div",{className:"chat-body",children:A.map((n,s)=>{var V;let C,f=null,R=n.sender===h?"self":"other",G=((V=A[s+1])==null?void 0:V.sender)===h?"self":"other";if(A[s+1]?C=Math.abs(n.timestamp-A[s+1].timestamp)/36e5:G=void 0,S(n.content)){let Y=n.content.match(/X=(-?\d*\.?\d*)Y/)[1],$=n.content.match(/Y=(-?\d*\.?\d*)!>/)[1];f={x:Y,y:$}}return i("div",{className:ie("message",R),children:[f?t("div",{className:"location",onClick:()=>{j.patch({active:{name:"Maps",data:{location:[f.y,f.x],name:`${n.sender}'s location`}}})},children:t("div",{className:"img",style:{backgroundImage:'url("https://img.gta5-mods.com/q95/images/mirror-park-garden/2b72f9-20160428154103_1.jpg")'}})}):t("div",{className:"content",children:oe(n.content)}),A[s+1]&&C>6?i("div",{className:"date",children:[R=="other"&&t("div",{className:"user",children:n.sender}),F(n.timestamp)]}):R!==G&&i("div",{className:"date",children:[R=="other"&&t("div",{className:"user",children:n.sender}),F(n.timestamp)]})]},s)})})}),t("div",{className:"chat-bottom",children:i("div",{className:"input",children:[t(K,{type:"text",placeholder:e("APPS.DARKCHAT.INPUT_PLACEHOLDER"),value:m,ref:E,onChange:n=>T(n.target.value),onKeyDown:n=>{if(n.key==="Enter")return H()}}),i("div",{className:"buttons",children:[t("div",{className:"location",children:t(ce,{onClick:c})}),m.length>0&&t("div",{className:"send",onClick:H,children:t(le,{})})]})]})})]})}function me(){const[a,l]=N.useState({username:"",password:""}),h=()=>{D("DarkChat",{action:"login",username:a.username,password:a.password},{success:!0}).then(r=>{r&&r.success?(p.set(a.username),o.APPS.DARKCHAT.username.set(a.username),u.set("channels")):r&&r.reason&&(d("error",r.reason),g.PopUp.set({title:e("APPS.DARKCHAT.ERROR"),description:e(`APPS.DARKCHAT.${r.reason.toUpperCase()}`),buttons:[{title:e("APPS.DARKCHAT.OK")}]}))})};return t(M.div,{...k("right","signin",.2),className:"animation-container",children:i("div",{className:"form",children:[t("div",{className:"form-header",children:t("div",{className:"title",children:e("APPS.DARKCHAT.LOGIN")})}),i("div",{className:"form-content",children:[i("div",{className:"item",children:[t("div",{className:"title",children:e("APPS.DARKCHAT.USERNAME")}),t("div",{className:"input",children:t(K,{type:"text",placeholder:"anonymous",maxLength:50,onChange:r=>{var A;l({...a,username:(A=r.target.value)==null?void 0:A.toLowerCase()})}})})]}),i("div",{className:"item",children:[t("div",{className:"title",children:e("APPS.DARKCHAT.PASSWORD")}),t("div",{className:"input",children:t(K,{type:"password",placeholder:"••••••••••",minLength:3,onChange:r=>{l({...a,password:r.target.value})}})})]}),t("div",{className:"button",onClick:()=>h(),children:e("APPS.DARKCHAT.LOGIN")})]}),i("div",{className:"footer",children:[e("APPS.DARKCHAT.NO_ACCOUNT")," ",t("span",{onClick:()=>u.set("signup"),children:e("APPS.DARKCHAT.SIGN_UP")})]})]})})}function he(){const[a,l]=N.useState({username:"",password:""}),h=()=>{var P;if(a.username===""||a.password==="")return d("warning","Username or password is empty");let r=(P=a==null?void 0:a.username)==null?void 0:P.toLowerCase().replace(/\s/g,""),A=r==null?void 0:r.match(b.value.UsernameFilter);if(!A||(A==null?void 0:A[0])!==r||r.length<3||r.length>15){d("warning","Username did not match regex"),g.PopUp.set({title:e("APPS.DARKCHAT.ERROR"),description:e("APPS.DARKCHAT.USERNAME_NOT_ALLOWED"),buttons:[{title:e("APPS.DARKCHAT.OK")}]});return}D("DarkChat",{action:"register",username:a.username,password:a.password},{success:!0}).then(m=>{if(!m)return d("warning","Darkchat response is null");if(m&&m.success)d("info","Account created successfully"),p.set(a.username),o.APPS.DARKCHAT.username.set(a.username),u.set("channels");else if(m&&m.reason){d("warning",m.reason),g.PopUp.set({title:e("APPS.DARKCHAT.ERROR"),description:e(`APPS.DARKCHAT.${m.reason.toUpperCase()}`),buttons:[{title:e("APPS.DARKCHAT.OK")}]});return}})};return t(M.div,{...k("left","signup",.2),className:"animation-container",children:i("div",{className:"form",children:[t("div",{className:"form-header",children:t("div",{className:"title",children:e("APPS.DARKCHAT.SIGN_UP")})}),i("div",{className:"form-content",children:[i("div",{className:"item",children:[t("div",{className:"title",children:e("APPS.DARKCHAT.USERNAME")}),t("div",{className:"input",children:t(K,{type:"text",placeholder:"anonymous",maxLength:50,onChange:r=>{var A;l({...a,username:(A=r.target.value)==null?void 0:A.toLowerCase()})}})})]}),i("div",{className:"item",children:[t("div",{className:"title",children:e("APPS.DARKCHAT.PASSWORD")}),t("div",{className:"input",children:t(K,{type:"password",placeholder:"••••••••••",minLength:3,onChange:r=>{l({...a,password:r.target.value})}})})]}),t("div",{className:"button",onClick:()=>h(),children:e("APPS.DARKCHAT.SIGN_UP")})]}),i("div",{className:"footer",children:[e("APPS.DARKCHAT.ALREADY_HAVE")," ",t("span",{onClick:()=>u.set("signin"),children:e("APPS.DARKCHAT.LOGIN")})]})]})})}const ue={channels:t(Ae,{}),chat:t(de,{}),signin:t(me,{}),signup:t(he,{})},u=x("signup"),O=x(null),p=x(null);function Se(){const a=v(u),[l,h]=N.useState(""),[r,A]=N.useState(!0);return N.useEffect(()=>{if(o.APPS.DARKCHAT.username.value){p.set(o.APPS.DARKCHAT.username.value),A(!1);return}D("DarkChat",{action:"getUsername"},_.username).then(P=>{if(A(!1),!P||!(P!=null&&P.username))return d("warning","Darkchat response is null");p.set(P.username),o.APPS.DARKCHAT.username.set(P.username),P.password?u.set("channels"):g.PopUp.set({title:e("APPS.DARKCHAT.SET_PASSWORD_POPUP.TITLE"),description:e("APPS.DARKCHAT.SET_PASSWORD_POPUP.DESCRIPTION"),input:{type:"password",placeholder:e("APPS.DARKCHAT.SET_PASSWORD_POPUP.PLACEHOLDER"),minLength:3,maxLength:50,onChange:m=>h(m)},buttons:[{title:e("APPS.DARKCHAT.SET_PASSWORD_POPUP.TITLE"),cb:()=>{h(m=>(D("DarkChat",{action:"setPassword",password:m},{success:!0}).then(T=>{if(!T||T!=null&&T.reason)return d("error",T.reason);u.set("channels")}),""))}}]})})},[]),X("darkchat:logout",P=>{if(!P)return d("warning","No username provided to logout");if(!p.value)return d("warning","No username stored in cache to logout");if(P!==(p==null?void 0:p.value))return d("warning","Username provided does not match current logged in username, not logging out");p.set(null),u.set("signin"),o.APPS.DARKCHAT.username.set(null),o.APPS.DARKCHAT.channels.set(null)}),t("div",{className:"darkchat-container",children:!r&&ue[a]})}export{O as ActiveChannel,p as Username,u as View,Se as default};
