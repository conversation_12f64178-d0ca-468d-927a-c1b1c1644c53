:root{--phone-color-primary: rgb(255, 255, 255);--phone-color-opacity: rgba(242, 242, 242, .4);--phone-color-opacity2: rgb(30, 30, 30, .5);--phone-color-highlight: rgb(250, 250, 250);--phone-color-highlight2: rgb(240, 240, 240);--phone-color-highlight3: rgb(220, 220, 220);--phone-highlight-opacity15: rgba(145, 145, 145, .15);--phone-highlight-opacity35: rgba(145, 145, 145, .35);--phone-highlight-opacity45: rgba(145, 145, 145, .45);--phone-highlight-opacity55: rgba(145, 145, 145, .55);--phone-color-input: rgba(241, 241, 241, .656);--phone-text-primary: rgb(0, 0, 0);--phone-text-secondary: rgb(142, 142, 147);--phone-color-hover: rgb(240, 240, 240);--phone-color-border: rgba(200, 200, 200, .4);--phone-color-grey: #8e8e93;--phone-color-blue: #0a84ff;--phone-color-green: #32d74b;--phone-color-green-secondary: #092911;--phone-color-red: #ff3b30;--phone-color-orange: rgb(255, 157, 10);--phone-color-yellow: #cca250;--phone-color-pink: #ff3b30;--instagram-primary: #ffffff;--instagram-blue: rgb(0, 149, 246);--instagram-red: rgb(237, 73, 86);--instagram-primary-text: rgb(38, 38, 38);--instagram-secondary-text: rgb(142, 142, 142);--instagram-stroke: rgb(219, 219, 219);--instagram-border: rgb(219, 219, 219);--instagram-highlight: rgb(239, 239, 239);--tinder-color-pink: #ff4573;--tinder-color-orange: #ff5f65;--tinder-color-mix: #f5547c;--twitter-primary: #f5f8fa;--twitter-secondary: #14171a;--twitter-background-highlight: rgb(239, 243, 244);--twitter-primary-text: #14171a;--twitter-secondary-text: #657786;--twitter-alt-text: #657786;--twitter-border: #bdc5cd75;--twitter-border-secondary: #1d9bf0;--twitter-highlight: #1d9bf0;--twitter-hover: rgba(15, 20, 25, .1);--twitter-action: #14171a;--twitter-blue: #1d9bf0;--tiktok-primary: #ffffff;--tiktok-secondary: #000000;--tiktok-text-primary: #000000;--tiktok-text-secondary: #86878b;--tiktok-color-border: #d0d1d3;--tiktok-color-pink: #fe2c55;--tiktok-color-aqua: #00f2ea;--tiktok-color-yellow: #f8cd14;--tiktok-color-blue: #479fc5;--tiktok-color-unread: rgba(254, 44, 86, .2);--crypto-color-primary: rgb(255, 255, 255);--browser-primary: rgb(245, 245, 245);--browser-secondary: rgba(153, 153, 153, .15);--browser-gradient: linear-gradient(230deg, #f4d6ff, #c5f1ff);--browser-footer: rgba(255, 255, 255, .75);--browser-border: rgba(102, 102, 102, .75);--browser-text-secondary: #696969;--controlcentre-opacity: rgba(255, 255, 255, .15);--controlcentre-opacity2: rgba(255, 255, 255, .2);--controlcentre-active: rgba(255, 255, 255, .5);--notification-primary: rgba(215, 215, 215, .5);--notification-secondary: rgba(215, 215, 215, .1);--lockscreeneditor-background: rgba(255, 255, 255, .75);--lockscreeneditor-secondary: #d9d9d9;--app-bg: #ececec;--app-bg2: #ffffff;--app-secondary: #ffffff;--app-secondary2: #ececec;--app-highlight: #cccccc;--app-highlight2: #999999;--app-highlight3: #ffffff;--app-border: #666666;--app-slider: #cccccc;--app-slider-active: #333333;--app-button: #ffffff;--components-bg: #eeeeee;--components-secondary: #ffffff;--components-highlight: #cccccc}[data-theme=dark]{--phone-color-primary: #000000;--phone-color-opacity: rgb(30, 30, 30, .5);--phone-color-opacity2: rgba(242, 242, 242, .4);--phone-color-highlight: rgb(15, 15, 15);--phone-color-highlight2: rgb(20, 20, 20);--phone-color-highlight3: rgb(25, 25, 25);--phone-highlight-opacity35: rgba(145, 145, 145, .35);--phone-highlight-opacity45: rgba(50, 50, 50, .6);--phone-highlight-opacity55: rgb(60, 60, 60, .8);--phone-color-input: rgba(60, 60, 67, .6);--phone-text-primary: #f2f2f7;--phone-text-secondary: #6f6f6f;--phone-color-grey: #636366;--phone-color-hover: rgb(30, 30, 30);--phone-color-border: rgba(150, 150, 150, .2);--phone-color-blue: #076bcf;--instagram-primary: #000000;--instagram-blue: rgb(0, 149, 246);--instagram-red: rgb(237, 73, 86);--instagram-primary-text: rgb(250, 250, 250);--instagram-secondary-text: rgb(142, 142, 142);--instagram-stroke: rgb(219, 219, 219);--instagram-border: rgb(54, 54, 54);--instagram-highlight: rgb(38, 38, 38);--twitter-primary: #000000;--twitter-secondary: #f5f8fa;--twitter-background-highlight: rgb(20, 20, 20);--twitter-primary-text: #f5f8fa;--twitter-secondary-text: #aab8c2;--twitter-alt-text: #657786;--twitter-border: #38444d;--twitter-border-secondary: #38444d;--twitter-hover: rgba(150, 150, 150, .1);--twitter-highlight: #dcdcdc;--twitter-action: #1d9bf0;--twitter-blue: #1d9bf0;--crypto-color-primary: rgb(24, 26, 32);--tiktok-text-primary: #f2f2f7;--tiktok-text-secondary: #6f6f6f;--tiktok-color-border: #96969633;--browser-primary: rgb(15, 15, 15);--browser-secondary: rgba(153, 153, 153, .15);--browser-gradient: linear-gradient(230deg, #453b48, #2f393d);--browser-footer: rgba(51, 51, 51, .75);--browser-border: rgba(102, 102, 102, .75);--browser-text-secondary: #999999;--controlcentre-opacity: rgba(0, 0, 0, .15);--controlcentre-opacity2: rgba(0, 0, 0, .2);--controlcentre-active: rgba(0, 0, 0, .5);--notification-primary: rgba(0, 0, 0, .1);--notification-secondary: rgba(0, 0, 0, .12);--lockscreeneditor-background: rgba(0, 0, 0, .8);--lockscreeneditor-secondary: #333333;--app-bg: #000000;--app-bg2: #000000;--app-secondary: #141414;--app-secondary2: #141414;--app-highlight: #cccccc;--app-highlight2: #696969;--app-highlight3: #212121;--app-border: #cccccc;--app-slider: #999999;--app-slider-active: #ffffff;--app-button: #333333;--components-bg: #000000;--components-secondary: #141414;--components-highlight: #696969}@keyframes zoomIn{0%{transform:scale(.5)}to{transform:scale(1)}}@keyframes zoomOut{0%{transform:scale(1.5)}to{transform:scale(1)}}@keyframes slideDown{0%{transform:translateY(-20%)}to{transform:translateY(0)}}@keyframes slideUp{0%{transform:translateY(40%)}to{transform:translateY(0)}}@keyframes slideRight{0%{transform:translate(-10%)}to{transform:translate(0)}}@keyframes slideLeft{0%{transform:translate(10%)}to{transform:translate(0)}}@keyframes appJiggle{0%{transform:rotate(-1deg);animation-timing-function:ease-in}50%{transform:rotate(1.5deg);animation-timing-function:ease-out}}@keyframes appJiggle2{0%{transform:rotate(1deg);animation-timing-function:ease-in}50%{transform:rotate(-1.5deg);animation-timing-function:ease-out}}@keyframes widgetJiggle{0%{transform:rotate(-.5deg);animation-timing-function:ease-in}50%{transform:rotate(.5deg);animation-timing-function:ease-out}}@keyframes widgetJiggle2{0%{transform:rotate(.5deg);animation-timing-function:ease-in}50%{transform:rotate(-.5deg);animation-timing-function:ease-out}}.wallet-app-container{display:flex;flex-direction:column;height:100%;width:100%;background-color:var(--app-bg)}.wallet-app-container .overview{display:flex;flex-direction:column;padding:4rem 1.75rem;gap:2rem;overflow-y:scroll;max-height:100%}.wallet-app-container .overview::-webkit-scrollbar{display:none}.wallet-app-container .overview .wallet-header{display:flex;flex-direction:column;gap:.5rem}.wallet-app-container .overview .wallet-header .title{color:var(--phone-text-primary);font-size:30px;font-weight:600;font-style:bold}.wallet-app-container .overview .wallet-header .navigation{display:flex;align-items:center;gap:.1rem;color:var(--phone-color-blue);font-size:18px;cursor:pointer}.wallet-app-container .overview .wallet-header .navigation svg{font-size:24px}.wallet-app-container .overview .wrapper{display:flex;flex-direction:column;align-items:center;gap:1.75rem}.wallet-app-container .overview .wrapper::-webkit-scrollbar{display:none}.wallet-app-container .overview .wrapper.center{justify-content:center;align-items:center;gap:2rem;height:85vh}.wallet-app-container .overview .wrapper .card{height:13.8rem;aspect-ratio:1.59/1;border-radius:15px;background-image:url(/ui/dist/assets/img/card.png);background-size:cover;background-position:center;background-repeat:no-repeat;filter:drop-shadow(0 0px 10px rgba(0,0,0,.2))}.wallet-app-container .overview .wrapper .subtitle{display:flex;align-items:center;justify-content:space-between;color:var(--phone-text-primary);margin-bottom:-1rem;width:95%}.wallet-app-container .overview .wrapper .subtitle .title{font-size:18px;font-weight:700}.wallet-app-container .overview .wrapper .subtitle .link{display:flex;align-items:center;gap:.1rem;font-size:16px;font-weight:500;color:var(--phone-color-blue);cursor:pointer}.wallet-app-container .overview .wrapper .subtitle .link svg{font-size:18px}.wallet-app-container .overview .wrapper .subtitle .value{font-size:16px;color:var(--phone-text-secondary)}.wallet-app-container .overview .wrapper section{display:flex;flex-direction:column;background-color:var(--app-secondary);border-radius:14px;padding:1rem;width:100%;box-sizing:border-box}.wallet-app-container .overview .wrapper section.input{padding:1.5rem}.wallet-app-container .overview .wrapper section .item{display:flex;flex-direction:column;gap:.4rem}.wallet-app-container .overview .wrapper section .item.space{display:flex;flex-direction:row;justify-content:space-between;align-items:center;width:100%}.wallet-app-container .overview .wrapper section .item .button{background-color:var(--phone-text-primary);color:var(--phone-color-primary);padding:.5rem 1.25rem;border-radius:20px;font-size:16px;cursor:pointer;transition:all .2s ease-in-out}.wallet-app-container .overview .wrapper section .item .button:hover{opacity:.8}.wallet-app-container .overview .wrapper section .item .balance{width:auto;display:flex;flex-direction:column;gap:.5rem;color:var(--phone-text-primary)}.wallet-app-container .overview .wrapper section .item .balance .title{font-size:17px;font-weight:500}.wallet-app-container .overview .wrapper section .item .balance .title.big{font-size:25px}.wallet-app-container .overview .wrapper section .item .balance .value{font-size:22px;font-weight:700}.wallet-app-container .overview .wrapper section .item .input{display:flex;flex-direction:row;align-items:center;gap:1rem;color:var(--phone-color-highlight);padding:.5rem .75rem;border-radius:5px}.wallet-app-container .overview .wrapper section .item .input input{border:none;background:transparent;font-size:15px}.wallet-app-container .overview .wrapper section .item .input input:active,.wallet-app-container .overview .wrapper section .item .input input:focus{outline:none}.wallet-app-container .overview .wrapper section .item .date{font-size:15px;font-weight:400}.wallet-app-container .overview .wrapper section .item .link{cursor:pointer;transition:all .2s ease-in-out;font-size:18px;font-weight:500;color:var(--phone-color-blue)}.wallet-app-container .overview .wrapper section .item .link:hover{transform:scale(1.02)}.wallet-app-container .overview .wrapper section .item.multi{display:flex;flex-direction:row;align-items:center;width:100%;padding:1rem 0;border-top:2px solid var(--phone-color-border)}.wallet-app-container .overview .wrapper section .item.multi:first-child{border:none;padding-top:0}.wallet-app-container .overview .wrapper section .item.multi:last-child{padding-bottom:0}.wallet-app-container .overview .wrapper section .item.multi img{height:2.5rem;aspect-ratio:1/1;border-radius:8px;object-fit:cover;object-position:center}.wallet-app-container .overview .wrapper section .item.multi .image{display:flex;height:2.5rem;aspect-ratio:1/1;border-radius:50%;justify-content:center;align-items:center;font-size:18px;font-weight:400;background-position:center;background-size:cover;background-repeat:no-repeat;background:linear-gradient(180deg,#e3e3e3,#999999);color:#fff;box-shadow:inset 0 4px 15px #00000040}.wallet-app-container .overview .wrapper section .item.multi .info{display:flex;flex-direction:row;align-items:center;justify-content:space-between;width:100%}.wallet-app-container .overview .wrapper section .item.multi .info .transaction-details{display:flex;flex-direction:column}.wallet-app-container .overview .wrapper section .item.multi .info .transaction-details .title{font-size:17px;font-weight:600;color:var(--phone-text-primary)}.wallet-app-container .overview .wrapper section .item.multi .info .transaction-details .date{color:var(--phone-text-secondary);font-size:15px;font-weight:400}.wallet-app-container .overview .wrapper section .item.multi .info .amount{font-size:16px;font-weight:500;letter-spacing:.03rem;font-variant-numeric:tabular-nums}.wallet-app-container .overview .wrapper section .item.multi .info .amount.green{color:var(--phone-color-green)}.wallet-app-container .overview .wrapper section .item.multi .info .amount.red{color:var(--phone-color-red)}.wallet-app-container .overview .wrapper section .item.multi .info i{font-size:18px}.wallet-app-container .send-container{display:flex;flex-direction:column;align-items:center;gap:10rem;height:100%;width:100%;z-index:1;bottom:0;background-color:var(--app-bg)}.wallet-app-container .send-container .send-header{margin-top:4rem;width:88%;display:flex;flex-direction:row;align-items:center;justify-content:space-between}.wallet-app-container .send-container .send-header div{display:flex;align-items:center;font-size:18px;color:var(--phone-color-blue);cursor:pointer}.wallet-app-container .send-container .send-header div svg{font-size:24px}.wallet-app-container .send-container .send-header div.disabled{color:var(--phone-text-secondary);cursor:not-allowed}.wallet-app-container .send-container .send-wrapper{display:flex;flex-direction:column;align-items:center;width:90%}.wallet-app-container .send-container .send-wrapper .success{display:flex;flex-direction:column;align-items:center;gap:.5rem}.wallet-app-container .send-container .send-wrapper .success .title{color:var(--phone-text-primary);font-size:20px;font-weight:400}.wallet-app-container .send-container .send-wrapper .success .subtitle{color:var(--phone-text-secondary);font-size:15px;font-weight:400}.wallet-app-container .send-container .send-wrapper .success svg{font-size:80px;color:var(--phone-color-blue)}.wallet-app-container .send-container .send-wrapper .amount,.wallet-app-container .send-container .send-wrapper .user{display:flex;flex-direction:row;justify-content:center;gap:.25rem;width:100%}.wallet-app-container .send-container .send-wrapper .amount .symbol,.wallet-app-container .send-container .send-wrapper .user .symbol{font-size:35px;font-weight:700;text-align:center;color:var(--phone-text-primary)}.wallet-app-container .send-container .send-wrapper .amount input,.wallet-app-container .send-container .send-wrapper .user input{border:none;background:transparent;color:var(--phone-text-primary);font-size:65px;text-align:center;width:10rem}.wallet-app-container .send-container .send-wrapper .amount input.number_input,.wallet-app-container .send-container .send-wrapper .user input.number_input{width:20rem;font-size:40px}.wallet-app-container .send-container .send-wrapper .amount input::-webkit-outer-spin-button,.wallet-app-container .send-container .send-wrapper .amount input::-webkit-inner-spin-button,.wallet-app-container .send-container .send-wrapper .user input::-webkit-outer-spin-button,.wallet-app-container .send-container .send-wrapper .user input::-webkit-inner-spin-button{-webkit-appearance:none}.wallet-app-container .send-container .send-wrapper .amount input:active,.wallet-app-container .send-container .send-wrapper .amount input:focus,.wallet-app-container .send-container .send-wrapper .user input:active,.wallet-app-container .send-container .send-wrapper .user input:focus{outline:none}.wallet-app-container .send-container .send-wrapper .button{margin-top:1.25rem;margin-bottom:1rem;padding:.4rem .8rem;border-radius:20px;font-size:16px;background-color:var(--phone-color-highlight3);color:var(--phone-text-primary);cursor:pointer;transition:all .2s ease-in-out}.wallet-app-container .send-container .send-wrapper .button:hover{filter:brightness(.9)}.wallet-app-container .send-container .send-wrapper .description{margin-top:1rem;font-size:16px;color:var(--phone-text-secondary);text-align:center}.wallet-app-container .send-container .send-wrapper .error{margin-top:1rem;font-size:16px;color:var(--phone-color-red)}.wallet-app-container .send-container .numpad-wrapper{width:90%;position:absolute;bottom:3rem}
