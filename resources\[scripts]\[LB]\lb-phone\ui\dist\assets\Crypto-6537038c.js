import{r as m,j as v,a,O as I,m as N,u as f,G as B,bO as F,b as g,s as Y,q as _,a0 as k,ad as b,v as A,aa as $,b0 as D,F as j,L as t,c6 as M,c7 as H,x as z,c8 as q,c9 as G,a5 as X,C as T,f as W,P as w,A as V}from"./index-a04bc7c5.js";function J({children:e,text:c}){const[h,S]=m.useState(!1);return v("div",{className:"tooltip-wrapper",onMouseEnter:()=>S(!0),onMouseLeave:()=>S(!1),children:[e,a(I,{children:h&&a(N.div,{initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},transition:{duration:.3,ease:"easeInOut"},className:"tooltip top",children:a("div",{dangerouslySetInnerHTML:{__html:c}})})})]})}const U=m.createContext(null);function te(){var d;const e=f(g),c=f(w.Settings),h=f(V),[S,O]=m.useState(0),L=f(w.Styles.TextColor),[R,n]=m.useState([]),[l,y]=m.useState(""),[p,o]=m.useState("0"),[P,u]=m.useState(c==null?void 0:c.streamerMode);m.useEffect(()=>{B("Crypto")&&(F()&&fetch(`https://api.coingecko.com/api/v3/coins/markets?vs_currency=usd&sparkline=true&order=market_cap_desc&per_page=100&page=1&ids=${g.value.Crypto.Coins.join(",")}`).then(i=>i.json()).then(i=>{n(i.map(r=>{var C;return{id:r.id,name:r.name,symbol:r.symbol,image:r.image,current_price:r.current_price,prices:(C=r.sparkline_in_7d)==null?void 0:C.price,change_24h:r.price_change_percentage_24h}}))}).catch(i=>Y("error",i)),_("Crypto",{action:"get"}).then(i=>{if(!i)return Y("warning","No data received from Crypto NUI event");n(i)}))},[h==null?void 0:h.active]),k("crypto:updateCoins",i=>{if(!i)return Y("warning","No data received from the update Crypto NUI event");n(i)}),m.useEffect(()=>O(R.reduce((i,r)=>i+(r.owned??0)*r.current_price,0)),[R]),m.useEffect(()=>{o(P?b("∗".repeat(A(S,2).toString().length)):b(A(S,2).toString()))},[S,P]);const s=i=>{let r={0:50,10:45,12:40,14:35},C=0;for(let E=0;E<Object.keys(r).length;E++)i.length>=parseInt(Object.keys(r)[E])&&(C=r[Object.keys(r)[E]]);return C};return a("div",{className:"crypto-container",children:$()?v(j,{children:[v("div",{className:"crypto-header",children:[a("img",{src:"./assets/img/icons/apps/Cryptopng.png",alt:"btc"}),t("APPS.CRYPTO.TITLE")]}),a(U.Provider,{value:{Coins:[R,n]},children:v("div",{className:"crypto-wrapper",children:[v("div",{className:"balance",children:[v("div",{className:"title",children:[t("APPS.CRYPTO.BALANCE"),a("div",{className:"icon",onClick:()=>u(!P),children:P?a(M,{}):a(H,{})})]}),a("div",{className:"amount",style:{fontSize:s(p.toString())},onClick:()=>u(!P),children:(d=e==null?void 0:e.CurrencyFormat)==null?void 0:d.replace("%s",p.toString())})]}),a("div",{className:"search",children:a(z,{placeholder:"Search Cryptocurrency",onChange:i=>y(i.target.value)})}),a(K,{search:l}),a("div",{className:"credits",children:"Powered by CoinGecko"})]})})]}):a("div",{className:"loading",children:a(D,{size:40,lineWeight:5,speed:2,color:L})})})}const K=({search:e})=>{const[c,h]=m.useState("balance"),[S,O]=m.useState(!0),L=["Name","Graph","Balance"],[R]=m.useContext(U).Coins;return a("div",{className:"holdings",children:v("div",{className:"items",children:[a("div",{className:"item filters",children:L.map((n,l)=>v("div",{className:"filter",onClick:()=>{if(h(n==null?void 0:n.toLowerCase()),c===(n==null?void 0:n.toLowerCase()))return O(!S);O(!0)},children:[n,(n==null?void 0:n.toLowerCase())===c&&S?a(q,{}):a(G,{})]},l))}),R.sort((n,l)=>c==="name"?S?n.name.localeCompare(l.name):l.name.localeCompare(n.name):c==="graph"?S?n.change_24h-l.change_24h:l.change_24h-n.change_24h:c==="balance"?S?(l.owned??0)*l.current_price-(n.owned??0)*n.current_price:(n.owned??0)*n.current_price-(l.owned??0)*l.current_price:0).filter(n=>{var l,y;return((l=n.name)==null?void 0:l.toLowerCase().includes(e.toLowerCase()))||((y=n.symbol)==null?void 0:y.toLowerCase().includes(e.toLowerCase()))}).map((n,l)=>a(Q,{data:n},l))]})})},Q=({data:e})=>{const[c,h]=m.useState(!1),[S,O]=m.useState(""),[L,R]=m.useContext(U).Coins,n=(p,o)=>{var P,u;if(o>((u=(P=g.value)==null?void 0:P.CryptoLimit)==null?void 0:u.Buy))return Y("warning","Amount is too high, cancelling request (MAX 1,000,000)");o>1?_("Crypto",{action:"buy",coin:p,amount:o}).then(s=>{if(s!=null&&s.success)return R(d=>d.map(i=>i.id===p?{...i,owned:(i.owned??0)+o/i.current_price,value:(i.value??0)+o,invested:(i.invested??0)+o}:i));s!=null&&s.msg&&setTimeout(()=>{T.PopUp.set({title:t("APPS.CRYPTO.ERROR"),description:t(`APPS.CRYPTO.${s.msg}`),buttons:[{title:t("APPS.CRYPTO.CLOSE")}]})},250)}):setTimeout(()=>{T.PopUp.set({title:t("APPS.CRYPTO.ERROR"),description:t("APPS.CRYPTO.ATLEAST_ONE_COIN").format({action:t("APPS.CRYPTO.BUY").toLowerCase(),currency:g.value.CurrencyFormat.replace("%s","")}),buttons:[{title:t("APPS.CRYPTO.CLOSE")}]})},250)},l=(p,o,P)=>{var u,s;if(o>((s=(u=g.value)==null?void 0:u.CryptoLimit)==null?void 0:s.Sell))return Y("warning","Amount is too high, cancelling request (MAX 1,000,000)");P>0?_("Crypto",{action:"sell",coin:p,amount:P}).then(d=>{if(d!=null&&d.success)return R(i=>i.map(r=>r.id===p?{...r,owned:(r.owned??0)-o/r.current_price,value:(r.value??0)+o,invested:(r.invested??0)-o}:r));d!=null&&d.msg&&setTimeout(()=>{T.PopUp.set({title:t("APPS.CRYPTO.ERROR"),description:t(`APPS.CRYPTO.${d.msg}`),buttons:[{title:t("APPS.CRYPTO.CLOSE")}]})},250)}):setTimeout(()=>{T.PopUp.set({title:t("APPS.CRYPTO.ERROR"),description:t("APPS.CRYPTO.ATLEAST_ONE_COIN").format({action:t("APPS.CRYPTO.SELL").toLowerCase(),currency:g.value.CurrencyFormat.replace("%s","")}),buttons:[{title:t("APPS.CRYPTO.CLOSE")}]})},250)},y=(p,o,P,u)=>{P>0?_("Crypto",{action:"transfer",coin:p,amount:P,number:u}).then(s=>{s!=null&&s.success&&setTimeout(()=>{T.PopUp.set({title:t("APPS.CRYPTO.SUCCESSFULLY_TRANSFERED_POPUP.TITLE"),description:t("APPS.CRYPTO.SUCCESSFULLY_TRANSFERED_POPUP.DESCRIPTION").format({amount:o,coin:e.symbol,number:W(u)}),buttons:[{title:t("APPS.CRYPTO.CLOSE")}]}),R(d=>d.map(i=>i.id===p?{...i,owned:(i.owned??0)-o/i.current_price,value:(i.value??0)+o,invested:(i.invested??0)-o}:i))},250),s!=null&&s.msg&&setTimeout(()=>{T.PopUp.set({title:t("APPS.CRYPTO.ERROR"),description:t(`APPS.CRYPTO.${s.msg}`),buttons:[{title:t("APPS.CRYPTO.CLOSE")}]})},250)}):setTimeout(()=>{T.PopUp.set({title:t("APPS.CRYPTO.ERROR"),description:t("APPS.CRYPTO.ATLEAST_ONE_COIN").format({action:t("APPS.CRYPTO.TRANSFER").toLowerCase(),currency:g.value.CurrencyFormat.replace("%s","")}),buttons:[{title:t("APPS.CRYPTO.CLOSE")}]})},250)};return e.symbol=e.symbol.toUpperCase(),e.owned=e.owned??0,e.value=e.current_price*(e.owned??0),v("div",{className:"item",onClick:()=>h(!c),children:[v("div",{className:"top",children:[v("div",{className:"info",children:[a(X,{src:e.image,className:"icon",alt:"Icon"}),v("div",{className:"coin",children:[a("div",{className:"name",children:e.symbol}),a("div",{className:"symbol",children:e.name})]})]}),a(Z,{data:e}),v("div",{className:"amount",children:[a("div",{className:"value",children:b(A(e.owned,4))}),v("div",{className:"asset",children:["$",b(A(e.value,2))]})]})]}),a(I,{children:c&&v(N.div,{initial:{height:0},animate:{height:"auto"},exit:{height:0},className:"bottom",children:[a(N.div,{initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},transition:{duration:.1},className:"button fill",onClick:p=>{var o,P,u,s,d,i,r;p.stopPropagation(),T.PopUp.set({title:t("APPS.CRYPTO.BUY_POPUP.TITLE").format({coin:e.symbol}),description:t("APPS.CRYPTO.BUY_POPUP.DESCRIPTION").format({coin:e.symbol}),input:{placeholder:t("APPS.CRYPTO.BUY_POPUP.PLACEHOLDER"),type:"number",value:"0",max:((u=(P=(o=g.value)==null?void 0:o.CryptoLimit)==null?void 0:P.Buy)==null?void 0:u.toString())??"1000000",maxLength:((r=(i=(d=(s=g.value)==null?void 0:s.CryptoLimit)==null?void 0:d.Buy)==null?void 0:i.toString())==null?void 0:r.length)+2,onChange:C=>O(C)},buttons:[{title:t("APPS.CRYPTO.BUY_POPUP.CANCEL")},{title:t("APPS.CRYPTO.BUY_POPUP.PROCEED"),cb:()=>{O(C=>(n(e.id,parseFloat(C)),C))}}]})},children:t("APPS.CRYPTO.BUY")}),a(N.div,{initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},transition:{duration:.1},className:"button",onClick:p=>{var o,P,u,s,d,i,r;p.stopPropagation(),T.PopUp.set({title:t("APPS.CRYPTO.SELL_POPUP.TITLE").format({coin:e.symbol}),description:t("APPS.CRYPTO.SELL_POPUP.DESCRIPTION").format({coin:e.symbol}),input:{placeholder:t("APPS.CRYPTO.BUY_POPUP.PLACEHOLDER"),type:"number",value:"0",max:((u=(P=(o=g.value)==null?void 0:o.CryptoLimit)==null?void 0:P.Sell)==null?void 0:u.toString())??"1000000",maxLength:((r=(i=(d=(s=g.value)==null?void 0:s.CryptoLimit)==null?void 0:d.Sell)==null?void 0:i.toString())==null?void 0:r.length)+2,onChange:C=>O(C)},buttons:[{title:t("APPS.CRYPTO.BUY_POPUP.CANCEL")},{title:t("APPS.CRYPTO.BUY_POPUP.PROCEED"),cb:()=>{O(C=>{let E=parseFloat(C),x=E/e.current_price;return l(e.id,E,x),C})}}]})},children:t("APPS.CRYPTO.SELL")}),a(N.div,{initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},transition:{duration:.1},className:"button blue",onClick:p=>{p.stopPropagation(),T.PopUp.set({title:t("APPS.CRYPTO.TRANSFER_POPUP.TITLE").format({coin:e.symbol}),description:t("APPS.CRYPTO.TRANSFER_POPUP.DESCRIPTION").format({coin:e.symbol}),input:{placeholder:t("APPS.CRYPTO.BUY_POPUP.PLACEHOLDER"),type:"number",value:"0",onChange:o=>O(o)},buttons:[{title:t("APPS.CRYPTO.BUY_POPUP.CANCEL")},{title:t("APPS.CRYPTO.TRANSFER_POPUP.NEXT"),cb:()=>{O(o=>{let P=parseFloat(o),u=P/e.current_price;return T.ContactSelector.set({onSelect:s=>{if(!s)return Y("warning","No contact selected? Cancelling request");y(e.id,P,u,s.number)}}),o})}}]})},children:t("APPS.CRYPTO.TRANSFER")}),a(N.div,{initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},transition:{duration:.1},className:"button red",onClick:p=>{p.stopPropagation(),T.PopUp.set({title:t("APPS.CRYPTO.SELL_ALL_POPUP.TITLE").format({coin:e.symbol}),description:t("APPS.CRYPTO.SELL_ALL_POPUP.DESCRIPTION").format({coin:e.symbol,amount:e.owned*e.current_price}),buttons:[{title:t("APPS.CRYPTO.BUY_POPUP.CANCEL")},{title:t("APPS.CRYPTO.BUY_POPUP.PROCEED"),color:"red",cb:()=>{let o=e.owned*e.current_price;l(e.id,o,e.owned)}}]})},children:t("APPS.CRYPTO.SELL_ALL")})]})})]})},Z=e=>{if(!(e!=null&&e.data.prices))return null;let c=e.data.prices;const h=m.useRef(null),[S,O]=m.useState(null),[L,R]=m.useState(null);return c=c.slice(0,24),m.useEffect(()=>{if(!(h!=null&&h.current))return;const n=h.current;n.width=150,n.height=100;const l=n.getContext("2d");l.lineWidth=3;const y=Math.min(...c),o=Math.max(...c)-y,P=n.height-10;let u=0,s=n.height-5-(c[0]-y)/o*P;l.beginPath(),l.moveTo(u,s);const d=c[c.length-1]-c[0]>0?"green":"red";l.strokeStyle=d;for(let C=1;C<c.length;C++)u+=n.width/(c.length-1),s=n.height-5-(c[C]-y)/o*P,l.lineTo(u,s);l.stroke();const i=(c[c.length-1]-c[0])/c[0]*100;i>0?O(`<div style="color: var(--phone-color-green)">+${A(i,2)}%</div>`):i<0?O(`<div style="color: var(--phone-color-red)">${A(i,2)}%</div>`):O(`<div>${A(i,2)}%</div>`);let r=(e.data.value-e.data.invested)/e.data.invested*100;r>0?R(`<div style="color: var(--phone-color-green)">+${A(r,2)}%</div>`):r<0&&R(`<div style="color: var(--phone-color-red)">${A(r,2)}%</div>`)},[h,c]),a(J,{text:`$${A(e.data.current_price,2)} ${S??""} ${L??""}`,children:a("canvas",{ref:h})})};export{te as default};
