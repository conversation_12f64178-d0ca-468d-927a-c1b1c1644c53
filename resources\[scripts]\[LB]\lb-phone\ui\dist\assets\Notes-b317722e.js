import{u as T,r as f,t as r,s as l,q as u,a as t,m as p,j as c,S as v,L as o,x as D,N as P,ax as I,ai as k,I as b,d as x,T as F,C as A,n as _,J as O}from"./index-a04bc7c5.js";import{T as M}from"./Textarea-63971279.js";const U=[{id:"123",title:"Shopping List",content:"Milk, eggs, bread, butter",timestamp:Date.now()-1e3*60*60*2},{id:"1414",title:"To Do List",content:`- Finish the UI
- Implement the backend
- Deploy the app`,timestamp:Date.now()-1e3*60*60*5},{id:"51515",title:"another list",content:`this is yet another list, it functions as a test for the notes app.

You can add new notes, edit them, and delete them.`,timestamp:Date.now()-1e3*60*60*24*2}];const N=O([]),d=O(null);function j(){const s=T(N),i=T(d),[m,g]=f.useState("");f.useEffect(()=>{if(r.APPS.NOTES.notes.value&&r.APPS.NOTES.notes.value.length>0){l("info","Using cached notes, not fetching",r.APPS.NOTES.notes.value),N.set(r.APPS.NOTES.notes.value);return}l("info","Fetching notes"),u("Notes",{action:"fetch"},U).then(e=>{if(!e)return l("warning","Failed to fetch notes, server returned false");l("info","Fetched notes",e),N.set(e),r.APPS.NOTES.notes.set(e)})},[]);const C=()=>{let e=Math.floor(Date.now()),n={title:o("APPS.NOTES.NEW_NOTE"),content:"",timestamp:e,id:null};u("Notes",{action:"create",data:n},(Math.random()*1e6).toString()).then(a=>{n.id=a,d.set(n),N.set([...s,n]),r.APPS.NOTES.notes.set([...s,n])})},L=e=>{let n=Math.floor(Date.now()),a=[...s],h=a.findIndex(S=>S.id===e.id),E=a[h];if(E.title===e.title&&E.content===e.content){l("info","Note is the same, not saving"),d.set(null);return}a[h]={...e,timestamp:n},u("Notes",{action:"save",data:a[h]},!0).then(S=>{if(!S)return l("warning","Failed to save note, server returned false");l("info","Saved note"),d.set(null),N.set(a),r.APPS.NOTES.notes.set(a)})},w=e=>{u("Notes",{action:"remove",id:e},!0).then(n=>{if(!n)return l("warning","Failed to remove note, server returned false");let a=s==null?void 0:s.filter(h=>h.id!==e);d.set(null),N.set(a),r.APPS.NOTES.notes.set(a),l("info","Removed note")})};return t(p.div,{className:"notes-container",children:i?c(p.div,{...v("right","note",.2),className:"notes-wrapper note",children:[c("div",{className:"top",children:[c("div",{className:"back",onClick:()=>L(i),children:[t(b,{}),o("APPS.NOTES.TITLE")]}),t(x,{className:"title",defaultValue:i.title,onChange:e=>d.patch({title:e.target.value,timestamp:Math.floor(Date.now())})}),t("div",{})]}),t(M,{onChange:e=>d.patch({content:e.target.value}),defaultValue:i.content}),c("div",{className:"notes-bottom",children:[t(F,{onClick:()=>A.PopUp.set({title:o("APPS.NOTES.DELETE_TITLE").format({title:i.title}),description:o("APPS.NOTES.DELETE_TEXT").format({title:i.title}),buttons:[{title:o("APPS.NOTES.BUTTON_CANCEL")},{title:o("APPS.NOTES.BUTTON_DELETE"),color:"red",cb:()=>w(i.id)}]})}),t("div",{className:"amount",children:o("APPS.NOTES.LAST_EDITED").format({date:P(i.timestamp)})}),t(_,{onClick:()=>A.Share.set({type:"note",data:i})})]})]}):c(p.div,{...v("left","notes",.2),className:"notes-wrapper ",children:[t("div",{className:"title",children:o("APPS.NOTES.TITLE")}),t(D,{placeholder:o("APPS.NOTES.SEARCH_PLACEHOLDER"),onChange:e=>g(e.target.value)}),t("div",{className:"notes-body",children:t("div",{className:"notes-list",children:s==null?void 0:s.filter(e=>{var n,a;return((n=e.title)==null?void 0:n.toLowerCase().includes(m==null?void 0:m.toLowerCase()))||((a=e.content)==null?void 0:a.toLowerCase().includes(m==null?void 0:m.toLowerCase()))}).sort((e,n)=>n.timestamp-e.timestamp).map((e,n)=>c("div",{className:"note-item",onClick:()=>d.set(e),children:[t("div",{className:"note-title",children:e.title}),c("div",{className:"note-details",children:[t("div",{className:"date",children:P(e.timestamp)}),t("div",{className:"content",children:e.content.length>30?e.content.slice(0,30)+"...":e.content})]})]},n))})}),c("div",{className:"notes-bottom",children:[t(I,{className:"hidden"}),t("div",{className:"amount",children:o("APPS.NOTES.FOOTER").format({amount:(s==null?void 0:s.length)??0})}),t(k,{onClick:C})]})]})})}export{j as default};
