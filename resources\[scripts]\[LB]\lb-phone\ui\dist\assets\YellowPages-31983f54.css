:root{--phone-color-primary: rgb(255, 255, 255);--phone-color-opacity: rgba(242, 242, 242, .4);--phone-color-opacity2: rgb(30, 30, 30, .5);--phone-color-highlight: rgb(250, 250, 250);--phone-color-highlight2: rgb(240, 240, 240);--phone-color-highlight3: rgb(220, 220, 220);--phone-highlight-opacity15: rgba(145, 145, 145, .15);--phone-highlight-opacity35: rgba(145, 145, 145, .35);--phone-highlight-opacity45: rgba(145, 145, 145, .45);--phone-highlight-opacity55: rgba(145, 145, 145, .55);--phone-color-input: rgba(241, 241, 241, .656);--phone-text-primary: rgb(0, 0, 0);--phone-text-secondary: rgb(142, 142, 147);--phone-color-hover: rgb(240, 240, 240);--phone-color-border: rgba(200, 200, 200, .4);--phone-color-grey: #8e8e93;--phone-color-blue: #0a84ff;--phone-color-green: #32d74b;--phone-color-green-secondary: #092911;--phone-color-red: #ff3b30;--phone-color-orange: rgb(255, 157, 10);--phone-color-yellow: #cca250;--phone-color-pink: #ff3b30;--instagram-primary: #ffffff;--instagram-blue: rgb(0, 149, 246);--instagram-red: rgb(237, 73, 86);--instagram-primary-text: rgb(38, 38, 38);--instagram-secondary-text: rgb(142, 142, 142);--instagram-stroke: rgb(219, 219, 219);--instagram-border: rgb(219, 219, 219);--instagram-highlight: rgb(239, 239, 239);--tinder-color-pink: #ff4573;--tinder-color-orange: #ff5f65;--tinder-color-mix: #f5547c;--twitter-primary: #f5f8fa;--twitter-secondary: #14171a;--twitter-background-highlight: rgb(239, 243, 244);--twitter-primary-text: #14171a;--twitter-secondary-text: #657786;--twitter-alt-text: #657786;--twitter-border: #bdc5cd75;--twitter-border-secondary: #1d9bf0;--twitter-highlight: #1d9bf0;--twitter-hover: rgba(15, 20, 25, .1);--twitter-action: #14171a;--twitter-blue: #1d9bf0;--tiktok-primary: #ffffff;--tiktok-secondary: #000000;--tiktok-text-primary: #000000;--tiktok-text-secondary: #86878b;--tiktok-color-border: #d0d1d3;--tiktok-color-pink: #fe2c55;--tiktok-color-aqua: #00f2ea;--tiktok-color-yellow: #f8cd14;--tiktok-color-blue: #479fc5;--tiktok-color-unread: rgba(254, 44, 86, .2);--crypto-color-primary: rgb(255, 255, 255);--browser-primary: rgb(245, 245, 245);--browser-secondary: rgba(153, 153, 153, .15);--browser-gradient: linear-gradient(230deg, #f4d6ff, #c5f1ff);--browser-footer: rgba(255, 255, 255, .75);--browser-border: rgba(102, 102, 102, .75);--browser-text-secondary: #696969;--controlcentre-opacity: rgba(255, 255, 255, .15);--controlcentre-opacity2: rgba(255, 255, 255, .2);--controlcentre-active: rgba(255, 255, 255, .5);--notification-primary: rgba(215, 215, 215, .5);--notification-secondary: rgba(215, 215, 215, .1);--lockscreeneditor-background: rgba(255, 255, 255, .75);--lockscreeneditor-secondary: #d9d9d9;--app-bg: #ececec;--app-bg2: #ffffff;--app-secondary: #ffffff;--app-secondary2: #ececec;--app-highlight: #cccccc;--app-highlight2: #999999;--app-highlight3: #ffffff;--app-border: #666666;--app-slider: #cccccc;--app-slider-active: #333333;--app-button: #ffffff;--components-bg: #eeeeee;--components-secondary: #ffffff;--components-highlight: #cccccc}[data-theme=dark]{--phone-color-primary: #000000;--phone-color-opacity: rgb(30, 30, 30, .5);--phone-color-opacity2: rgba(242, 242, 242, .4);--phone-color-highlight: rgb(15, 15, 15);--phone-color-highlight2: rgb(20, 20, 20);--phone-color-highlight3: rgb(25, 25, 25);--phone-highlight-opacity35: rgba(145, 145, 145, .35);--phone-highlight-opacity45: rgba(50, 50, 50, .6);--phone-highlight-opacity55: rgb(60, 60, 60, .8);--phone-color-input: rgba(60, 60, 67, .6);--phone-text-primary: #f2f2f7;--phone-text-secondary: #6f6f6f;--phone-color-grey: #636366;--phone-color-hover: rgb(30, 30, 30);--phone-color-border: rgba(150, 150, 150, .2);--phone-color-blue: #076bcf;--instagram-primary: #000000;--instagram-blue: rgb(0, 149, 246);--instagram-red: rgb(237, 73, 86);--instagram-primary-text: rgb(250, 250, 250);--instagram-secondary-text: rgb(142, 142, 142);--instagram-stroke: rgb(219, 219, 219);--instagram-border: rgb(54, 54, 54);--instagram-highlight: rgb(38, 38, 38);--twitter-primary: #000000;--twitter-secondary: #f5f8fa;--twitter-background-highlight: rgb(20, 20, 20);--twitter-primary-text: #f5f8fa;--twitter-secondary-text: #aab8c2;--twitter-alt-text: #657786;--twitter-border: #38444d;--twitter-border-secondary: #38444d;--twitter-hover: rgba(150, 150, 150, .1);--twitter-highlight: #dcdcdc;--twitter-action: #1d9bf0;--twitter-blue: #1d9bf0;--crypto-color-primary: rgb(24, 26, 32);--tiktok-text-primary: #f2f2f7;--tiktok-text-secondary: #6f6f6f;--tiktok-color-border: #96969633;--browser-primary: rgb(15, 15, 15);--browser-secondary: rgba(153, 153, 153, .15);--browser-gradient: linear-gradient(230deg, #453b48, #2f393d);--browser-footer: rgba(51, 51, 51, .75);--browser-border: rgba(102, 102, 102, .75);--browser-text-secondary: #999999;--controlcentre-opacity: rgba(0, 0, 0, .15);--controlcentre-opacity2: rgba(0, 0, 0, .2);--controlcentre-active: rgba(0, 0, 0, .5);--notification-primary: rgba(0, 0, 0, .1);--notification-secondary: rgba(0, 0, 0, .12);--lockscreeneditor-background: rgba(0, 0, 0, .8);--lockscreeneditor-secondary: #333333;--app-bg: #000000;--app-bg2: #000000;--app-secondary: #141414;--app-secondary2: #141414;--app-highlight: #cccccc;--app-highlight2: #696969;--app-highlight3: #212121;--app-border: #cccccc;--app-slider: #999999;--app-slider-active: #ffffff;--app-button: #333333;--components-bg: #000000;--components-secondary: #141414;--components-highlight: #696969}@keyframes zoomIn{0%{transform:scale(.5)}to{transform:scale(1)}}@keyframes zoomOut{0%{transform:scale(1.5)}to{transform:scale(1)}}@keyframes slideDown{0%{transform:translateY(-20%)}to{transform:translateY(0)}}@keyframes slideUp{0%{transform:translateY(40%)}to{transform:translateY(0)}}@keyframes slideRight{0%{transform:translate(-10%)}to{transform:translate(0)}}@keyframes slideLeft{0%{transform:translate(10%)}to{transform:translate(0)}}@keyframes appJiggle{0%{transform:rotate(-1deg);animation-timing-function:ease-in}50%{transform:rotate(1.5deg);animation-timing-function:ease-out}}@keyframes appJiggle2{0%{transform:rotate(1deg);animation-timing-function:ease-in}50%{transform:rotate(-1.5deg);animation-timing-function:ease-out}}@keyframes widgetJiggle{0%{transform:rotate(-.5deg);animation-timing-function:ease-in}50%{transform:rotate(.5deg);animation-timing-function:ease-out}}@keyframes widgetJiggle2{0%{transform:rotate(.5deg);animation-timing-function:ease-in}50%{transform:rotate(-.5deg);animation-timing-function:ease-out}}.pages-container{height:100%;width:100%;display:flex;flex-direction:column;align-items:center;position:relative;background-color:var(--app-bg)}.pages-container .pages-header{width:90%;padding-top:4rem;padding-bottom:1rem;display:flex;flex-direction:column;align-items:center;gap:1rem;box-sizing:border-box}.pages-container .pages-header .pages-header-top{display:flex;align-items:center;justify-content:space-between;width:100%}.pages-container .pages-header .pages-header-top .title{font-size:28px;font-weight:600;color:var(--phone-text-primary)}.pages-container .pages-header .pages-header-top svg{font-size:30px;color:var(--phone-color-blue);cursor:pointer}.pages-container .pages-header .pages-header-top .back{display:flex;flex-direction:row;align-items:center;gap:.1rem;color:var(--phone-color-blue);font-size:17px;cursor:pointer}.pages-container .pages-header .pages-header-top .back svg{font-size:22px}.pages-container .pages-header .searchbox{width:100%;box-sizing:border-box;background-color:var(--app-secondary);padding:.4rem 1rem;border-radius:12px;margin-bottom:.5rem}.pages-container .pages-header .searchbox input{width:95%}.pages-container .pages-wrapper{display:flex;flex-direction:column;align-items:center;width:100%;height:100%;position:relative}.pages-container .pages-wrapper .posts{width:100%;display:flex;flex-direction:column;align-items:center;gap:1rem;overflow-y:auto;overflow-x:hidden;box-sizing:border-box;padding-bottom:15rem}.pages-container .pages-wrapper .posts::-webkit-scrollbar{display:none}.pages-container .pages-wrapper .posts .post-item{display:flex;flex-direction:column;align-items:center;gap:1rem;width:90%;padding:1rem;border-radius:12px;background-color:var(--app-secondary);position:relative;box-sizing:border-box}.pages-container .pages-wrapper .posts .post-item .post-info{width:100%;display:flex;flex-direction:column;gap:.25rem}.pages-container .pages-wrapper .posts .post-item .post-info .post-header{display:flex;align-items:center;justify-content:space-between}.pages-container .pages-wrapper .posts .post-item .post-info .post-header .title{font-size:18px;font-weight:500;color:var(--phone-text-primary)}.pages-container .pages-wrapper .posts .post-item .post-info .post-header .date{font-size:14px;font-weight:300;color:var(--phone-text-secondary)}.pages-container .pages-wrapper .posts .post-item .post-info .post-content{display:flex;justify-content:space-between;align-items:flex-end;gap:1rem;margin-top:.5rem}.pages-container .pages-wrapper .posts .post-item .post-info .post-content .post-text{display:flex;flex-direction:column;gap:.5rem;max-width:65%}.pages-container .pages-wrapper .posts .post-item .post-info .post-content .post-text .description{width:100%;font-size:15px;color:var(--phone-text-secondary)}.pages-container .pages-wrapper .posts .post-item .post-info .post-content .post-text .price{font-size:18px;font-weight:500;color:var(--phone-text-primary)}.pages-container .pages-wrapper .posts .post-item .post-info .post-content .attachment{width:6.5rem;height:6.5rem}.pages-container .pages-wrapper .posts .post-item .post-info .post-content .attachment img{width:100%;height:100%;object-fit:cover;aspect-ratio:1/1;border-radius:6px}.pages-container .pages-wrapper .posts .post-item .post-info .post-footer{display:flex;align-items:flex-end;justify-content:flex-end;gap:.5rem;margin-top:.5rem}.pages-container .pages-wrapper .posts .post-item .post-info .post-footer svg{display:flex;align-items:center;justify-content:center;width:1.25rem;height:1.25rem;padding:.35rem;border-radius:8px;background-color:var(--app-bg);color:var(--phone-text-secondary);font-size:12px;cursor:pointer;transition:all .2s ease-in-out}.pages-container .pages-wrapper .posts .post-item .post-info .post-footer svg.red{background-color:var(--phone-color-red);color:#fff}.pages-container .pages-wrapper .posts .post-item .post-info .post-footer svg.blue{background-color:var(--phone-color-blue);color:#fff}.pages-container .pages-wrapper .posts .post-item .post-info .post-footer svg.green{background-color:var(--phone-color-green);color:#fff}.pages-container .new-post-container{position:absolute;bottom:0;z-index:1;height:94%;width:100%;background-color:var(--app-secondary);display:flex;flex-direction:column;align-items:center;gap:1rem;border-top-left-radius:1.5rem;border-top-right-radius:1.5rem}.pages-container .new-post-container .new-post-header{display:flex;align-items:center;justify-content:space-between;width:100%;padding:1rem 1.5rem;box-sizing:border-box}.pages-container .new-post-container .new-post-header>div{flex:1}.pages-container .new-post-container .new-post-header .post{color:var(--phone-color-blue);font-size:16px;cursor:pointer;text-align:right}.pages-container .new-post-container .new-post-header .close{display:flex;align-items:center;justify-content:center}.pages-container .new-post-container .new-post-header .close>div{width:4rem;height:.3rem;border-radius:5px;background-color:var(--components-highlight);cursor:pointer}.pages-container .new-post-container .new-post-body{width:100%;display:flex;flex-direction:column;align-items:center;gap:1rem;margin-right:1rem}.pages-container .new-post-container .new-post-body .item{display:flex;flex-direction:column;gap:.5rem;width:80%}.pages-container .new-post-container .new-post-body .item .title{font-size:16px;font-weight:500;color:var(--phone-text-primary)}.pages-container .new-post-container .new-post-body .item input,.pages-container .new-post-container .new-post-body .item textarea{width:100%;padding:.7rem 1rem;border:none;font-family:Roboto;font-size:16px;background-color:var(--app-bg);color:var(--phone-text-primary);resize:none;border-radius:16px}.pages-container .new-post-container .new-post-body .item input::-webkit-inner-spin-button,.pages-container .new-post-container .new-post-body .item input::-webkit-outer-spin-button,.pages-container .new-post-container .new-post-body .item textarea::-webkit-inner-spin-button,.pages-container .new-post-container .new-post-body .item textarea::-webkit-outer-spin-button{-webkit-appearance:none;margin:0}.pages-container .new-post-container .new-post-body .item input:focus,.pages-container .new-post-container .new-post-body .item textarea:focus{outline:none}.pages-container .new-post-container .new-post-body .item .images{width:100%;display:grid;grid-template-columns:repeat(3,1fr);gap:.5rem}.pages-container .new-post-container .new-post-body .item .images .image{height:6.6rem;width:6.6rem;aspect-ratio:1/1;display:flex;align-items:center;justify-content:center;align-self:center;justify-self:center;background-color:var(--app-bg);border:3px dashed var(--phone-color-border);border-radius:16px;font-size:55px;color:var(--phone-text-secondary);background-size:cover;background-position:center;cursor:pointer;transition:all .2s ease-in-out}.pages-container .new-post-container .new-post-body .item .images .image:hover{filter:brightness(.9)}.pages-container .new-post-container .new-post-body .button{margin-top:4rem;padding:.6rem 2rem;border-radius:7px;background-color:#0e4892;color:#fff;font-size:18px;font-weight:500;cursor:pointer;transition:all .2s ease-in-out}.pages-container .new-post-container .new-post-body .button:hover{filter:brightness(.8)}
